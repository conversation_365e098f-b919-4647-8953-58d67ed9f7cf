<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page for Playwright Workflow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="email"],
        textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .success-message {
            color: #4CAF50;
            font-weight: bold;
            margin-top: 10px;
            display: none;
        }
    </style>
</head>
<body>
    <h1>Playwright Workflow Test Page</h1>
    
    <div class="form-container">
        <div class="form-group">
            <label for="input1">Input Field 1:</label>
            <input type="text" id="input1" name="input1" placeholder="Enter text here...">
        </div>
        
        <div class="form-group">
            <label for="input2">Input Field 2 (Email):</label>
            <input type="email" id="input2" name="input2" placeholder="Enter email...">
        </div>
        
        <div class="form-group">
            <label for="textarea1">Text Area:</label>
            <textarea id="textarea1" name="textarea1" rows="4" placeholder="Enter multiline text..."></textarea>
        </div>
        
        <div class="form-group">
            <button id="submit-btn" type="button">Submit Form</button>
            <div id="success-message" class="success-message">
                Form submitted successfully!
            </div>
        </div>
    </div>

    <div class="dynamic-content">
        <h2>Dynamic Content</h2>
        <button id="load-content">Load Content</button>
        <div id="content-area" style="margin-top: 10px; min-height: 50px; border: 1px dashed #ccc; padding: 10px;">
            Content will appear here...
        </div>
    </div>

    <div class="modal" id="test-modal" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border: 1px solid #ccc; z-index: 1000;">
        <h3>Test Modal</h3>
        <p>This is a test modal dialog.</p>
        <button id="close-modal">Close</button>
    </div>
    <button id="open-modal">Open Modal</button>

    <script>
        // 提交按钮点击事件
        document.getElementById('submit-btn').addEventListener('click', function() {
            const successMessage = document.getElementById('success-message');
            successMessage.style.display = 'block';
            
            // 3秒后隐藏成功消息
            setTimeout(() => {
                successMessage.style.display = 'none';
            }, 3000);
        });
        
        // 加载内容按钮点击事件
        document.getElementById('load-content').addEventListener('click', function() {
            const contentArea = document.getElementById('content-area');
            contentArea.innerHTML = `
                <p>This content was loaded dynamically at ${new Date().toLocaleTimeString()}</p>
                <p>You can interact with this content after it's loaded.</p>
                <button id="content-button">Click Me!</button>
                <div id="button-message" style="margin-top: 10px; color: blue;"></div>
            `;
            
            // 添加动态按钮事件
            document.getElementById('content-button').addEventListener('click', function() {
                document.getElementById('button-message').textContent = 'Button was clicked!';
            });
        });
        
        // 模态框控制
        document.getElementById('open-modal').addEventListener('click', function() {
            document.getElementById('test-modal').style.display = 'block';
        });
        
        document.getElementById('close-modal').addEventListener('click', function() {
            document.getElementById('test-modal').style.display = 'none';
        });
    </script>
</body>
</html>
