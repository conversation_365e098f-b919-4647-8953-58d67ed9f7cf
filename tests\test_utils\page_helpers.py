"""
测试辅助函数 - 页面操作
"""
from typing import Dict, Any, Optional
from pathlib import Path
import json
import time


def wait_for_console_message(page, message: str, timeout: int = 10) -> bool:
    """等待控制台出现指定消息"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        messages = page.evaluate('''() => {
            return window.consoleMessages || [];
        }''')
        if any(message in msg for msg in messages):
            return True
        time.sleep(0.5)
    return False


def get_element_info(page, selector: str) -> Dict[str, Any]:
    """获取元素信息"""
    return page.evaluate(f'''(selector) => {{
        const el = document.querySelector(selector);
        if (!el) return {{ exists: false }};
        
        const rect = el.getBoundingClientRect();
        return {{
            exists: true,
            visible: !!(el.offsetWidth || el.offsetHeight || el.getClientRects().length),
            text: el.textContent?.trim() || '',
            tagName: el.tagName,
            id: el.id || '',
            class: el.className || '',
            disabled: el.disabled,
            hidden: el.hidden,
            style: window.getComputedStyle(el).visibility,
            rect: {{
                x: rect.x,
                y: rect.y,
                width: rect.width,
                height: rect.height
            }}
        }};
    }}''', selector)


def save_test_screenshot(page, filename: str, base_dir: Optional[Path] = None):
    """保存测试截图"""
    if base_dir is None:
        base_dir = Path(__file__).parent.parent / "screenshots"
    
    base_dir.mkdir(exist_ok=True)
    screenshot_path = base_dir / f"{filename}.png"
    
    try:
        screenshot = page.screenshot()
        with open(screenshot_path, "wb") as f:
            f.write(screenshot)
        return str(screenshot_path)
    except Exception as e:
        print(f"保存截图失败: {str(e)}")
        return None


def save_test_data(data: Any, filename: str, base_dir: Optional[Path] = None):
    """保存测试数据"""
    if base_dir is None:
        base_dir = Path(__file__).parent.parent / "test_output"
    
    base_dir.mkdir(exist_ok=True)
    file_path = base_dir / f"{filename}.json"
    
    try:
        with open(file_path, "w", encoding="utf-8") as f:
            if isinstance(data, (dict, list)):
                json.dump(data, f, ensure_ascii=False, indent=2)
            else:
                f.write(str(data))
        return str(file_path)
    except Exception as e:
        print(f"保存测试数据失败: {str(e)}")
        return None
