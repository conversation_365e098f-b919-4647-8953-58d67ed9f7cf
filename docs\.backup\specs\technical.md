# 技术规范

**最后更新**：2025-05-30

## 1. 数据格式规范

### 1.1 测试用例JSON格式

```json
{
  "name": "test_case_name",
  "description": "测试用例描述",
  "version": "1.0.0",
  "metadata": {
    "author": "作者",
    "createdAt": "2025-05-30T00:00:00+08:00",
    "lastModified": "2025-05-30T00:00:00+08:00"
  },
  "steps": [
    {
      "id": "step_1",
      "action": "navigate",
      "target": "https://example.com",
      "description": "访问示例网站",
      "parameters": {},
      "assertions": [
        {
          "type": "url_contains",
          "value": "example.com",
          "message": "URL应包含example.com"
        }
      ],
      "screenshot": true,
      "waitForSelector": "body",
      "timeout": 30000
    }
  ],
  "variables": {
    "baseUrl": "https://example.com"
  },
  "hooks": {
    "beforeAll": [],
    "afterAll": [],
    "beforeEach": [],
    "afterEach": []
  },
  "tags": ["smoke", "regression"]
}
```

### 1.2 工作流定义格式

```json
{
  "name": "user_registration_flow",
  "description": "用户注册工作流",
  "version": "1.0.0",
  "variables": {
    "username": "test_user_{{timestamp}}",
    "email": "test_{{timestamp}}@example.com"
  },
  "steps": [
    {
      "id": "navigate_to_register",
      "type": "navigate",
      "url": "{{baseUrl}}/register",
      "description": "导航到注册页面"
    },
    {
      "id": "fill_registration_form",
      "type": "form",
      "description": "填写注册表单",
      "fields": [
        {
          "selector": "#username",
          "action": "fill",
          "value": "{{username}}"
        },
        {
          "selector": "#email",
          "action": "fill",
          "value": "{{email}}"
        },
        {
          "selector": "#password",
          "action": "fill",
          "value": "P@ssw0rd123"
        }
      ]
    },
    {
      "id": "submit_registration",
      "type": "click",
      "selector": "button[type='submit']",
      "description": "提交注册表单",
      "waitForNavigation": true
    },
    {
      "id": "verify_registration_success",
      "type": "assert",
      "description": "验证注册成功",
      "assertions": [
        {
          "type": "url_contains",
          "value": "/dashboard",
          "message": "注册后应跳转到仪表盘"
        },
        {
          "type": "element_visible",
          "selector": ".welcome-message",
          "message": "应显示欢迎消息"
        }
      ]
    }
  ],
  "errorHandling": {
    "retry": 2,
    "fallbackSteps": [
      {
        "condition": "element_not_found",
        "action": "refresh_page",
        "description": "刷新页面并重试"
      }
    ]
  },
  "dependencies": [
    {
      "name": "test_data",
      "version": "^1.0.0"
    }
  ]
}
```

## 2. 接口规范

### 2.1 录制模块 (recorder.py)

```python
class Recorder:
    def __init__(self, output_file: str, headless: bool = False):
        """
        初始化录制器
        :param output_file: 输出文件路径
        :param headless: 是否使用无头模式
        """
        pass
    
    async def start(self) -> None:
        """开始录制"""
        pass
    
    async def stop(self) -> None:
        """停止录制并保存结果"""
        pass
    
    async def pause(self) -> None:
        """暂停录制"""
        pass
    
    async def resume(self) -> None:
        """恢复录制"""
        pass
    
    async def add_comment(self, comment: str) -> None:
        """添加注释到当前步骤
        :param comment: 注释内容
        """
        pass
```

### 2.2 执行模块 (runner.py)

```python
class TestRunner:
    def __init__(self, test_case_file: str, env: str = "dev"):
        """
        初始化测试运行器
        :param test_case_file: 测试用例文件路径
        :param env: 运行环境 (dev/staging/prod)
        """
        pass
    
    async def run(self, browser_type: str = "chromium", headless: bool = True) -> Dict:
        """
        执行测试用例
        :param browser_type: 浏览器类型 (chromium/firefox/webkit)
        :param headless: 是否使用无头模式
        :return: 测试结果
        """
        pass
    
    async def run_step(self, step: Dict) -> Dict:
        """
        执行单个测试步骤
        :param step: 测试步骤定义
        :return: 步骤执行结果
        """
        pass
    
    async def run_workflow(self, workflow_file: str) -> Dict:
        """
        执行工作流
        :param workflow_file: 工作流定义文件路径
        :return: 工作流执行结果
        """
        pass
```

## 3. 工作流引擎规范

### 3.1 工作流生命周期

1. **初始化**：加载工作流定义，解析变量和依赖
2. **验证**：验证工作流定义的有效性
3. **准备**：准备执行环境，加载依赖
4. **执行**：按顺序执行工作流步骤
5. **清理**：释放资源，生成报告

### 3.2 变量系统

- **作用域**：全局变量、工作流变量、步骤变量
- **变量解析**：支持模板字符串 `${variable}` 或 `{{variable}}`
- **变量来源**：
  - 工作流定义中的 `variables` 部分
  - 环境变量
  - 命令行参数
  - 上一步骤的输出
  - 外部数据源（如数据库、API）

### 3.3 错误处理

- **重试机制**：可配置的重试次数和间隔
- **回滚策略**：定义失败时的回滚步骤
- **错误通知**：支持邮件、Slack等通知方式
- **日志记录**：详细的执行日志，便于调试

## 4. 自我修复系统

### 4.1 元素定位策略

1. 主选择器（用户定义）
2. 备用选择器（自动生成）
3. 视觉匹配（基于图像识别）
4. DOM结构分析

### 4.2 自动修复流程

1. 元素定位失败
2. 尝试备用选择器
3. 分析DOM变化
4. 生成新的选择器
5. 验证并应用修复
6. 记录修复方案

## 5. 性能规范

### 5.1 执行超时

- 步骤默认超时：30秒
- 页面加载超时：60秒
- 元素查找超时：10秒
- 网络请求超时：30秒

### 5.2 资源限制

- 最大并发测试数：10
- 单次测试最大步骤数：1000
- 最大日志文件大小：100MB
- 最大内存使用：2GB

## 6. 安全规范

### 6.1 敏感数据处理

- 密码和密钥必须使用环境变量或密钥管理服务
- 测试数据中的敏感信息应使用占位符
- 日志中的敏感信息应进行脱敏处理

### 6.2 权限控制

- 最小权限原则
- 基于角色的访问控制 (RBAC)
- 操作审计日志

## 7. API 接口规范

### 7.1 RESTful API 基础

#### 基础URL
```
/api/v1/
```

#### 认证
- 使用 JWT 认证
- 请求头: `Authorization: Bearer <token>`

#### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-05-30T10:00:00+08:00"
}
```

### 7.2 测试用例管理

#### 获取测试用例列表
- **URL**: `/testcases`
- **Method**: GET
- **Query Parameters**:
  - `page`: 页码 (default: 1)
  - `pageSize`: 每页数量 (default: 20)
  - `tags`: 标签过滤，多个用逗号分隔

#### 创建测试用例
- **URL**: `/testcases`
- **Method**: POST
- **Request Body**:
  ```json
  {
    "name": "test_case_name",
    "description": "测试用例描述",
    "steps": [
      {
        "action": "navigate",
        "target": "https://example.com",
        "description": "访问示例网站"
      }
    ]
  }
  ```

### 7.3 工作流执行

#### 执行工作流
- **URL**: `/workflows/execute`
- **Method**: POST
- **Request Body**:
  ```json
  {
    "workflowId": "workflow_123",
    "environment": "staging",
    "variables": {
      "username": "testuser"
    }
  }
  ```
- **Response**:
  ```json
  {
    "executionId": "exec_123",
    "status": "pending",
    "startTime": "2025-05-30T10:00:00+08:00"
  }
  ```

## 8. 工作流数据格式更新

### 8.1 增强的工作流定义

```json
{
  "id": "workflow_123",
  "name": "用户注册流程",
  "version": "1.0.0",
  "description": "完整的用户注册流程",
  "variables": {
    "baseUrl": "https://example.com",
    "username": {
      "type": "string",
      "default": "testuser",
      "required": true
    },
    "password": {
      "type": "string",
      "sensitive": true,
      "required": true
    }
  },
  "steps": [
    {
      "id": "navigate_to_register",
      "action": "navigate",
      "target": "{{baseUrl}}/register",
      "description": "导航到注册页面",
      "timeout": 30000,
      "retry": 3,
      "onFailure": "abort"
    },
    {
      "id": "fill_registration_form",
      "action": "fillForm",
      "target": "form#registration",
      "parameters": {
        "username": "{{username}}",
        "email": "{{username}}@example.com",
        "password": "{{password}}",
        "confirmPassword": "{{password}}"
      },
      "description": "填写注册表单"
    },
    {
      "id": "submit_form",
      "action": "click",
      "target": "button[type='submit']",
      "description": "提交注册表单",
      "waitForNavigation": true
    }
  ],
  "hooks": {
    "beforeAll": [
      {
        "action": "executeScript",
        "script": "console.log('Starting workflow execution')"
      }
    ],
    "afterAll": [
      {
        "action": "executeScript",
        "script": "console.log('Workflow execution completed')"
      }
    ]
  },
  "metadata": {
    "author": "QA Team",
    "createdAt": "2025-05-30T00:00:00+08:00",
    "lastModified": "2025-05-30T00:00:00+08:00",
    "tags": ["regression", "critical"]
  }
}
```

### 8.2 条件执行

```json
{
  "steps": [
    {
      "id": "check_login_status",
      "action": "evaluate",
      "expression": "return localStorage.getItem('isLoggedIn') === 'true'",
      "storeResultAs": "isLoggedIn"
    },
    {
      "id": "login_if_needed",
      "action": "if",
      "condition": "{{!isLoggedIn}}",
      "then": [
        {
          "action": "navigate",
          "target": "{{baseUrl}}/login"
        },
        {
          "action": "fillForm",
          "target": "form#login",
          "parameters": {
            "username": "{{username}}",
            "password": "{{password}}"
          }
        },
        {
          "action": "click",
          "target": "button[type='submit']"
        }
      ]
    }
  ]
}
```

## 9. 扩展性规范

### 9.1 插件系统

#### 插件结构
```
plugins/
  my-plugin/
    __init__.py
    actions.py    # 自定义操作
    assertions.py # 自定义断言
    hooks.py      # 生命周期钩子
    config.yaml   # 插件配置
```

#### 插件注册
```python
# __init__.py
from . import actions, assertions, hooks

def register(plugin_manager):
    plugin_manager.register_action('my_action', actions.my_action)
    plugin_manager.register_assertion('my_assertion', assertions.my_assertion)
    plugin_manager.register_hook('before_step', hooks.before_step_hook)
```

### 9.2 事件系统

#### 核心事件
- `workflow_started`
- `workflow_completed`
- `step_started`
- `step_completed`
- `step_failed`
- `variable_updated`

#### 事件订阅示例
```python
def on_step_completed(event):
    print(f"Step {event.step_id} completed in {event.duration}ms")

event_bus.subscribe('step_completed', on_step_completed)
```

## 10. 性能优化指南

### 10.1 测试数据管理

#### 数据驱动测试
```yaml
test_data:
  - username: <EMAIL>
    password: pass123
    expected_result: success
  - username: <EMAIL>
    password: wrongpass
    expected_result: failure
```

#### 测试数据工厂
```python
from factory import Faker

class UserFactory(Factory):
    class Meta:
        model = dict
    
    username = Faker('user_name')
    email = Faker('email')
    password = Faker('password')
```

### 10.2 并行执行

#### 测试分片配置
```yaml
parallel:
  enabled: true
  workers: 4
  shard:
    strategy: by-module  # or by-class, by-method
```

## 11. 安全最佳实践

### 11.1 安全测试

#### OWASP Top 10 测试用例
- SQL 注入
- XSS 跨站脚本
- CSRF 跨站请求伪造
- 认证和会话管理
- 敏感数据暴露

### 11.2 安全头
```python
# 安全中间件配置
SECURE_HEADERS = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Content-Security-Policy': "default-src 'self'"
}
```

## 12. 监控与告警

### 12.1 监控指标

#### 关键指标
- 测试执行成功率
- 平均响应时间
- 资源使用率 (CPU, 内存, 网络)
- 错误率

### 12.2 告警规则
```yaml
alerts:
  - name: high_failure_rate
    condition: "error_rate > 0.1"
    severity: critical
    notify:
      - email: <EMAIL>
      - slack: #dev-alerts
    
  - name: slow_response
    condition: "avg_response_time > 5000"
    severity: warning
    for: 5m
```
