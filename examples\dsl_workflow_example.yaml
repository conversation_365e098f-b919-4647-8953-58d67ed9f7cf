# 工作流DSL示例
id: example_workflow
name: 示例工作流
version: 1.0.0
description: 展示工作流DSL功能的示例

# 工作流变量
variables:
  base_url: "https://example.com"
  username: "testuser"
  max_retries: 3

# 起始节点
start_at: navigate_to_home

# 节点定义
nodes:
  # 导航到首页
  navigate_to_home:
    id: navigate_to_home
    type: operation
    name: 导航到首页
    description: 导航到网站首页
    operation: navigate
    value: "{{ variables.base_url }}"
    output: navigation_result
    
  # 登录操作
  login:
    id: login
    type: operation
    name: 用户登录
    description: 执行用户登录操作
    operation: fill
    selector: "#username"
    value: "{{ variables.username }}"
    depends_on:
      - navigate_to_home
    output: login_result
    
  # 点击登录按钮
  click_login:
    id: click_login
    type: operation
    name: 点击登录按钮
    description: 点击登录按钮提交表单
    operation: click
    selector: "button[type='submit']"
    retry: 3  # 使用固定值而不是变量引用，简化示例
    retry_delay: 1.0
    depends_on:
      - login
    output: login_click_result
    
  # 条件检查 - 是否登录成功
  check_login:
    id: check_login
    type: condition
    name: 检查登录状态
    description: 检查用户是否成功登录
    condition:
      type: "exists"
      selector: ".user-profile"
    true_branch: ["welcome_message"]
    false_branch: ["show_error"]
    depends_on:
      - click_login
    
  # 欢迎消息
  welcome_message:
    id: welcome_message
    type: operation
    name: 显示欢迎消息
    description: 显示登录成功的欢迎消息
    operation: extract
    selector: ".welcome-message"
    output: welcome_text
    
  # 错误处理
  show_error:
    id: show_error
    type: operation
    name: 显示错误
    description: 显示登录错误信息
    operation: extract
    selector: ".error-message"
    output: error_message
    continue_on_failure: true
    
  # 循环示例 - 点击所有链接
  process_links:
    id: process_links
    type: loop
    name: 处理页面链接
    description: 循环处理页面上的所有链接
    loop_type: for_each
    selector: "a:not([href^='#'])"
    loop_var: link
    loop_body:
      - 
        id: click_link
        type: operation
        operation: click
        selector: "{{ link.selector }}"
        output: "{{ link.index }}_click_result"
      - 
        id: wait_after_click
        type: operation
        operation: wait
        value: 1000  # 等待1秒
    depends_on:
      - check_login
