# 快速开始

**预计时间**: 5分钟 | **难度**: 初级

## 🎯 目标

通过本指南，您将：
- 安装和配置AI+RPA系统
- 运行第一个AI自动化任务
- 了解基本使用方法

## 📋 前置条件

### 系统要求
- **操作系统**: Windows 11, macOS, 或 Linux
- **Python**: 3.8 或更高版本
- **内存**: 至少 4GB RAM
- **网络**: 稳定的互联网连接

### 必需账户
- **OpenAI账户**: 用于AI功能 ([注册链接](https://platform.openai.com/))
- **API密钥**: 从OpenAI控制台获取

## 🚀 安装步骤

### 步骤1: 下载项目
```bash
# 克隆项目
git clone <repository-url>
cd playwright

# 或下载ZIP文件并解压
```

### 步骤2: 创建虚拟环境
```bash
# Windows
python -m venv .venv
.\.venv\Scripts\activate

# macOS/Linux
python3 -m venv .venv
source .venv/bin/activate
```

### 步骤3: 安装依赖
```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装AI功能依赖
pip install browser-use

# 安装浏览器
playwright install
```

### 步骤4: 配置API密钥
```bash
# 方法1: 环境变量
export OPENAI_API_KEY="your-api-key-here"

# 方法2: .env文件
echo "OPENAI_API_KEY=your-api-key-here" > .env
```

## 基本使用

### 1. 录制工作流

```bash
# 启动录制器
python -m src.recorder -u https://your-target-url.com --viewport 1280,720

# 可选参数
--lang zh-CN              # 设置语言
--device "iPhone 13"      # 模拟移动设备
--color-scheme dark       # 使用深色模式
--timezone "Asia/Shanghai" # 设置时区
```

### 2. 执行工作流

```bash
# 执行已录制的工作流
python -m src.executor workflows/your_workflow.json

# 可选参数
--headless               # 无头模式执行
--debug                  # 启用调试模式
--retry 3               # 设置重试次数
```

### 3. 监控执行

```bash
# 启动带监控的执行
python -m src.monitor workflows/your_workflow.json

# 可选参数
--alert                  # 启用异常告警
--repair                 # 启用自动修复
--report                 # 生成执行报告
```

## 示例工作流

### 1. 简单登录流程

```json
{
  "name": "login_workflow",
  "version": "1.0.0",
  "steps": [
    {
      "id": "step_0",
      "type": "navigate",
      "description": "导航到登录页面",
      "metadata": {
        "url": "https://example.com/login"
      }
    },
    {
      "id": "step_1",
      "type": "fill",
      "description": "输入用户名",
      "metadata": {
        "selector": "#username",
        "value": "${username}"
      }
    },
    {
      "id": "step_2",
      "type": "fill",
      "description": "输入密码",
      "metadata": {
        "selector": "#password",
        "value": "${password}"
      }
    },
    {
      "id": "step_3",
      "type": "click",
      "description": "点击登录按钮",
      "metadata": {
        "selector": "#login-button"
      }
    }
  ],
  "variables": {
    "username": "test_user",
    "password": "test_pass"
  }
}
```

### 2. 执行示例工作流

```bash
# 使用变量执行工作流
python -m src.executor workflows/login_workflow.json \
  --var username=real_user \
  --var password=real_pass
```

## 常见问题

### 1. 录制问题
- **Q**: 录制时浏览器无法启动？
  - **A**: 检查 Playwright 是否正确安装：`playwright install`

- **Q**: 录制的选择器不准确？
  - **A**: 尝试使用更多的定位策略，如 `data-testid` 或文本内容

### 2. 执行问题
- **Q**: 执行时元素找不到？
  - **A**: 检查页面加载时间，可能需要增加等待时间

- **Q**: AI 功能无法使用？
  - **A**: 确认 OpenAI API 密钥配置正确且有效

## 下一步

- 查看[工作流录制指南](../03-user-guide/workflow-recording/README.md)了解更多录制功能
- 查看[浏览器监控指南](../03-user-guide/browser-monitoring/README.md)了解监控功能
- 查看[开发者指南](../04-developer-guide/README.md)了解如何扩展系统 