<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
            background-color: #f9f9f9;
        }
        .search-container {
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .search-box {
            display: flex;
            margin-bottom: 15px;
        }
        #search-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px 0 0 4px;
            font-size: 16px;
        }
        #search-button {
            padding: 10px 20px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
        }
        #search-button:hover {
            background-color: #0b7dda;
        }
        .search-results {
            display: none;
            margin-top: 20px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        .result-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .result-item:last-child {
            border-bottom: none;
        }
        .result-title {
            color: #1a0dab;
            text-decoration: none;
            font-weight: bold;
        }
        .result-url {
            color: #006621;
            font-size: 14px;
            margin: 3px 0;
            display: block;
        }
        .result-snippet {
            color: #545454;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="search-container">
        <h2>Search</h2>
        <div class="search-box">
            <input type="text" id="search-input" placeholder="Enter search terms..." autofocus>
            <button id="search-button">Search</button>
        </div>
        
        <div id="search-results" class="search-results">
            <div class="result-count">About 3 results</div>
            
            <div class="result-item">
                <a href="#" class="result-title">Test Result 1</a>
                <span class="result-url">https://example.com/test1</span>
                <div class="result-snippet">This is a test search result for demonstration purposes.</div>
            </div>
            
            <div class="result-item">
                <a href="#" class="result-title">Test Result 2</a>
                <span class="result-url">https://example.com/test2</span>
                <div class="result-snippet">Another test result to show multiple items in the search results.</div>
            </div>
            
            <div class="result-item">
                <a href="#" class="result-title">Test Result 3</a>
                <span class="result-url">https://example.com/test3</span>
                <div class="result-snippet">The third test result to complete our example set.</div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('search-button').addEventListener('click', performSearch);
        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        function performSearch() {
            const searchTerm = document.getElementById('search-input').value.trim();
            if (!searchTerm) return;
            
            // 模拟搜索延迟
            setTimeout(() => {
                const results = document.getElementById('search-results');
                const resultCount = document.querySelector('.result-count');
                
                // 更新结果计数
                resultCount.textContent = `About 3 results for "${searchTerm}"`;
                
                // 显示结果
                results.style.display = 'block';
                
                // 滚动到结果
                results.scrollIntoView({ behavior: 'smooth' });
                
                console.log('Search performed for:', searchTerm);
            }, 500);
        }
    </script>
</body>
</html>
