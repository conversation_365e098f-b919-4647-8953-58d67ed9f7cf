"""
页面操作分析器

实现页面链接提取、操作数据生成和智能导航功能
"""
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from urllib.parse import urljoin, urlparse

from ai_llm_manager import get_llm_manager, LLMProvider

logger = logging.getLogger(__name__)


@dataclass
class PageLink:
    """页面链接信息"""
    text: str                    # 链接文本
    href: str                    # 链接地址
    selector: str                # CSS选择器
    element_type: str            # 元素类型 (link, button, menu, etc.)
    description: str             # AI生成的描述
    category: str                # 功能分类
    priority: int                # 重要性优先级 (1-10)
    is_external: bool            # 是否外部链接
    requires_auth: bool          # 是否需要认证
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PageLink':
        return cls(**data)


@dataclass
class PageOperationData:
    """页面操作数据"""
    url: str                     # 页面URL
    title: str                   # 页面标题
    domain: str                  # 域名
    links: List[PageLink]        # 所有链接
    navigation_structure: Dict   # 导航结构
    functional_areas: Dict       # 功能区域
    analysis_timestamp: str      # 分析时间戳
    ai_summary: str              # AI生成的页面摘要
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "url": self.url,
            "title": self.title,
            "domain": self.domain,
            "links": [link.to_dict() for link in self.links],
            "navigation_structure": self.navigation_structure,
            "functional_areas": self.functional_areas,
            "analysis_timestamp": self.analysis_timestamp,
            "ai_summary": self.ai_summary
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PageOperationData':
        return cls(
            url=data["url"],
            title=data["title"],
            domain=data["domain"],
            links=[PageLink.from_dict(link) for link in data["links"]],
            navigation_structure=data["navigation_structure"],
            functional_areas=data["functional_areas"],
            analysis_timestamp=data["analysis_timestamp"],
            ai_summary=data["ai_summary"]
        )


class PageOperationAnalyzer:
    """页面操作分析器"""
    
    def __init__(self):
        self.llm_manager = get_llm_manager()
        self.analysis_cache = {}
    
    async def analyze_page_operations(self, page) -> PageOperationData:
        """分析页面操作"""
        try:
            logger.info("开始分析页面操作")
            
            # 获取页面基本信息
            url = page.url
            title = await page.title()
            domain = self._extract_domain(url)
            
            logger.info(f"分析页面: {title} ({url})")
            
            # 提取所有链接和操作元素
            links = await self._extract_page_links(page)
            logger.info(f"提取到 {len(links)} 个链接")
            
            # AI分析页面结构和功能
            navigation_structure, functional_areas, ai_summary = await self._ai_analyze_page_structure(page, links)
            
            # 创建页面操作数据
            operation_data = PageOperationData(
                url=url,
                title=title,
                domain=domain,
                links=links,
                navigation_structure=navigation_structure,
                functional_areas=functional_areas,
                analysis_timestamp=datetime.now().isoformat(),
                ai_summary=ai_summary
            )
            
            logger.info(f"页面操作分析完成: {len(links)} 个链接, {len(functional_areas)} 个功能区域")
            
            return operation_data
            
        except Exception as e:
            logger.error(f"页面操作分析失败: {e}")
            raise
    
    async def _extract_page_links(self, page) -> List[PageLink]:
        """提取页面链接"""
        try:
            # 获取所有可点击元素
            elements_script = """
            () => {
                const elements = [];
                
                // 获取所有链接
                document.querySelectorAll('a[href]').forEach((el, index) => {
                    const rect = el.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {
                        elements.push({
                            type: 'link',
                            text: el.textContent.trim(),
                            href: el.href,
                            selector: `a:nth-of-type(${index + 1})`,
                            visible: true
                        });
                    }
                });
                
                // 获取所有按钮
                document.querySelectorAll('button, input[type="button"], input[type="submit"]').forEach((el, index) => {
                    const rect = el.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {
                        elements.push({
                            type: 'button',
                            text: el.textContent.trim() || el.value || el.title,
                            href: el.onclick ? 'javascript:void(0)' : '',
                            selector: `button:nth-of-type(${index + 1})`,
                            visible: true
                        });
                    }
                });
                
                // 获取导航菜单
                document.querySelectorAll('.nav a, .menu a, .navbar a, [role="menuitem"]').forEach((el, index) => {
                    const rect = el.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {
                        elements.push({
                            type: 'menu',
                            text: el.textContent.trim(),
                            href: el.href || '',
                            selector: `.nav a:nth-of-type(${index + 1})`,
                            visible: true
                        });
                    }
                });
                
                return elements;
            }
            """
            
            raw_elements = await page.evaluate(elements_script)
            
            # 处理和分类链接
            links = []
            current_url = page.url
            
            for element in raw_elements:
                if not element['text'] or len(element['text']) > 200:
                    continue
                
                href = element['href']
                if href:
                    # 转换为绝对URL
                    if href.startswith('/'):
                        href = urljoin(current_url, href)
                    elif href.startswith('#'):
                        href = current_url + href
                
                # AI分析链接功能
                description, category, priority = await self._ai_analyze_link(element['text'], href, element['type'])
                
                link = PageLink(
                    text=element['text'],
                    href=href,
                    selector=element['selector'],
                    element_type=element['type'],
                    description=description,
                    category=category,
                    priority=priority,
                    is_external=self._is_external_link(href, current_url),
                    requires_auth=self._requires_auth(href)
                )
                
                links.append(link)
            
            # 去重和排序
            unique_links = self._deduplicate_links(links)
            sorted_links = sorted(unique_links, key=lambda x: (-x.priority, x.text))
            
            return sorted_links
            
        except Exception as e:
            logger.error(f"提取页面链接失败: {e}")
            return []
    
    async def _ai_analyze_link(self, text: str, href: str, element_type: str) -> Tuple[str, str, int]:
        """AI分析链接功能"""
        try:
            prompt = f"""
请分析这个页面元素的功能和重要性：

元素信息：
- 文本: {text}
- 链接: {href}
- 类型: {element_type}

请提供：
1. 功能描述（简洁明了，不超过50字）
2. 功能分类（从以下选择：导航、操作、查询、设置、帮助、其他）
3. 重要性评分（1-10，10最重要）

返回格式：
描述|分类|评分

例如：
用户管理页面，可以查看和编辑用户信息|操作|8
"""
            
            response = await self.llm_manager.generate(prompt, provider=LLMProvider.GEMINI)
            
            # 解析AI响应
            parts = response.content.strip().split('|')
            if len(parts) >= 3:
                description = parts[0].strip()
                category = parts[1].strip()
                try:
                    priority = int(parts[2].strip())
                    priority = max(1, min(10, priority))  # 限制在1-10范围
                except ValueError:
                    priority = 5
            else:
                description = f"{element_type}: {text}"
                category = "其他"
                priority = 5
            
            return description, category, priority
            
        except Exception as e:
            logger.warning(f"AI分析链接失败: {e}")
            return f"{element_type}: {text}", "其他", 5
    
    async def _ai_analyze_page_structure(self, page, links: List[PageLink]) -> Tuple[Dict, Dict, str]:
        """AI分析页面结构"""
        try:
            # 获取页面HTML结构信息
            page_info = await page.evaluate("""
            () => {
                return {
                    title: document.title,
                    headings: Array.from(document.querySelectorAll('h1, h2, h3')).map(h => h.textContent.trim()),
                    navigation: Array.from(document.querySelectorAll('.nav, .navbar, .menu')).map(nav => nav.textContent.trim()),
                    main_content: document.querySelector('main, .main, .content, #content')?.textContent.trim().substring(0, 500) || '',
                    sidebar: document.querySelector('.sidebar, .aside, aside')?.textContent.trim().substring(0, 300) || ''
                };
            }
            """)
            
            # 构建AI分析提示
            links_summary = "\n".join([f"- {link.text} ({link.category})" for link in links[:20]])
            
            prompt = f"""
请分析这个网页的结构和功能：

页面信息：
- 标题: {page_info['title']}
- 主要标题: {', '.join(page_info['headings'][:5])}
- 导航区域: {', '.join(page_info['navigation'][:3])}

主要链接：
{links_summary}

请提供：
1. 导航结构分析（JSON格式，包含主导航、子导航等）
2. 功能区域分析（JSON格式，包含各功能模块）
3. 页面功能摘要（100字以内）

返回格式：
导航结构JSON|功能区域JSON|页面摘要
"""
            
            response = await self.llm_manager.generate(prompt, provider=LLMProvider.GEMINI)
            
            # 解析AI响应
            parts = response.content.strip().split('|')
            
            try:
                navigation_structure = json.loads(parts[0]) if len(parts) > 0 else {}
            except:
                navigation_structure = {"main_nav": [link.text for link in links if link.category == "导航"][:5]}
            
            try:
                functional_areas = json.loads(parts[1]) if len(parts) > 1 else {}
            except:
                functional_areas = {
                    "操作功能": [link.text for link in links if link.category == "操作"][:5],
                    "查询功能": [link.text for link in links if link.category == "查询"][:5]
                }
            
            ai_summary = parts[2] if len(parts) > 2 else f"包含{len(links)}个操作链接的{page_info['title']}页面"
            
            return navigation_structure, functional_areas, ai_summary
            
        except Exception as e:
            logger.warning(f"AI分析页面结构失败: {e}")
            return {}, {}, "页面结构分析失败"
    
    def _extract_domain(self, url: str) -> str:
        """提取域名"""
        try:
            parsed = urlparse(url)
            return f"{parsed.scheme}://{parsed.netloc}"
        except Exception:
            return url
    
    def _is_external_link(self, href: str, current_url: str) -> bool:
        """判断是否外部链接"""
        try:
            if not href or href.startswith('#') or href.startswith('javascript:'):
                return False
            
            current_domain = urlparse(current_url).netloc
            link_domain = urlparse(href).netloc
            
            return link_domain and link_domain != current_domain
        except Exception:
            return False
    
    def _requires_auth(self, href: str) -> bool:
        """判断是否需要认证"""
        # 简单的启发式判断
        auth_keywords = ['login', 'auth', 'admin', 'secure', 'private']
        return any(keyword in href.lower() for keyword in auth_keywords)
    
    def _deduplicate_links(self, links: List[PageLink]) -> List[PageLink]:
        """去重链接"""
        seen = set()
        unique_links = []
        
        for link in links:
            # 使用文本和href作为去重键
            key = (link.text.lower().strip(), link.href)
            if key not in seen:
                seen.add(key)
                unique_links.append(link)
        
        return unique_links


class PageOperationManager:
    """页面操作管理器"""
    
    def __init__(self, storage_dir: str = "page_operations"):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        self.analyzer = PageOperationAnalyzer()
    
    async def save_page_operations(self, operation_data: PageOperationData) -> str:
        """保存页面操作数据"""
        try:
            # 生成文件名
            timestamp = int(time.time())
            domain_name = urlparse(operation_data.url).netloc.replace('.', '_')
            filename = f"page_ops_{domain_name}_{timestamp}.json"
            filepath = self.storage_dir / filename
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(operation_data.to_dict(), f, indent=2, ensure_ascii=False)
            
            logger.info(f"页面操作数据已保存: {filepath}")
            
            return str(filepath)
            
        except Exception as e:
            logger.error(f"保存页面操作数据失败: {e}")
            raise
    
    def load_page_operations(self, filepath: str) -> PageOperationData:
        """加载页面操作数据"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return PageOperationData.from_dict(data)
            
        except Exception as e:
            logger.error(f"加载页面操作数据失败: {e}")
            raise
    
    def list_page_operations(self) -> List[Dict[str, Any]]:
        """列出所有页面操作数据"""
        operations = []
        
        for filepath in self.storage_dir.glob("*.json"):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                operations.append({
                    "filepath": str(filepath),
                    "url": data["url"],
                    "title": data["title"],
                    "domain": data["domain"],
                    "links_count": len(data["links"]),
                    "analysis_timestamp": data["analysis_timestamp"],
                    "ai_summary": data["ai_summary"]
                })
                
            except Exception as e:
                logger.warning(f"读取操作文件失败: {filepath} - {e}")
                continue
        
        # 按时间排序
        operations.sort(key=lambda x: x["analysis_timestamp"], reverse=True)
        
        return operations


# 全局实例
global_page_operation_analyzer = PageOperationAnalyzer()
global_page_operation_manager = PageOperationManager()


def get_page_operation_analyzer() -> PageOperationAnalyzer:
    """获取页面操作分析器实例"""
    return global_page_operation_analyzer


def get_page_operation_manager() -> PageOperationManager:
    """获取页面操作管理器实例"""
    return global_page_operation_manager
