"""
交互模式测试

测试图形化聊天窗口和智能交互功能
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"✅ 加载环境变量: {key}")


def test_imports():
    """测试导入"""
    print("🧪 测试交互模式模块导入")
    print("-" * 40)
    
    try:
        from interactive_chat_window import InteractiveChatWindow, ChatMessage, InteractionRecord
        print("   ✅ interactive_chat_window 导入成功")
        
        from intelligent_command_processor import (
            IntelligentCommandProcessor,
            CommandType,
            CommandResult,
            get_intelligent_command_processor
        )
        print("   ✅ intelligent_command_processor 导入成功")
        
        from interactive_mode_manager import (
            InteractiveModeManager,
            get_interactive_mode_manager
        )
        print("   ✅ interactive_mode_manager 导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        return False


async def test_command_processor():
    """测试智能命令处理器"""
    print("\n🧪 测试智能命令处理器")
    print("-" * 40)
    
    try:
        from intelligent_command_processor import get_intelligent_command_processor
        
        processor = get_intelligent_command_processor()
        print("   ✅ 智能命令处理器创建成功")
        
        # 测试命令
        test_commands = [
            "分析当前页面",
            "我要查看用户管理",
            "导航到设置页面",
            "查询订单信息",
            "系统状态",
            "帮助",
            "这是一个未知的命令"
        ]
        
        print("   🔍 测试命令识别:")
        
        for command in test_commands:
            print(f"\n   📝 测试命令: '{command}'")
            
            result = await processor.process_command(command)
            
            print(f"      ✅ 识别成功: {result.success}")
            print(f"      🏷️ 命令类型: {result.command_type.value}")
            print(f"      ⚡ 动作: {result.action}")
            print(f"      📊 置信度: {result.confidence:.2f}")
            print(f"      🔧 处理方法: {result.processing_method}")
            print(f"      💬 响应: {result.response}")
        
        # 测试支持的命令
        supported_commands = processor.get_supported_commands()
        print(f"\n   📋 支持的命令类型: {len(supported_commands)} 种")
        
        for cmd_type, examples in supported_commands.items():
            print(f"      • {cmd_type}: {len(examples)} 个示例")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 智能命令处理器测试失败: {e}")
        return False


def test_chat_window():
    """测试聊天窗口（非阻塞）"""
    print("\n🧪 测试聊天窗口")
    print("-" * 40)
    
    try:
        from interactive_chat_window import InteractiveChatWindow
        
        print("   ✅ 聊天窗口类导入成功")
        
        # 创建聊天窗口实例
        chat_window = InteractiveChatWindow(
            title="测试聊天窗口",
            width=600,
            height=700
        )
        
        print("   ✅ 聊天窗口实例创建成功")
        print("   💡 聊天窗口功能:")
        print("      • 图形化界面")
        print("      • 实时消息显示")
        print("      • 用户输入处理")
        print("      • 快捷操作按钮")
        print("      • 菜单和状态栏")
        print("      • 对话记录保存")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 聊天窗口测试失败: {e}")
        return False


async def test_interactive_manager():
    """测试交互模式管理器"""
    print("\n🧪 测试交互模式管理器")
    print("-" * 40)
    
    try:
        from interactive_mode_manager import get_interactive_mode_manager
        from ai_login_workflow_generator import get_login_state_executor
        
        manager = get_interactive_mode_manager()
        login_executor = get_login_state_executor()
        
        print("   ✅ 交互模式管理器创建成功")
        
        # 检查登录会话
        print("   🔍 检查可用的登录会话...")
        sessions = await login_executor.list_available_sessions()
        valid_sessions = [s for s in sessions if s["is_valid"]]
        
        print(f"   📊 找到 {len(valid_sessions)} 个有效登录会话")
        
        if valid_sessions:
            for i, session in enumerate(valid_sessions[:3], 1):
                print(f"      {i}. {session['name']} ({session['domain']})")
        
        # 测试管理器功能
        print("   🔧 交互模式管理器功能:")
        print("      • 聊天窗口管理")
        print("      • 命令处理集成")
        print("      • 复杂场景工作流集成")
        print("      • 操作记录管理")
        print("      • 会话状态管理")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 交互模式管理器测试失败: {e}")
        return False


async def demo_interactive_mode():
    """演示交互模式"""
    print("\n🎭 交互模式演示")
    print("-" * 40)
    
    try:
        from interactive_mode_manager import get_interactive_mode_manager
        from ai_login_workflow_generator import get_login_state_executor
        
        manager = get_interactive_mode_manager()
        login_executor = get_login_state_executor()
        
        # 检查登录会话
        sessions = await login_executor.list_available_sessions()
        valid_sessions = [s for s in sessions if s["is_valid"]]
        
        if not valid_sessions:
            print("   ❌ 没有可用的登录会话")
            print("   💡 请先运行登录测试创建登录会话")
            return False
        
        print(f"   📋 可用的登录会话:")
        for i, session in enumerate(valid_sessions, 1):
            print(f"      {i}. {session['name']} ({session['domain']})")
        
        # 选择登录会话
        choice = input(f"\n   选择登录会话 (1-{len(valid_sessions)}, 或按回车使用第一个): ").strip()
        
        if choice and choice.isdigit():
            session_index = int(choice) - 1
            if 0 <= session_index < len(valid_sessions):
                selected_session = valid_sessions[session_index]
            else:
                selected_session = valid_sessions[0]
        else:
            selected_session = valid_sessions[0]
        
        print(f"   ✅ 选择会话: {selected_session['name']}")
        
        # 启动交互模式
        scenario_name = input("   场景名称 (默认: 交互式测试): ").strip()
        if not scenario_name:
            scenario_name = "交互式测试"
        
        print(f"\n   🚀 启动交互模式: {scenario_name}")
        print("   💡 即将打开图形化聊天窗口...")
        print("   📱 您可以在聊天窗口中:")
        print("      • 输入需求进行智能导航")
        print("      • 使用命令进行操作")
        print("      • 点击快捷按钮")
        print("      • 查看操作记录")
        
        # 启动交互模式（这会打开图形界面）
        await manager.start_interactive_mode(
            scenario_name=scenario_name,
            login_session_id=selected_session["session_id"]
        )
        
        return True
        
    except Exception as e:
        print(f"   ❌ 交互模式演示失败: {e}")
        return False


def show_interactive_features():
    """显示交互模式功能特点"""
    print("\n💡 交互模式功能特点")
    print("=" * 50)
    
    print("\n🎨 图形化界面:")
    print("   • 友好的聊天窗口界面")
    print("   • 实时消息显示和交互")
    print("   • 快捷操作按钮")
    print("   • 完整的菜单系统")
    print("   • 状态栏和会话信息")
    
    print("\n🤖 智能命令识别:")
    print("   • 自然语言命令理解")
    print("   • 多种命令类型支持")
    print("   • AI协助处理未知命令")
    print("   • 智能参数提取")
    print("   • 置信度评估")
    
    print("\n🎯 支持的操作:")
    print("   • 智能页面导航")
    print("   • 页面分析和信息提取")
    print("   • 操作记录和历史查看")
    print("   • 系统状态查询")
    print("   • 帮助和使用指南")
    
    print("\n📝 操作记录:")
    print("   • 完整的交互历史记录")
    print("   • 操作流程自动记录")
    print("   • JSON格式数据导出")
    print("   • 可复用的操作模板")
    
    print("\n🔧 使用示例:")
    print("   • '我要查看用户管理' - 智能导航")
    print("   • '分析当前页面' - 页面分析")
    print("   • '查询订单信息' - 信息查询")
    print("   • '系统状态' - 状态查看")
    print("   • '帮助' - 使用说明")


async def main():
    """主函数"""
    print("🎭 交互模式系统测试")
    print("图形化聊天窗口 + 智能命令识别 + AI协助处理")
    print("=" * 70)
    
    # 加载环境变量
    load_env()
    
    # 检查AI配置
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        print("❌ 缺少GEMINI_API_KEY配置")
        return
    
    print("✅ AI配置检查通过")
    
    # 检查tkinter支持
    try:
        import tkinter
        print("✅ tkinter图形界面支持可用")
    except ImportError:
        print("❌ tkinter不可用，无法显示图形界面")
        return
    
    # 运行测试
    tests = [
        ("模块导入", test_imports),
        ("智能命令处理器", test_command_processor),
        ("聊天窗口", test_chat_window),
        ("交互模式管理器", test_interactive_manager)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                results[test_name] = await test_func()
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    print("\n" + "=" * 70)
    print("📊 交互模式系统测试报告")
    print("=" * 70)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！交互模式系统准备就绪！")
        
        # 显示功能特点
        show_interactive_features()
        
        # 询问是否启动演示
        demo_choice = input(f"\n🎭 是否启动交互模式演示? (y/n): ").strip().lower()
        if demo_choice in ['y', 'yes']:
            await demo_interactive_mode()
        
        print(f"\n🏆 交互模式系统优势:")
        print(f"   • 图形化用户界面，提升用户体验")
        print(f"   • 智能命令识别，支持自然语言交互")
        print(f"   • AI协助处理，提高命令理解准确率")
        print(f"   • 完整操作记录，支持流程复用")
        print(f"   • 实时交互反馈，提高操作效率")
        
        print(f"\n🎯 现在您可以:")
        print(f"   • 使用图形界面进行AI+RPA操作")
        print(f"   • 用自然语言描述需求")
        print(f"   • 享受智能化的交互体验")
        print(f"   • 记录和复用操作流程")
        
    else:
        print(f"\n❌ 部分测试失败，需要检查配置")


if __name__ == "__main__":
    asyncio.run(main())
