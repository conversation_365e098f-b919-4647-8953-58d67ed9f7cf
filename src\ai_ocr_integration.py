"""
AI OCR集成模块

提供AI OCR功能，用于获取和分析界面信息。
"""
import base64
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import json

logger = logging.getLogger(__name__)


@dataclass
class OCRResult:
    """OCR识别结果"""
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # (x, y, width, height)
    element_type: Optional[str] = None  # button, input, text, etc.


@dataclass
class PageAnalysis:
    """页面分析结果"""
    timestamp: float
    page_url: str
    title: str
    ocr_results: List[OCRResult]
    ui_elements: List[Dict[str, Any]]
    layout_analysis: Dict[str, Any]
    suggested_actions: List[str]


class AIVisionAnalyzer:
    """AI视觉分析器"""
    
    def __init__(self):
        """初始化AI视觉分析器"""
        self.ocr_enabled = True
        self.element_detection_enabled = True
        self.layout_analysis_enabled = True
        
        # 模拟AI模型配置（实际应该集成真实的AI服务）
        self.confidence_threshold = 0.7
        self.supported_languages = ['zh-CN', 'en-US']
        
    async def analyze_page(self, page, screenshot_path: Optional[str] = None) -> PageAnalysis:
        """
        分析页面
        
        Args:
            page: Playwright页面对象
            screenshot_path: 截图路径（可选）
            
        Returns:
            PageAnalysis: 页面分析结果
        """
        logger.info("开始AI页面分析")
        
        # 获取页面基本信息
        page_info = await self._get_page_info(page)
        
        # 截图（如果没有提供）
        if not screenshot_path:
            screenshot_path = f"temp_screenshot_{int(time.time())}.png"
            await page.screenshot(path=screenshot_path)
        
        # OCR文字识别
        ocr_results = []
        if self.ocr_enabled:
            ocr_results = await self._perform_ocr(screenshot_path)
        
        # UI元素检测
        ui_elements = []
        if self.element_detection_enabled:
            ui_elements = await self._detect_ui_elements(page, screenshot_path)
        
        # 布局分析
        layout_analysis = {}
        if self.layout_analysis_enabled:
            layout_analysis = await self._analyze_layout(page, ocr_results, ui_elements)
        
        # 生成建议操作
        suggested_actions = self._generate_suggested_actions(ocr_results, ui_elements, layout_analysis)
        
        return PageAnalysis(
            timestamp=time.time(),
            page_url=page_info['url'],
            title=page_info['title'],
            ocr_results=ocr_results,
            ui_elements=ui_elements,
            layout_analysis=layout_analysis,
            suggested_actions=suggested_actions
        )
    
    async def _get_page_info(self, page) -> Dict[str, Any]:
        """获取页面基本信息"""
        try:
            return await page.evaluate("""
                () => {
                    return {
                        title: document.title,
                        url: window.location.href,
                        readyState: document.readyState,
                        viewport: {
                            width: window.innerWidth,
                            height: window.innerHeight
                        }
                    };
                }
            """)
        except Exception as e:
            logger.error(f"获取页面信息失败: {e}")
            return {"title": "", "url": "", "readyState": "unknown", "viewport": {}}
    
    async def _perform_ocr(self, screenshot_path: str) -> List[OCRResult]:
        """
        执行OCR识别
        
        注意：这里是模拟实现，实际应该集成真实的OCR服务
        如：Google Vision API, Azure Computer Vision, 百度OCR等
        """
        logger.info(f"执行OCR识别: {screenshot_path}")
        
        # 模拟OCR结果
        mock_ocr_results = [
            OCRResult(
                text="登录",
                confidence=0.95,
                bbox=(100, 50, 60, 30),
                element_type="button"
            ),
            OCRResult(
                text="用户名",
                confidence=0.92,
                bbox=(50, 100, 80, 25),
                element_type="label"
            ),
            OCRResult(
                text="密码",
                confidence=0.90,
                bbox=(50, 150, 60, 25),
                element_type="label"
            ),
            OCRResult(
                text="提交",
                confidence=0.88,
                bbox=(200, 200, 50, 30),
                element_type="button"
            )
        ]
        
        # 实际实现应该调用真实的OCR API
        # 例如：
        # try:
        #     import cv2
        #     import pytesseract
        #     
        #     image = cv2.imread(screenshot_path)
        #     # 预处理图像
        #     gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        #     
        #     # 使用tesseract进行OCR
        #     data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)
        #     
        #     ocr_results = []
        #     for i in range(len(data['text'])):
        #         if int(data['conf'][i]) > self.confidence_threshold * 100:
        #             ocr_results.append(OCRResult(
        #                 text=data['text'][i],
        #                 confidence=int(data['conf'][i]) / 100.0,
        #                 bbox=(data['left'][i], data['top'][i], data['width'][i], data['height'][i])
        #             ))
        #     
        #     return ocr_results
        # except Exception as e:
        #     logger.error(f"OCR识别失败: {e}")
        #     return []
        
        return mock_ocr_results
    
    async def _detect_ui_elements(self, page, screenshot_path: str) -> List[Dict[str, Any]]:
        """
        检测UI元素
        
        结合Playwright的元素信息和AI视觉检测
        """
        logger.info("检测UI元素")
        
        ui_elements = []
        
        try:
            # 获取页面中的所有交互元素
            elements_info = await page.evaluate("""
                () => {
                    const elements = [];
                    
                    // 获取所有按钮
                    document.querySelectorAll('button, input[type="button"], input[type="submit"]').forEach((el, index) => {
                        const rect = el.getBoundingClientRect();
                        elements.push({
                            type: 'button',
                            text: el.textContent || el.value || '',
                            id: el.id || '',
                            className: el.className || '',
                            bbox: [rect.x, rect.y, rect.width, rect.height],
                            visible: rect.width > 0 && rect.height > 0,
                            enabled: !el.disabled,
                            selector: `button:nth-child(${index + 1})`
                        });
                    });
                    
                    // 获取所有输入框
                    document.querySelectorAll('input[type="text"], input[type="password"], input[type="email"], textarea').forEach((el, index) => {
                        const rect = el.getBoundingClientRect();
                        elements.push({
                            type: 'input',
                            placeholder: el.placeholder || '',
                            id: el.id || '',
                            className: el.className || '',
                            bbox: [rect.x, rect.y, rect.width, rect.height],
                            visible: rect.width > 0 && rect.height > 0,
                            enabled: !el.disabled,
                            selector: `input:nth-child(${index + 1})`
                        });
                    });
                    
                    // 获取所有链接
                    document.querySelectorAll('a').forEach((el, index) => {
                        const rect = el.getBoundingClientRect();
                        elements.push({
                            type: 'link',
                            text: el.textContent || '',
                            href: el.href || '',
                            id: el.id || '',
                            className: el.className || '',
                            bbox: [rect.x, rect.y, rect.width, rect.height],
                            visible: rect.width > 0 && rect.height > 0,
                            selector: `a:nth-child(${index + 1})`
                        });
                    });
                    
                    return elements;
                }
            """)
            
            ui_elements = elements_info
            
        except Exception as e:
            logger.error(f"UI元素检测失败: {e}")
        
        return ui_elements
    
    async def _analyze_layout(self, page, ocr_results: List[OCRResult], ui_elements: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析页面布局"""
        logger.info("分析页面布局")
        
        layout_analysis = {
            "page_type": "unknown",
            "main_sections": [],
            "form_detected": False,
            "navigation_detected": False,
            "content_areas": []
        }
        
        try:
            # 分析页面类型
            layout_analysis["page_type"] = self._classify_page_type(ocr_results, ui_elements)
            
            # 检测表单
            form_elements = [el for el in ui_elements if el['type'] in ['input', 'button']]
            if len(form_elements) >= 2:
                layout_analysis["form_detected"] = True
                layout_analysis["form_elements"] = form_elements
            
            # 检测导航
            nav_elements = [el for el in ui_elements if el['type'] == 'link']
            if len(nav_elements) >= 3:
                layout_analysis["navigation_detected"] = True
                layout_analysis["navigation_elements"] = nav_elements
            
            # 分析内容区域
            layout_analysis["content_areas"] = self._identify_content_areas(ocr_results, ui_elements)
            
        except Exception as e:
            logger.error(f"布局分析失败: {e}")
        
        return layout_analysis
    
    def _classify_page_type(self, ocr_results: List[OCRResult], ui_elements: List[Dict[str, Any]]) -> str:
        """分类页面类型"""
        # 基于OCR文本和UI元素分类页面
        ocr_texts = [result.text.lower() for result in ocr_results]
        
        # 登录页面
        if any(keyword in ' '.join(ocr_texts) for keyword in ['登录', 'login', '用户名', 'username', '密码', 'password']):
            return "login_page"
        
        # 表单页面
        input_count = len([el for el in ui_elements if el['type'] == 'input'])
        if input_count >= 3:
            return "form_page"
        
        # 列表页面
        if any(keyword in ' '.join(ocr_texts) for keyword in ['列表', 'list', '搜索', 'search']):
            return "list_page"
        
        # 详情页面
        if any(keyword in ' '.join(ocr_texts) for keyword in ['详情', 'detail', '信息', 'info']):
            return "detail_page"
        
        return "general_page"
    
    def _identify_content_areas(self, ocr_results: List[OCRResult], ui_elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """识别内容区域"""
        content_areas = []
        
        # 简单的区域划分逻辑
        # 实际应该使用更复杂的布局分析算法
        
        # 头部区域
        header_elements = [el for el in ui_elements if el['bbox'][1] < 100]
        if header_elements:
            content_areas.append({
                "type": "header",
                "elements": header_elements,
                "bbox": self._calculate_bounding_box(header_elements)
            })
        
        # 主体区域
        main_elements = [el for el in ui_elements if 100 <= el['bbox'][1] <= 500]
        if main_elements:
            content_areas.append({
                "type": "main",
                "elements": main_elements,
                "bbox": self._calculate_bounding_box(main_elements)
            })
        
        # 底部区域
        footer_elements = [el for el in ui_elements if el['bbox'][1] > 500]
        if footer_elements:
            content_areas.append({
                "type": "footer",
                "elements": footer_elements,
                "bbox": self._calculate_bounding_box(footer_elements)
            })
        
        return content_areas
    
    def _calculate_bounding_box(self, elements: List[Dict[str, Any]]) -> Tuple[int, int, int, int]:
        """计算元素组的边界框"""
        if not elements:
            return (0, 0, 0, 0)
        
        min_x = min(el['bbox'][0] for el in elements)
        min_y = min(el['bbox'][1] for el in elements)
        max_x = max(el['bbox'][0] + el['bbox'][2] for el in elements)
        max_y = max(el['bbox'][1] + el['bbox'][3] for el in elements)
        
        return (min_x, min_y, max_x - min_x, max_y - min_y)
    
    def _generate_suggested_actions(self, ocr_results: List[OCRResult], ui_elements: List[Dict[str, Any]], layout_analysis: Dict[str, Any]) -> List[str]:
        """生成建议操作"""
        suggestions = []
        
        page_type = layout_analysis.get("page_type", "unknown")
        
        if page_type == "login_page":
            suggestions.extend([
                "填写用户名和密码",
                "点击登录按钮",
                "检查是否有验证码需要输入"
            ])
        elif page_type == "form_page":
            suggestions.extend([
                "填写必填字段",
                "检查表单验证",
                "提交表单"
            ])
        elif page_type == "list_page":
            suggestions.extend([
                "使用搜索功能",
                "点击列表项查看详情",
                "使用分页导航"
            ])
        
        # 基于可用的UI元素添加建议
        button_count = len([el for el in ui_elements if el['type'] == 'button'])
        if button_count > 0:
            suggestions.append(f"页面有 {button_count} 个可点击按钮")
        
        input_count = len([el for el in ui_elements if el['type'] == 'input'])
        if input_count > 0:
            suggestions.append(f"页面有 {input_count} 个输入框需要填写")
        
        return suggestions
    
    def save_analysis_result(self, analysis: PageAnalysis, file_path: str):
        """保存分析结果到文件"""
        try:
            # 转换为可序列化的格式
            data = {
                "timestamp": analysis.timestamp,
                "page_url": analysis.page_url,
                "title": analysis.title,
                "ocr_results": [
                    {
                        "text": result.text,
                        "confidence": result.confidence,
                        "bbox": result.bbox,
                        "element_type": result.element_type
                    }
                    for result in analysis.ocr_results
                ],
                "ui_elements": analysis.ui_elements,
                "layout_analysis": analysis.layout_analysis,
                "suggested_actions": analysis.suggested_actions
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"分析结果已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")


# 全局AI视觉分析器实例
global_analyzer = AIVisionAnalyzer()


def get_analyzer() -> AIVisionAnalyzer:
    """获取全局AI视觉分析器实例"""
    return global_analyzer
