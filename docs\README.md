# Playwright 自动化测试框架文档

## 文档结构

### 1. 项目概述
- [项目简介](overview/README.md)

### 2. 规格说明
- [功能需求规格说明](specs/requirements.md)
- [技术规范](specs/technical.md)
- [API文档](specs/api.md)

### 3. 系统架构
- [系统架构设计](architecture/design.md)

### 4. 开发文档
- [开发计划](development/plan.md)
- [开发进度](development/progress.md)
- [操作系统设计](development/operations.md)
- [自修复机制指南](development/self_healing.md)

### 5. 测试
- [测试策略](testing/strategy.md)

### 6. 部署
- [部署与运维指南](deployment/guide.md)

### 7. 用户文档
- [用户指南](user/guide.md)

### 8. 开发者文档
- [开发者指南](developer/guide.md)

### 9. AI相关
- [AI模型开发与训练指南](ai/guide.md)

## 最新更新

**更新日期**: 2025-05-31

### 主要进展
1. 完成了工作流引擎核心功能的开发和测试
2. 实现了操作执行器的基本功能
3. 添加了工作流状态管理和序列化支持
4. 完善了测试用例覆盖

### 下一步计划
1. 实现工作流版本控制
2. 完善操作记录功能
3. 优化操作执行性能
4. 添加更多的测试用例

## 文档维护指南

1. 所有文档使用Markdown格式编写
2. 文档更新时请同步更新本README.md文件
3. 保持文档的版本号与代码版本一致
4. 重要更新请在"最新更新"部分注明
5. 每个目录下都应该包含一个README.md文件，说明该目录下文档的组织结构

## 贡献指南

1. 提交前请确保文档格式正确
2. 更新文档时请同步更新相关的索引
3. 保持文档的简洁性和可读性
4. 重要的代码示例请确保经过测试
5. 欢迎提交Issue和Pull Request来改进文档
