"""
工作流图片识别节点

将通用图片识别集成为标准工作流业务节点
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from workflow.models import Step, Workflow
from universal_image_recognition import (
    UniversalImageRecognizer,
    ImageRecognitionConfig,
    ImageRecognitionType,
    RecognitionMethod,
    ImageRecognitionResult
)

logger = logging.getLogger(__name__)


class ImageRecognitionWorkflowNode:
    """图片识别工作流节点"""
    
    def __init__(self):
        self.recognizer = UniversalImageRecognizer()
        self.node_type = "image_recognition"
    
    async def execute(self, page, step: Step) -> Dict[str, Any]:
        """执行图片识别节点"""
        try:
            logger.info(f"执行图片识别节点: {step.id}")
            
            # 从步骤元数据中解析配置
            config = self._parse_step_config(step.metadata)
            
            # 执行图片识别
            result = await self.recognizer.recognize_image(page, config)
            
            # 返回执行结果
            return {
                "success": result.success,
                "message": f"图片识别{'成功' if result.success else '失败'}: {result.recognized_text}",
                "recognized_text": result.recognized_text,
                "confidence": result.confidence,
                "method_used": result.method_used,
                "processing_time": result.processing_time,
                "image_path": result.image_path,
                "retry_count": result.retry_count,
                "error_message": result.error_message,
                "metadata": result.metadata or {}
            }
            
        except Exception as e:
            error_msg = f"图片识别节点执行异常: {e}"
            logger.error(error_msg)
            
            return {
                "success": False,
                "message": error_msg,
                "recognized_text": "",
                "confidence": 0.0,
                "method_used": "error",
                "processing_time": 0.0,
                "error_message": error_msg
            }
    
    def _parse_step_config(self, metadata: Dict[str, Any]) -> ImageRecognitionConfig:
        """解析步骤配置"""
        try:
            return ImageRecognitionConfig.from_dict(metadata.get("recognition_config", {}))
        except Exception as e:
            logger.warning(f"解析图片识别配置失败，使用默认配置: {e}")
            # 返回默认验证码识别配置
            return ImageRecognitionConfig(
                recognition_type=ImageRecognitionType.CAPTCHA,
                recognition_method=RecognitionMethod.OCR_THEN_AI,
                target_selectors=["img[alt*='验证码']", "img[src*='captcha']"],
                input_selectors=["input[name='captcha']", "input[placeholder*='验证码']"]
            )


class WorkflowImageRecognitionGenerator:
    """工作流图片识别生成器"""
    
    @staticmethod
    def create_captcha_recognition_step(
        step_id: str,
        target_selectors: List[str],
        input_selectors: List[str],
        recognition_method: RecognitionMethod = RecognitionMethod.OCR_THEN_AI,
        **kwargs
    ) -> Step:
        """创建验证码识别步骤"""
        
        config = ImageRecognitionConfig(
            recognition_type=ImageRecognitionType.CAPTCHA,
            recognition_method=recognition_method,
            target_selectors=target_selectors,
            input_selectors=input_selectors,
            **kwargs
        )
        
        return Step(
            id=step_id,
            type="image_recognition",
            description="验证码识别和填写",
            metadata={
                "recognition_config": config.to_dict(),
                "node_type": "captcha_recognition"
            }
        )
    
    @staticmethod
    def create_qr_code_recognition_step(
        step_id: str,
        target_selectors: List[str],
        recognition_method: RecognitionMethod = RecognitionMethod.AI_ONLY,
        **kwargs
    ) -> Step:
        """创建二维码识别步骤"""
        
        config = ImageRecognitionConfig(
            recognition_type=ImageRecognitionType.QR_CODE,
            recognition_method=recognition_method,
            target_selectors=target_selectors,
            input_selectors=[],  # 二维码通常不需要填写
            **kwargs
        )
        
        return Step(
            id=step_id,
            type="image_recognition",
            description="二维码识别",
            metadata={
                "recognition_config": config.to_dict(),
                "node_type": "qr_code_recognition"
            }
        )
    
    @staticmethod
    def create_text_ocr_step(
        step_id: str,
        target_selectors: List[str],
        input_selectors: List[str] = None,
        recognition_method: RecognitionMethod = RecognitionMethod.PARALLEL,
        **kwargs
    ) -> Step:
        """创建文本OCR识别步骤"""
        
        config = ImageRecognitionConfig(
            recognition_type=ImageRecognitionType.TEXT_OCR,
            recognition_method=recognition_method,
            target_selectors=target_selectors,
            input_selectors=input_selectors or [],
            **kwargs
        )
        
        return Step(
            id=step_id,
            type="image_recognition",
            description="文本OCR识别",
            metadata={
                "recognition_config": config.to_dict(),
                "node_type": "text_ocr"
            }
        )
    
    @staticmethod
    def create_custom_recognition_step(
        step_id: str,
        recognition_type: ImageRecognitionType,
        recognition_method: RecognitionMethod,
        target_selectors: List[str],
        input_selectors: List[str] = None,
        ai_prompt_template: str = "",
        validation_rules: Dict[str, Any] = None,
        **kwargs
    ) -> Step:
        """创建自定义识别步骤"""
        
        config = ImageRecognitionConfig(
            recognition_type=recognition_type,
            recognition_method=recognition_method,
            target_selectors=target_selectors,
            input_selectors=input_selectors or [],
            ai_prompt_template=ai_prompt_template,
            validation_rules=validation_rules or {},
            **kwargs
        )
        
        return Step(
            id=step_id,
            type="image_recognition",
            description=f"{recognition_type.value}识别",
            metadata={
                "recognition_config": config.to_dict(),
                "node_type": "custom_recognition"
            }
        )


class ImageRecognitionWorkflowTemplates:
    """图片识别工作流模板"""
    
    @staticmethod
    def get_captcha_login_workflow_template() -> Dict[str, Any]:
        """获取验证码登录工作流模板"""
        return {
            "name": "验证码登录工作流",
            "description": "包含验证码识别的完整登录工作流",
            "version": "1.0.0",
            "steps": [
                {
                    "id": "step_1",
                    "type": "navigate",
                    "description": "导航到登录页面",
                    "metadata": {
                        "url": "{{login_url}}"
                    }
                },
                {
                    "id": "step_2",
                    "type": "fill",
                    "description": "填写用户名",
                    "metadata": {
                        "selector": "{{username_selector}}",
                        "value": "{{username}}"
                    }
                },
                {
                    "id": "step_3",
                    "type": "fill",
                    "description": "填写密码",
                    "metadata": {
                        "selector": "{{password_selector}}",
                        "value": "{{password}}"
                    }
                },
                {
                    "id": "step_4",
                    "type": "image_recognition",
                    "description": "验证码识别和填写",
                    "metadata": {
                        "recognition_config": {
                            "recognition_type": "captcha",
                            "recognition_method": "ocr_then_ai",
                            "target_selectors": [
                                "img[alt*='验证码']",
                                "img[src*='captcha']",
                                "img[src*='verify']",
                                "#captcha_img",
                                ".captcha img"
                            ],
                            "input_selectors": [
                                "input[name='captcha']",
                                "input[placeholder*='验证码']",
                                "input[id*='captcha']"
                            ],
                            "ocr_confidence_threshold": 0.7,
                            "ai_confidence_threshold": 0.6,
                            "max_retry_count": 3,
                            "validation_rules": {
                                "min_length": 3,
                                "max_length": 8,
                                "alphanumeric_only": True
                            }
                        },
                        "node_type": "captcha_recognition"
                    }
                },
                {
                    "id": "step_5",
                    "type": "click",
                    "description": "点击登录按钮",
                    "metadata": {
                        "selector": "{{login_button_selector}}"
                    }
                },
                {
                    "id": "step_6",
                    "type": "verify",
                    "description": "验证登录结果",
                    "metadata": {
                        "success_indicators": [
                            "url_change",
                            "text=欢迎",
                            "text=首页",
                            ".user-info"
                        ],
                        "error_indicators": [
                            "text=用户名或密码错误",
                            "text=验证码错误",
                            ".error-message"
                        ]
                    }
                }
            ],
            "variables": {
                "login_url": "",
                "username_selector": "",
                "password_selector": "",
                "login_button_selector": "",
                "username": "",
                "password": ""
            }
        }
    
    @staticmethod
    def get_multi_image_recognition_workflow_template() -> Dict[str, Any]:
        """获取多图片识别工作流模板"""
        return {
            "name": "多图片识别工作流",
            "description": "支持多种图片识别场景的工作流",
            "version": "1.0.0",
            "steps": [
                {
                    "id": "step_1",
                    "type": "navigate",
                    "description": "导航到目标页面",
                    "metadata": {
                        "url": "{{target_url}}"
                    }
                },
                {
                    "id": "step_2",
                    "type": "image_recognition",
                    "description": "验证码识别",
                    "metadata": {
                        "recognition_config": {
                            "recognition_type": "captcha",
                            "recognition_method": "ocr_then_ai",
                            "target_selectors": ["{{captcha_image_selector}}"],
                            "input_selectors": ["{{captcha_input_selector}}"]
                        },
                        "node_type": "captcha_recognition"
                    }
                },
                {
                    "id": "step_3",
                    "type": "image_recognition",
                    "description": "二维码识别",
                    "metadata": {
                        "recognition_config": {
                            "recognition_type": "qr_code",
                            "recognition_method": "ai_only",
                            "target_selectors": ["{{qr_code_selector}}"],
                            "input_selectors": []
                        },
                        "node_type": "qr_code_recognition"
                    }
                },
                {
                    "id": "step_4",
                    "type": "image_recognition",
                    "description": "文档OCR识别",
                    "metadata": {
                        "recognition_config": {
                            "recognition_type": "document",
                            "recognition_method": "parallel",
                            "target_selectors": ["{{document_selector}}"],
                            "input_selectors": ["{{document_input_selector}}"]
                        },
                        "node_type": "document_ocr"
                    }
                }
            ],
            "variables": {
                "target_url": "",
                "captcha_image_selector": "",
                "captcha_input_selector": "",
                "qr_code_selector": "",
                "document_selector": "",
                "document_input_selector": ""
            }
        }
    
    @staticmethod
    def get_adaptive_recognition_workflow_template() -> Dict[str, Any]:
        """获取自适应识别工作流模板"""
        return {
            "name": "自适应图片识别工作流",
            "description": "根据页面内容自适应选择识别策略的工作流",
            "version": "1.0.0",
            "steps": [
                {
                    "id": "step_1",
                    "type": "navigate",
                    "description": "导航到目标页面",
                    "metadata": {
                        "url": "{{target_url}}"
                    }
                },
                {
                    "id": "step_2",
                    "type": "image_recognition",
                    "description": "自适应图片识别",
                    "metadata": {
                        "recognition_config": {
                            "recognition_type": "custom",
                            "recognition_method": "parallel",
                            "target_selectors": [
                                "img[alt*='验证码']",
                                "img[src*='captcha']",
                                "img[src*='qr']",
                                "img[src*='code']",
                                ".image-content img",
                                ".recognition-target"
                            ],
                            "input_selectors": [
                                "input[type='text']",
                                "textarea",
                                ".input-field"
                            ],
                            "ocr_confidence_threshold": 0.6,
                            "ai_confidence_threshold": 0.5,
                            "max_retry_count": 2,
                            "ai_prompt_template": "请识别这张图片中的内容，如果是验证码请返回字符，如果是二维码请返回链接或文本，如果是文档请返回文字内容。"
                        },
                        "node_type": "adaptive_recognition"
                    }
                }
            ],
            "variables": {
                "target_url": ""
            }
        }


# 全局节点实例
global_image_recognition_node = ImageRecognitionWorkflowNode()


def get_image_recognition_workflow_node() -> ImageRecognitionWorkflowNode:
    """获取图片识别工作流节点实例"""
    return global_image_recognition_node


# 便捷函数
def create_captcha_step(step_id: str, target_selectors: List[str], input_selectors: List[str]) -> Step:
    """便捷创建验证码识别步骤"""
    return WorkflowImageRecognitionGenerator.create_captcha_recognition_step(
        step_id, target_selectors, input_selectors
    )


def get_captcha_workflow_template() -> Dict[str, Any]:
    """便捷获取验证码工作流模板"""
    return ImageRecognitionWorkflowTemplates.get_captcha_login_workflow_template()
