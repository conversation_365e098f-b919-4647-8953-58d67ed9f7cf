# 测试用例

**版本**: v1.0 | **更新**: 2025-12-19

## 🧪 测试概览

本文档包含AI+RPA系统的完整测试用例，覆盖功能测试、集成测试和端到端测试。

## 📋 测试分类

### 1. 单元测试
- 基础操作类型测试
- 工作流引擎测试
- AI分析功能测试
- 变量系统测试

### 2. 集成测试
- browser-use集成测试
- AI智能交互测试
- 监控系统测试

### 3. 端到端测试
- 完整用户流程测试
- 异常处理测试
- 性能测试

## 🔧 单元测试用例

### TC001: 基础操作创建
**目标**: 验证基础操作对象创建
```python
def test_operation_creation():
    op = Operation(type="click", selector="#button")
    assert op.type == "click"
    assert op.selector == "#button"
    assert op.timeout == 30000  # 默认值
```

### TC002: 工作流引擎执行
**目标**: 验证工作流引擎基础执行
```python
@pytest.mark.asyncio
async def test_workflow_engine_basic():
    engine = WorkflowEngine()
    context = ExecutionContext()
    
    step = {"type": "navigate", "url": "https://example.com"}
    result = await engine.execute_step(step, context)
    
    assert result.status == "passed"
```

### TC003: 变量系统
**目标**: 验证变量设置和获取
```python
def test_variable_context():
    context = VariableContext()
    context.set_variable("test_var", "test_value")
    
    assert context.get_variable("test_var") == "test_value"
```

### TC004: 条件分支执行
**目标**: 验证if/else条件执行
```python
@pytest.mark.asyncio
async def test_conditional_execution():
    engine = WorkflowEngine()
    context = ExecutionContext()
    context.set_variable("condition", True)
    
    step = {
        "type": "if",
        "condition": "${condition}",
        "then": [{"type": "navigate", "url": "https://example.com"}],
        "else": [{"type": "navigate", "url": "https://google.com"}]
    }
    
    result = await engine.execute_step(step, context)
    assert result.status == "passed"
```

## 🔗 集成测试用例

### TC101: AI需求分析
**目标**: 验证AI需求分析功能
```python
@pytest.mark.asyncio
async def test_ai_requirement_analysis():
    analyzer = AIBusinessAnalyzer()
    
    user_input = "请帮我创建一个新客户"
    requirement = analyzer.analyze_user_requirement(user_input)
    
    assert requirement.parsed_intent == "create"
    assert "客户" in requirement.original_text
    assert requirement.confidence > 0.7
```

### TC102: 工作流匹配
**目标**: 验证工作流智能匹配
```python
def test_workflow_matching():
    analyzer = AIBusinessAnalyzer()
    requirement = UserRequirement(
        original_text="创建客户",
        parsed_intent="create",
        business_domain="客户管理",
        confidence=0.9
    )
    
    matches = analyzer.find_matching_workflows(requirement)
    assert len(matches) > 0
    assert matches[0].match_score > 0.8
```

### TC103: browser-use集成
**目标**: 验证browser-use真实集成
```python
@pytest.mark.asyncio
async def test_browser_use_integration():
    agent = get_real_browser_use_agent()
    
    # 需要配置API密钥
    if not os.getenv('OPENAI_API_KEY'):
        pytest.skip("需要OpenAI API密钥")
    
    result = await agent.execute_user_request(
        "请打开example.com网站"
    )
    
    assert result['success'] == True
    assert result['execution_method'] == "real_browser_use"
```

### TC104: 监控系统
**目标**: 验证实时监控功能
```python
@pytest.mark.asyncio
async def test_monitoring_system():
    monitor = BrowserUseMonitor()
    
    # 模拟页面对象
    mock_page = Mock()
    operation_info = {"type": "click", "selector": "#button"}
    
    # 启动监控
    await monitor.start_monitoring(mock_page, operation_info)
    
    # 验证监控状态
    assert monitor.is_monitoring == True
```

## 🎭 端到端测试用例

### TC201: 完整用户流程
**目标**: 验证从需求输入到执行完成的完整流程
```python
@pytest.mark.asyncio
async def test_complete_user_workflow():
    agent = get_real_browser_use_agent()
    
    # 用户输入
    user_input = "请帮我在Google上搜索'AI自动化'"
    
    # 执行完整流程
    result = await agent.execute_user_request(user_input)
    
    # 验证结果
    assert result['success'] == True
    assert result['parsed_intent'] in ["query", "search"]
    assert result['execution_time'] > 0
    assert 'parameters_used' in result
```

### TC202: 异常处理流程
**目标**: 验证异常检测和处理
```python
@pytest.mark.asyncio
async def test_exception_handling():
    agent = get_real_browser_use_agent()
    
    # 故意输入无效请求
    user_input = "请帮我访问不存在的网站 http://invalid-url-12345.com"
    
    result = await agent.execute_user_request(user_input)
    
    # 验证异常处理
    assert 'error' in result or result['success'] == False
```

### TC203: 参数收集流程
**目标**: 验证参数收集和验证
```python
@pytest.mark.asyncio
async def test_parameter_collection():
    manager = get_interaction_manager()
    
    # 启动需求分析会话
    session = manager.start_requirement_analysis_session(
        "请帮我创建客户信息"
    )
    
    # 提供参数
    parameters = {
        "customer_name": "测试公司",
        "contact_person": "张三",
        "phone": "13800138000"
    }
    
    success = manager.provide_user_parameters(session.session_id, parameters)
    assert success == True
```

## 📊 性能测试用例

### TC301: 响应时间测试
**目标**: 验证系统响应时间
```python
@pytest.mark.asyncio
async def test_response_time():
    agent = get_real_browser_use_agent()
    
    start_time = time.time()
    result = await agent.execute_user_request("简单的网页导航测试")
    end_time = time.time()
    
    execution_time = end_time - start_time
    assert execution_time < 30  # 30秒内完成
```

### TC302: 并发执行测试
**目标**: 验证并发执行能力
```python
@pytest.mark.asyncio
async def test_concurrent_execution():
    agent = get_real_browser_use_agent()
    
    # 创建多个并发任务
    tasks = []
    for i in range(3):
        task = agent.execute_user_request(f"测试任务 {i}")
        tasks.append(task)
    
    # 等待所有任务完成
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 验证结果
    successful_count = sum(1 for r in results if isinstance(r, dict) and r.get('success'))
    assert successful_count >= 2  # 至少2个成功
```

## 🔍 测试数据

### 测试用例数据
```python
TEST_CASES = [
    {
        "name": "简单网页导航",
        "input": "请打开example.com网站",
        "expected_intent": "navigate",
        "expected_success": True
    },
    {
        "name": "搜索功能",
        "input": "在Google上搜索AI自动化",
        "expected_intent": "search",
        "expected_success": True
    },
    {
        "name": "表单填写",
        "input": "填写联系表单",
        "expected_intent": "fill",
        "expected_success": True
    }
]
```

### 模拟数据
```python
MOCK_WORKFLOWS = [
    {
        "id": "customer_creation",
        "name": "客户创建工作流",
        "domain": "客户管理",
        "required_parameters": ["customer_name", "contact_person"]
    }
]
```

## 🚀 运行测试

### 运行所有测试
```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定类型测试
python -m pytest tests/unit/ -v
python -m pytest tests/integration/ -v
python -m pytest tests/e2e/ -v
```

### 运行覆盖率测试
```bash
python -m pytest --cov=src tests/ --cov-report=html
```

### 运行性能测试
```bash
python -m pytest tests/performance/ -v --durations=10
```

## 📈 测试报告

### 覆盖率目标
- **单元测试**: >80%
- **集成测试**: >70%
- **端到端测试**: >60%

### 成功率目标
- **功能测试**: >95%
- **性能测试**: >90%
- **稳定性测试**: >85%

---

> 🧪 **测试策略**: 采用金字塔测试策略，重点关注单元测试，适量集成测试，少量端到端测试
