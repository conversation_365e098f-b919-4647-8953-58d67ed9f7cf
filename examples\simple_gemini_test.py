"""
简化的Gemini测试

快速验证Gemini配置和基础功能
"""
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def test_gemini_import():
    """测试Gemini导入"""
    print("🧪 测试Gemini导入")
    print("-" * 20)
    
    try:
        import google.generativeai as genai
        print("✅ google-generativeai 导入成功")
        return True
    except ImportError as e:
        print(f"❌ google-generativeai 导入失败: {e}")
        print("💡 请安装: pip install google-generativeai")
        return False


def test_gemini_config():
    """测试Gemini配置"""
    print("\n🧪 测试Gemini配置")
    print("-" * 20)
    
    # 检查API密钥
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY 环境变量未设置")
        return False
    
    print(f"✅ GEMINI_API_KEY 已设置 (长度: {len(api_key)})")
    
    # 检查模型配置
    model = os.getenv('GEMINI_MODEL', 'gemini-pro')
    print(f"✅ GEMINI_MODEL: {model}")
    
    return True


def test_gemini_basic():
    """测试Gemini基础功能"""
    print("\n🧪 测试Gemini基础功能")
    print("-" * 20)
    
    try:
        import google.generativeai as genai
        
        # 配置API密钥
        api_key = os.getenv('GEMINI_API_KEY')
        genai.configure(api_key=api_key)
        
        # 创建模型
        model_name = os.getenv('GEMINI_MODEL', 'gemini-pro')
        model = genai.GenerativeModel(model_name)
        
        print(f"✅ Gemini模型创建成功: {model_name}")
        
        # 测试简单生成
        print("🔍 测试简单文本生成...")
        response = model.generate_content("请说'Hello from Gemini'")
        
        print(f"✅ 生成成功")
        print(f"📝 响应: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini基础功能测试失败: {e}")
        return False


def test_llm_manager():
    """测试LLM管理器中的Gemini"""
    print("\n🧪 测试LLM管理器中的Gemini")
    print("-" * 30)
    
    try:
        from ai_llm_manager import get_llm_manager, LLMProvider
        
        manager = get_llm_manager()
        available_providers = manager.get_available_providers()
        
        print(f"可用提供商: {[p.value for p in available_providers]}")
        
        if LLMProvider.GEMINI in available_providers:
            print("✅ Gemini在LLM管理器中可用")
            return True
        else:
            print("❌ Gemini在LLM管理器中不可用")
            return False
            
    except Exception as e:
        print(f"❌ LLM管理器测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🎭 简化Gemini测试")
    print("快速验证Gemini配置和功能")
    print("=" * 40)
    
    results = {}
    
    # 运行测试
    results["Gemini导入"] = test_gemini_import()
    results["Gemini配置"] = test_gemini_config()
    
    # 只有前面的测试通过才继续
    if results["Gemini导入"] and results["Gemini配置"]:
        results["Gemini基础功能"] = test_gemini_basic()
        results["LLM管理器"] = test_llm_manager()
    else:
        print("\n❌ 基础配置有问题，跳过后续测试")
        results["Gemini基础功能"] = False
        results["LLM管理器"] = False
    
    # 生成报告
    print("\n" + "=" * 40)
    print("📊 Gemini测试报告")
    print("=" * 40)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！Gemini配置正确！")
        print(f"🚀 可以继续进行完整的端到端测试")
    elif passed_tests >= 2:
        print(f"\n🔄 基础配置正确，但部分功能有问题")
        print(f"💡 建议检查网络连接和API密钥权限")
    else:
        print(f"\n❌ 基础配置有问题")
        print(f"💡 请检查依赖安装和环境变量配置")
    
    print(f"\n📋 下一步:")
    if passed_tests == total_tests:
        print("   1. 运行完整测试: python examples/gemini_browser_use_test.py")
        print("   2. 开始使用Gemini进行browser-use自动化")
    else:
        print("   1. 安装依赖: pip install google-generativeai")
        print("   2. 检查API密钥配置")
        print("   3. 重新运行此测试")


if __name__ == "__main__":
    main()
