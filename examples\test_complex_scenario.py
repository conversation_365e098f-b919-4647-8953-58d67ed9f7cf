"""
复杂场景测试

测试协助登录 → 页面分析 → 操作数据生成 → 智能导航的完整流程
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"✅ 加载环境变量: {key}")


def test_imports():
    """测试导入"""
    print("🧪 测试复杂场景模块导入")
    print("-" * 40)
    
    try:
        from page_operation_analyzer import (
            PageOperationAnalyzer,
            PageOperationManager,
            PageLink,
            PageOperationData
        )
        print("   ✅ page_operation_analyzer 导入成功")
        
        from intelligent_page_navigator import (
            IntelligentPageNavigator,
            NavigationRequest,
            NavigationResult
        )
        print("   ✅ intelligent_page_navigator 导入成功")
        
        from complex_scenario_workflow import (
            ComplexScenarioWorkflow,
            ComplexScenarioSession
        )
        print("   ✅ complex_scenario_workflow 导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        return False


async def test_page_operation_analyzer():
    """测试页面操作分析器"""
    print("\n🧪 测试页面操作分析器")
    print("-" * 40)
    
    try:
        from page_operation_analyzer import get_page_operation_analyzer
        from playwright.async_api import async_playwright
        
        analyzer = get_page_operation_analyzer()
        print("   ✅ 页面操作分析器创建成功")
        
        print("   🌐 启动浏览器进行测试...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            
            try:
                # 访问测试页面
                test_url = "https://test.yushanyun.net/ac/web/"
                print(f"   📱 访问测试页面: {test_url}")
                
                await page.goto(test_url)
                await page.wait_for_load_state('networkidle')
                
                # 分析页面操作
                print("   🔍 开始分析页面操作...")
                operation_data = await analyzer.analyze_page_operations(page)
                
                print(f"   📊 页面分析结果:")
                print(f"      页面标题: {operation_data.title}")
                print(f"      页面URL: {operation_data.url}")
                print(f"      发现链接: {len(operation_data.links)} 个")
                print(f"      功能区域: {len(operation_data.functional_areas)} 个")
                print(f"      AI摘要: {operation_data.ai_summary}")
                
                # 显示前5个链接
                print(f"   🔗 主要链接:")
                for i, link in enumerate(operation_data.links[:5], 1):
                    print(f"      {i}. {link.text} ({link.category}) - 优先级: {link.priority}")
                    print(f"         描述: {link.description}")
                
                # 显示功能区域
                print(f"   🏗️ 功能区域:")
                for area, items in operation_data.functional_areas.items():
                    print(f"      {area}: {items}")
                
                input("   👀 请查看浏览器中的页面，按回车继续...")
                
                return True
                
            finally:
                await browser.close()
        
    except Exception as e:
        print(f"   ❌ 页面操作分析器测试失败: {e}")
        return False


async def test_intelligent_navigator():
    """测试智能页面导航器"""
    print("\n🧪 测试智能页面导航器")
    print("-" * 40)
    
    try:
        from intelligent_page_navigator import get_intelligent_page_navigator
        from page_operation_analyzer import get_page_operation_analyzer
        from playwright.async_api import async_playwright
        
        navigator = get_intelligent_page_navigator()
        analyzer = get_page_operation_analyzer()
        
        print("   ✅ 智能页面导航器创建成功")
        
        print("   🌐 启动浏览器进行导航测试...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            
            try:
                # 访问测试页面
                test_url = "https://test.yushanyun.net/ac/web/"
                await page.goto(test_url)
                await page.wait_for_load_state('networkidle')
                
                # 分析页面
                print("   🔍 分析页面操作...")
                operation_data = await analyzer.analyze_page_operations(page)
                
                # 测试智能导航
                user_requirements = [
                    "我想查看用户管理",
                    "我要进入系统设置",
                    "我想看帮助文档",
                    "我要查看数据统计"
                ]
                
                print(f"   🎯 测试智能导航:")
                
                for requirement in user_requirements:
                    print(f"\n   📋 用户需求: {requirement}")
                    
                    # 查找目标页面
                    navigation_result = await navigator.find_target_pages(operation_data, requirement)
                    
                    if navigation_result.matched_links:
                        print(f"      ✅ 找到 {len(navigation_result.matched_links)} 个匹配链接")
                        
                        best_link = navigation_result.matched_links[0]
                        best_confidence = navigation_result.confidence_scores[0]
                        
                        print(f"      🎯 最佳匹配: {best_link.text}")
                        print(f"      📊 置信度: {best_confidence:.2f}")
                        print(f"      💡 AI推理: {navigation_result.ai_reasoning}")
                        print(f"      🔧 推荐操作: {navigation_result.recommended_action}")
                    else:
                        print(f"      ❌ 未找到匹配的链接")
                
                # 测试实际导航
                test_requirement = input("\n   请输入您的导航需求 (或按回车跳过): ").strip()
                
                if test_requirement:
                    print(f"   🚀 执行导航: {test_requirement}")
                    
                    nav_result = await navigator.auto_navigate_by_requirement(
                        page, operation_data, test_requirement
                    )
                    
                    if nav_result["success"]:
                        print(f"      ✅ 导航成功")
                        print(f"      📍 当前URL: {nav_result['current_url']}")
                        print(f"      📄 当前标题: {nav_result['current_title']}")
                        print(f"      🎯 选择的链接: {nav_result['selected_link']['text']}")
                        print(f"      📊 置信度: {nav_result['confidence']:.2f}")
                    else:
                        print(f"      ❌ 导航失败: {nav_result['message']}")
                
                input("   👀 请查看导航结果，按回车继续...")
                
                return True
                
            finally:
                await browser.close()
        
    except Exception as e:
        print(f"   ❌ 智能页面导航器测试失败: {e}")
        return False


async def test_complex_scenario_workflow():
    """测试复杂场景工作流"""
    print("\n🧪 测试复杂场景工作流")
    print("-" * 40)
    
    try:
        from complex_scenario_workflow import get_complex_scenario_workflow
        from ai_login_workflow_generator import get_login_state_executor
        
        workflow = get_complex_scenario_workflow()
        login_executor = get_login_state_executor()
        
        print("   ✅ 复杂场景工作流创建成功")
        
        # 检查可用的登录会话
        print("   🔍 检查可用的登录会话...")
        sessions = await login_executor.list_available_sessions()
        valid_sessions = [s for s in sessions if s["is_valid"]]
        
        if not valid_sessions:
            print("   ❌ 没有可用的登录会话，请先完成登录")
            return False
        
        print(f"   📋 找到 {len(valid_sessions)} 个有效登录会话:")
        for i, session in enumerate(valid_sessions[:3], 1):
            print(f"      {i}. {session['name']} ({session['domain']})")
        
        # 选择登录会话
        choice = input(f"\n   选择登录会话 (1-{len(valid_sessions)}, 或按回车使用第一个): ").strip()
        
        if choice and choice.isdigit():
            session_index = int(choice) - 1
            if 0 <= session_index < len(valid_sessions):
                selected_session = valid_sessions[session_index]
            else:
                selected_session = valid_sessions[0]
        else:
            selected_session = valid_sessions[0]
        
        print(f"   ✅ 选择会话: {selected_session['name']}")
        
        # 启动复杂场景
        scenario_name = input("   复杂场景名称 (默认: 测试场景): ").strip()
        if not scenario_name:
            scenario_name = "测试场景"
        
        print(f"   🚀 启动复杂场景: {scenario_name}")
        
        start_result = await workflow.start_complex_scenario(
            session_name=scenario_name,
            login_session_id=selected_session["session_id"],
            description="复杂场景测试"
        )
        
        if start_result["success"]:
            print(f"   ✅ 复杂场景启动成功")
            print(f"      场景会话ID: {start_result['scenario_session_id']}")
            print(f"      当前URL: {start_result['current_url']}")
            print(f"      当前标题: {start_result['current_title']}")
            print(f"      发现操作: {start_result['page_analysis']['links_count']} 个")
            print(f"      AI摘要: {start_result['page_analysis']['ai_summary']}")
            
            # 显示可用操作
            print(f"\n   🔧 可用操作:")
            for i, op in enumerate(start_result["available_operations"][:5], 1):
                print(f"      {i}. {op['text']} ({op['category']}) - 优先级: {op['priority']}")
            
            # 测试智能导航
            while True:
                print(f"\n   🎯 智能导航测试:")
                user_requirement = input("   请输入您的需求 (或输入'quit'退出): ").strip()
                
                if user_requirement.lower() in ['quit', 'exit', 'q']:
                    break
                
                if user_requirement:
                    print(f"   🚀 执行智能导航: {user_requirement}")
                    
                    nav_result = await workflow.navigate_by_requirement(user_requirement)
                    
                    if nav_result["success"]:
                        print(f"      ✅ 导航成功")
                        print(f"      📍 当前URL: {nav_result['current_url']}")
                        print(f"      📄 当前标题: {nav_result['current_title']}")
                        print(f"      🎯 选择的链接: {nav_result['selected_link']['text']}")
                        print(f"      📊 置信度: {nav_result['confidence']:.2f}")
                        print(f"      💡 AI推理: {nav_result['ai_reasoning']}")
                        
                        # 显示替代选项
                        if nav_result.get("alternative_options"):
                            print(f"      🔄 其他选项:")
                            for alt in nav_result["alternative_options"]:
                                print(f"         - {alt['link']['text']} (置信度: {alt['confidence']:.2f})")
                    else:
                        print(f"      ❌ 导航失败: {nav_result['message']}")
            
            # 关闭场景
            print(f"\n   🔚 关闭复杂场景...")
            close_result = await workflow.close_scenario()
            
            if close_result["success"]:
                print(f"   ✅ 复杂场景已关闭")
            else:
                print(f"   ❌ 关闭失败: {close_result['message']}")
            
            return True
        else:
            print(f"   ❌ 复杂场景启动失败: {start_result['message']}")
            return False
        
    except Exception as e:
        print(f"   ❌ 复杂场景工作流测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🎭 复杂场景系统测试")
    print("协助登录 → 页面分析 → 操作数据生成 → 智能导航")
    print("=" * 70)
    
    # 加载环境变量
    load_env()
    
    # 检查AI配置
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        print("❌ 缺少GEMINI_API_KEY配置")
        return
    
    print("✅ AI配置检查通过")
    
    # 运行测试
    tests = [
        ("模块导入", test_imports),
        ("页面操作分析器", test_page_operation_analyzer),
        ("智能页面导航器", test_intelligent_navigator),
        ("复杂场景工作流", test_complex_scenario_workflow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*70}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                results[test_name] = await test_func()
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
        
        # 询问是否继续
        if test_name != tests[-1][0]:  # 不是最后一个测试
            continue_test = input(f"\n继续下一个测试? (y/n): ").strip().lower()
            if continue_test not in ['y', 'yes']:
                break
    
    # 生成测试报告
    print("\n" + "=" * 70)
    print("📊 复杂场景系统测试报告")
    print("=" * 70)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！复杂场景系统准备就绪！")
        print(f"\n🚀 系统功能:")
        print(f"   ✅ 协助登录 - 使用缓存登录状态快速进入")
        print(f"   ✅ 页面分析 - AI分析页面结构和操作链接")
        print(f"   ✅ 数据生成 - 生成结构化的操作基础数据")
        print(f"   ✅ 智能导航 - 根据用户要求自动调整和打开页面")
        
        print(f"\n🎯 复杂场景优势:")
        print(f"   • 一次登录，持续操作")
        print(f"   • AI智能理解用户需求")
        print(f"   • 自动发现页面操作选项")
        print(f"   • 智能匹配和导航")
        print(f"   • 完整的操作历史记录")
        
        print(f"\n🏆 现在您可以:")
        print(f"   • 快速进入任何已登录的系统")
        print(f"   • 自动分析页面的所有操作选项")
        print(f"   • 用自然语言描述需求进行导航")
        print(f"   • 智能发现和访问系统功能")
        
    else:
        print(f"\n❌ 部分测试失败，需要检查配置")


if __name__ == "__main__":
    asyncio.run(main())
