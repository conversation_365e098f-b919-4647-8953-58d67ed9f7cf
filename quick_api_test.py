"""
快速API测试脚本
"""
import requests
import json
import time

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 健康检查通过")
            print(f"   状态: {data.get('status')}")
            print(f"   组件: {data.get('components')}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_chat_api():
    """测试聊天API"""
    print("\n💬 测试聊天API...")
    try:
        data = {
            "content": "测试消息",
            "type": "user"
        }
        
        print("发送测试消息...")
        start_time = time.time()
        response = requests.post(
            "http://localhost:8000/api/chat/message", 
            json=data, 
            timeout=10
        )
        end_time = time.time()
        
        print(f"响应时间: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 聊天API测试通过")
            print(f"   响应: {result.get('message')}")
            print(f"   成功: {result.get('success')}")
            print(f"   置信度: {result.get('confidence')}")
            print(f"   命令类型: {result.get('commandType')}")
            return True
        else:
            print(f"❌ 聊天API失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 聊天API异常: {e}")
        return False

def test_workflows_api():
    """测试工作流API"""
    print("\n📋 测试工作流API...")
    try:
        response = requests.get("http://localhost:8000/api/workflows", timeout=5)
        if response.status_code == 200:
            data = response.json()
            workflows = data.get('workflows', [])
            print("✅ 工作流API测试通过")
            print(f"   工作流数量: {len(workflows)}")
            
            for i, workflow in enumerate(workflows[:3]):  # 显示前3个
                print(f"   {i+1}. {workflow.get('name')} ({workflow.get('domain')})")
            
            return True
        else:
            print(f"❌ 工作流API失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 工作流API异常: {e}")
        return False

def test_multiple_chat_messages():
    """测试多个聊天消息"""
    print("\n🔄 测试多个聊天消息...")
    
    test_messages = [
        "系统状态",
        "打开百度",
        "今天天气",
        "分析页面",
        "帮助"
    ]
    
    success_count = 0
    
    for i, message in enumerate(test_messages):
        print(f"\n测试消息 {i+1}: {message}")
        try:
            data = {"content": message, "type": "user"}
            start_time = time.time()
            response = requests.post(
                "http://localhost:8000/api/chat/message", 
                json=data, 
                timeout=8
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 成功 ({end_time - start_time:.2f}s)")
                print(f"   响应: {result.get('message')[:50]}...")
                success_count += 1
            else:
                print(f"   ❌ 失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    print(f"\n📊 多消息测试结果: {success_count}/{len(test_messages)} 成功")
    return success_count == len(test_messages)

def main():
    """主测试函数"""
    print("🧪 快速API测试")
    print("=" * 40)
    
    tests = [
        ("健康检查", test_health),
        ("聊天API", test_chat_api),
        ("工作流API", test_workflows_api),
        ("多消息测试", test_multiple_chat_messages)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*40}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！API修复成功！")
    else:
        print(f"⚠️ {total - passed} 个测试失败")
    
    print("\n💡 现在可以在前端界面中测试:")
    print("   - 打开: file:///D:/dev/AI/playwright/frontend_simple/index.html")
    print("   - 输入各种命令测试AI助手")

if __name__ == "__main__":
    main()
