"""
真实世界Gemini测试

测试Gemini在实际browser-use场景中的表现
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))

# 设置环境变量（从.env文件读取）
def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"设置环境变量: {key}")


async def test_gemini_real_scenario():
    """测试Gemini真实场景"""
    print("🧪 测试Gemini真实场景")
    print("=" * 40)
    
    # 加载环境变量
    load_env()
    
    # 检查Gemini配置
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        print("❌ GEMINI_API_KEY未设置")
        return False
    
    print(f"✅ Gemini API密钥已设置 (长度: {len(gemini_key)})")
    
    try:
        # 1. 测试LLM管理器
        print("\n1. 测试LLM管理器:")
        from ai_llm_manager import get_llm_manager, LLMProvider
        
        manager = get_llm_manager()
        available_providers = manager.get_available_providers()
        print(f"   可用提供商: {[p.value for p in available_providers]}")
        
        if LLMProvider.GEMINI not in available_providers:
            print("   ❌ Gemini不在可用提供商中")
            return False
        
        print("   ✅ Gemini可用")
        
        # 2. 测试Gemini直接调用
        print("\n2. 测试Gemini直接调用:")
        response = await manager.generate(
            "请简单介绍一下你自己，限制在50字以内", 
            provider=LLMProvider.GEMINI
        )
        print(f"   ✅ 调用成功")
        print(f"   📝 响应: {response.content}")
        print(f"   📊 模型: {response.model}")
        
        # 3. 测试browser-use适配器
        print("\n3. 测试browser-use适配器:")
        from browser_use_llm_adapter import create_browser_use_llm
        
        adapter = create_browser_use_llm(provider="gemini")
        adapter_response = await adapter.ainvoke("请说'Hello from browser-use adapter'")
        print(f"   ✅ 适配器调用成功")
        print(f"   📝 响应: {adapter_response.content}")
        
        # 4. 测试真实browser-use集成
        print("\n4. 测试真实browser-use集成:")
        from real_browser_use_integration import create_multi_llm_agent
        
        agent = create_multi_llm_agent("gemini")
        
        if not agent._check_prerequisites():
            print("   ❌ 前提条件检查失败")
            return False
        
        print("   ✅ 前提条件检查通过")
        
        # 创建LLM
        llm = agent._create_llm()
        print(f"   ✅ LLM创建成功: {llm.model}")
        
        # 5. 测试AI需求分析
        print("\n5. 测试AI需求分析:")
        test_request = "请帮我打开百度网站并搜索人工智能"
        
        session = agent.interaction_manager.start_requirement_analysis_session(test_request)
        print(f"   ✅ 需求分析成功")
        print(f"   📝 解析意图: {session.user_requirement.parsed_intent}")
        print(f"   🏢 业务领域: {session.user_requirement.business_domain}")
        print(f"   📊 置信度: {session.user_requirement.confidence:.2f}")
        
        # 6. 测试参数收集（如果需要）
        if session.user_requirement.requires_parameters:
            print("\n6. 测试参数收集:")
            print(f"   需要参数: {session.user_requirement.required_parameters}")
            
            # 模拟参数收集
            collected_params = {}
            for param in session.user_requirement.required_parameters:
                if param == "search_query":
                    collected_params[param] = "人工智能"
                elif param == "website_url":
                    collected_params[param] = "https://www.baidu.com"
            
            if collected_params:
                print(f"   ✅ 参数收集完成: {collected_params}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_gemini_performance():
    """测试Gemini性能"""
    print("\n🧪 测试Gemini性能")
    print("=" * 40)
    
    try:
        from ai_llm_manager import get_llm_manager, LLMProvider
        import time
        
        manager = get_llm_manager()
        
        # 测试不同类型的任务
        test_cases = [
            ("简单问答", "1+1等于几？"),
            ("中文理解", "请用中文解释什么是人工智能"),
            ("任务分析", "请分析这个任务：打开网站并填写表单"),
            ("代码理解", "请解释这段代码的作用：print('Hello World')"),
        ]
        
        results = []
        
        for task_name, prompt in test_cases:
            print(f"\n🔍 测试 {task_name}:")
            
            start_time = time.time()
            response = await manager.generate(prompt, provider=LLMProvider.GEMINI)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            print(f"   ✅ 响应时间: {response_time:.2f}秒")
            print(f"   📝 响应: {response.content[:100]}...")
            
            results.append({
                "task": task_name,
                "response_time": response_time,
                "response_length": len(response.content),
                "success": True
            })
        
        # 性能统计
        avg_response_time = sum(r["response_time"] for r in results) / len(results)
        print(f"\n📊 性能统计:")
        print(f"   平均响应时间: {avg_response_time:.2f}秒")
        print(f"   最快响应: {min(r['response_time'] for r in results):.2f}秒")
        print(f"   最慢响应: {max(r['response_time'] for r in results):.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🎭 真实世界Gemini测试")
    print("验证Gemini在实际AI+RPA场景中的表现")
    print("=" * 60)
    
    results = {}
    
    # 运行测试
    results["真实场景测试"] = await test_gemini_real_scenario()
    
    if results["真实场景测试"]:
        results["性能测试"] = await test_gemini_performance()
    else:
        results["性能测试"] = False
    
    # 生成报告
    print("\n" + "=" * 60)
    print("📊 真实世界Gemini测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！Gemini已准备好用于生产环境！")
        print(f"\n💡 下一步建议:")
        print(f"   1. 开始使用Gemini执行实际的browser-use任务")
        print(f"   2. 根据需要调整模型参数")
        print(f"   3. 监控使用情况和性能")
        
        print(f"\n🚀 使用示例:")
        print(f"   # 使用Gemini执行任务")
        print(f"   agent = get_real_browser_use_agent(llm_provider='gemini')")
        print(f"   result = await agent.execute_user_request('您的任务')")
        
    else:
        print(f"\n❌ 部分测试失败")
        print(f"💡 请检查:")
        print(f"   1. Gemini API密钥是否有效")
        print(f"   2. 网络连接是否正常")
        print(f"   3. 依赖是否正确安装")


if __name__ == "__main__":
    asyncio.run(main())
