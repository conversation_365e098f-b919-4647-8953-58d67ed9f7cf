'use client';

import * as React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';

/**
 * @ignore - internal component.
 */
import { jsx as _jsx } from "react/jsx-runtime";
function Ripple(props) {
  var className = props.className,
    classes = props.classes,
    _props$pulsate = props.pulsate,
    pulsate = _props$pulsate === void 0 ? false : _props$pulsate,
    rippleX = props.rippleX,
    rippleY = props.rippleY,
    rippleSize = props.rippleSize,
    inProp = props.in,
    onExited = props.onExited,
    timeout = props.timeout;
  var _React$useState = React.useState(false),
    leaving = _React$useState[0],
    setLeaving = _React$useState[1];
  var rippleClassName = clsx(className, classes.ripple, classes.rippleVisible, pulsate && classes.ripplePulsate);
  var rippleStyles = {
    width: rippleSize,
    height: rippleSize,
    top: -(rippleSize / 2) + rippleY,
    left: -(rippleSize / 2) + rippleX
  };
  var childClassName = clsx(classes.child, leaving && classes.childLeaving, pulsate && classes.childPulsate);
  if (!inProp && !leaving) {
    setLeaving(true);
  }
  React.useEffect(function () {
    if (!inProp && onExited != null) {
      // react-transition-group#onExited
      var timeoutId = setTimeout(onExited, timeout);
      return function () {
        clearTimeout(timeoutId);
      };
    }
    return undefined;
  }, [onExited, inProp, timeout]);
  return /*#__PURE__*/_jsx("span", {
    className: rippleClassName,
    style: rippleStyles,
    children: /*#__PURE__*/_jsx("span", {
      className: childClassName
    })
  });
}
process.env.NODE_ENV !== "production" ? Ripple.propTypes = {
  /**
   * Override or extend the styles applied to the component.
   */
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  /**
   * @ignore - injected from TransitionGroup
   */
  in: PropTypes.bool,
  /**
   * @ignore - injected from TransitionGroup
   */
  onExited: PropTypes.func,
  /**
   * If `true`, the ripple pulsates, typically indicating the keyboard focus state of an element.
   */
  pulsate: PropTypes.bool,
  /**
   * Diameter of the ripple.
   */
  rippleSize: PropTypes.number,
  /**
   * Horizontal position of the ripple center.
   */
  rippleX: PropTypes.number,
  /**
   * Vertical position of the ripple center.
   */
  rippleY: PropTypes.number,
  /**
   * exit delay
   */
  timeout: PropTypes.number.isRequired
} : void 0;
export default Ripple;