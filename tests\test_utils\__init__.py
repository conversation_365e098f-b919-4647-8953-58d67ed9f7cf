"""
测试工具模块
"""
from pathlib import Path
from typing import Any, Dict, Optional
import json
import time

def get_test_data_path(filename: str) -> str:
    """获取测试数据文件的完整路径"""
    test_data_dir = Path(__file__).parent.parent / "test_data"
    file_path = test_data_dir / filename
    
    # 转换为文件URL格式（处理Windows路径）
    file_url = f"file:///{file_path.absolute().as_posix().replace('//', '/').replace(':/', ':/').lstrip('/')}"
    return file_url

def wait_for_condition(page, condition: str, timeout: float = 10) -> bool:
    """等待页面条件成立"""
    try:
        page.wait_for_function(f"() => {{ return {condition}; }}", timeout=timeout * 1000)
        return True
    except Exception:
        return False

def get_console_logs(page) -> list:
    """获取控制台日志"""
    return page.evaluate('''() => {
        return window.consoleMessages || [];
    }''')

def get_form_data(page, form_selector: str = "form") -> Dict[str, Any]:
    """获取表单数据"""
    return page.evaluate(f'''(selector) => {{
        const form = document.querySelector(selector);
        if (!form) return {{ error: 'Form not found' }};
        
        const formData = new FormData(form);
        const data = {{}};
        
        // 处理常规输入字段
        formData.forEach((value, key) => {{
            if (data[key] === undefined) {{
                data[key] = value;
            }} else if (Array.isArray(data[key])) {{
                data[key].push(value);
            }} else {{
                data[key] = [data[key], value];
            }}
        }});
        
        // 处理复选框和单选按钮
        document.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(input => {{
            const name = input.name;
            if (name && input.checked) {{
                const value = input.value || 'on';
                if (data[name] === undefined) {{
                    data[name] = value;
                }} else if (Array.isArray(data[name])) {{
                    if (!data[name].includes(value)) data[name].push(value);
                }} else if (data[name] !== value) {{
                    data[name] = [data[name], value];
                }}
            }}
        }});
        
        return data;
    }}''', form_selector)

def save_debug_info(page, test_name: str, base_dir: Optional[Path] = None):
    """保存调试信息"""
    if base_dir is None:
        base_dir = Path(__file__).parent.parent / "debug"
    
    base_dir.mkdir(exist_ok=True, parents=True)
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    try:
        # 保存页面截图
        screenshot_path = base_dir / f"{test_name}_{timestamp}.png"
        page.screenshot(path=str(screenshot_path))
        
        # 保存页面HTML
        html_path = base_dir / f"{test_name}_{timestamp}.html"
        with open(html_path, "w", encoding="utf-8") as f:
            f.write(page.content())
            
        # 保存控制台日志
        logs = get_console_logs(page)
        logs_path = base_dir / f"{test_name}_{timestamp}_logs.json"
        with open(logs_path, "w", encoding="utf-8") as f:
            json.dump(logs, f, ensure_ascii=False, indent=2)
            
        print(f"调试信息已保存到: {base_dir}")
        return True
    except Exception as e:
        print(f"保存调试信息失败: {e}")
        return False
