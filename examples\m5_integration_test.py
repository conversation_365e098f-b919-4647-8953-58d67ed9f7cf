"""
M5里程碑集成测试

测试browser-use集成、browser-tools-mcp集成和OCR服务集成
"""
import asyncio
import os
import sys
import time
from pathlib import Path
from typing import Dict

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)
sys.path.insert(0, os.path.join(parent_dir, 'src'))

try:
    from real_browser_use_integration import get_real_browser_use_agent
    from enhanced_ocr_integration import get_enhanced_ocr_analyzer
except ImportError:
    # 尝试从src目录导入
    sys.path.insert(0, os.path.join(parent_dir, 'src'))
    from real_browser_use_integration import get_real_browser_use_agent
    from enhanced_ocr_integration import get_enhanced_ocr_analyzer


async def test_browser_use_integration():
    """测试browser-use集成"""
    print("\n" + "="*60)
    print("🧪 测试browser-use集成")
    print("="*60)
    
    try:
        # 获取代理
        agent = get_real_browser_use_agent()
        
        # 检查环境
        print("🔍 环境检查:")
        api_key = os.getenv('OPENAI_API_KEY')
        if api_key:
            print(f"   ✅ OpenAI API密钥: 已配置")
        else:
            print(f"   ❌ OpenAI API密钥: 未配置")
            return False
        
        # 检查前提条件
        if not agent._check_prerequisites():
            print("   ❌ 前提条件检查失败")
            return False
        
        print("   ✅ 前提条件检查通过")
        
        # 执行简单测试
        print("\n📋 执行测试用例:")
        test_input = "请帮我打开example.com网站"
        print(f"   输入: {test_input}")
        
        start_time = time.time()
        result = await agent.execute_user_request(test_input)
        execution_time = time.time() - start_time
        
        print(f"\n📊 测试结果:")
        print(f"   成功: {'✅' if result['success'] else '❌'}")
        print(f"   执行时间: {execution_time:.2f}秒")
        print(f"   意图识别: {result.get('parsed_intent', 'unknown')}")
        print(f"   置信度: {result.get('confidence', 0):.2f}")
        
        if not result['success']:
            print(f"   错误: {result.get('error', '未知错误')}")
        
        return result['success']
        
    except Exception as e:
        print(f"❌ browser-use集成测试失败: {e}")
        return False


async def test_browser_tools_mcp_integration():
    """测试browser-tools-mcp集成"""
    print("\n" + "="*60)
    print("🧪 测试browser-tools-mcp集成")
    print("="*60)
    
    try:
        # 动态导入以避免依赖问题
        try:
            from src.browser_tools_mcp_integration import (
                get_browser_tools_mcp_monitor,
                get_browser_tools_mcp_controller
            )
        except ImportError as e:
            print(f"❌ 导入失败: {e}")
            return False
        
        monitor = get_browser_tools_mcp_monitor()
        controller = get_browser_tools_mcp_controller()
        
        print("🔍 环境检查:")
        
        # 检查Node.js环境
        import shutil
        node_available = shutil.which('node') is not None
        npm_available = shutil.which('npm') is not None
        
        print(f"   Node.js: {'✅' if node_available else '❌'}")
        print(f"   npm: {'✅' if npm_available else '❌'}")
        
        if not (node_available and npm_available):
            print("   💡 请安装Node.js环境")
            return False
        
        # 测试监控功能
        print("\n📋 测试监控功能:")
        try:
            success = await monitor.start_monitoring("https://example.com")
            if success:
                print("   ✅ 监控启动成功")
                await asyncio.sleep(2)  # 监控2秒
                await monitor.stop_monitoring()
                print("   ✅ 监控停止成功")
            else:
                print("   ❌ 监控启动失败")
                return False
        except Exception as e:
            print(f"   ❌ 监控测试失败: {e}")
            return False
        
        # 测试控制功能
        print("\n📋 测试控制功能:")
        try:
            if await controller.connect():
                print("   ✅ 控制器连接成功")
                
                result = await controller.execute_command("test", {"param": "value"})
                if result["success"]:
                    print("   ✅ 命令执行成功")
                else:
                    print(f"   ❌ 命令执行失败: {result.get('error')}")
                
                await controller.disconnect()
                print("   ✅ 控制器断开成功")
                return True
            else:
                print("   ❌ 控制器连接失败")
                return False
        except Exception as e:
            print(f"   ❌ 控制测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ browser-tools-mcp集成测试失败: {e}")
        return False


async def test_ocr_integration():
    """测试OCR服务集成"""
    print("\n" + "="*60)
    print("🧪 测试OCR服务集成")
    print("="*60)
    
    try:
        analyzer = get_enhanced_ocr_analyzer()
        
        print("🔍 环境检查:")
        available_providers = analyzer.get_available_providers()
        print(f"   可用OCR提供商: {available_providers}")
        
        if not available_providers:
            print("   ❌ 没有可用的OCR提供商")
            print("   💡 配置建议:")
            print("     - Google Vision: 设置 GOOGLE_APPLICATION_CREDENTIALS")
            print("     - Azure CV: 设置 AZURE_COMPUTER_VISION_KEY 和 AZURE_COMPUTER_VISION_ENDPOINT")
            print("     - Tesseract: 安装 pytesseract")
            return False
        
        print(f"   ✅ 找到 {len(available_providers)} 个可用提供商")
        
        # 如果有可用的提供商，进行基础测试
        print("\n📋 OCR服务基础测试:")
        print("   ✅ OCR分析器初始化成功")
        print("   ✅ 提供商检查完成")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR集成测试失败: {e}")
        return False


async def test_end_to_end_workflow():
    """测试端到端工作流"""
    print("\n" + "="*60)
    print("🧪 端到端工作流测试")
    print("="*60)
    
    try:
        # 测试AI智能交互
        print("📋 测试AI智能交互:")
        from src.ai_intelligent_interaction import get_interaction_manager
        
        manager = get_interaction_manager()
        session = manager.start_requirement_analysis_session("测试需求分析")
        
        print(f"   ✅ 需求分析: {session.user_requirement.parsed_intent}")
        print(f"   ✅ 业务领域: {session.user_requirement.business_domain}")
        print(f"   ✅ 置信度: {session.user_requirement.confidence:.2f}")
        
        # 测试工作流引擎
        print("\n📋 测试工作流引擎:")
        from src.workflow.engine import WorkflowEngine
        
        # 创建简单工作流
        simple_workflow = {
            "name": "测试工作流",
            "version": "1.0.0",
            "steps": [
                {
                    "id": "step_1",
                    "type": "log",
                    "description": "记录日志",
                    "metadata": {
                        "message": "工作流测试成功"
                    }
                }
            ]
        }
        
        engine = WorkflowEngine(simple_workflow)
        result = await engine.execute()
        
        if result["success"]:
            print("   ✅ 工作流执行成功")
        else:
            print("   ❌ 工作流执行失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 端到端测试失败: {e}")
        return False


async def generate_test_report(results: Dict[str, bool]):
    """生成测试报告"""
    print("\n" + "="*60)
    print("📊 M5里程碑集成测试报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    failed_tests = total_tests - passed_tests
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {failed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 M5里程碑状态:")
    if passed_tests == total_tests:
        print("   ✅ 所有集成测试通过，M5里程碑基本完成")
        print("   🎉 可以开始M6系统优化里程碑")
    elif passed_tests >= total_tests * 0.75:
        print("   🔄 大部分集成测试通过，M5里程碑接近完成")
        print("   💡 需要解决剩余集成问题")
    else:
        print("   ❌ 多个集成测试失败，M5里程碑需要继续开发")
        print("   🔧 需要重点关注失败的集成")
    
    # 生成下一步建议
    print(f"\n📋 下一步建议:")
    
    if not results.get("browser-use集成", False):
        print("   🔧 配置OpenAI API密钥以启用browser-use功能")
    
    if not results.get("browser-tools-mcp集成", False):
        print("   🔧 安装Node.js环境以启用browser-tools-mcp功能")
    
    if not results.get("OCR服务集成", False):
        print("   🔧 配置OCR服务API密钥以启用OCR功能")
    
    if passed_tests == total_tests:
        print("   🚀 开始性能优化和稳定性测试")
        print("   📚 更新文档和用户指南")
        print("   🎯 准备M6里程碑规划")


async def main():
    """主函数"""
    print("🎭 M5里程碑集成测试")
    print("测试browser-use、browser-tools-mcp和OCR服务集成")
    
    # 执行各项测试
    results = {}
    
    try:
        # 测试browser-use集成
        results["browser-use集成"] = await test_browser_use_integration()
        
        # 测试browser-tools-mcp集成
        results["browser-tools-mcp集成"] = await test_browser_tools_mcp_integration()
        
        # 测试OCR服务集成
        results["OCR服务集成"] = await test_ocr_integration()
        
        # 测试端到端工作流
        results["端到端工作流"] = await test_end_to_end_workflow()
        
        # 生成测试报告
        await generate_test_report(results)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
