"""
登录状态缓存测试

测试登录状态的录制、保存、加载和恢复功能
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"✅ 加载环境变量: {key}")


def test_imports():
    """测试导入"""
    print("🧪 测试登录状态缓存模块导入")
    print("-" * 40)
    
    try:
        from login_state_manager import (
            LoginStateManager,
            LoginStateRecorder,
            LoginStateData,
            LoginSession,
            get_login_state_manager
        )
        print("   ✅ login_state_manager 导入成功")
        
        from ai_login_workflow_generator import (
            LoginStateWorkflowExecutor,
            get_login_state_executor
        )
        print("   ✅ LoginStateWorkflowExecutor 导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        return False


async def test_login_state_manager():
    """测试登录状态管理器"""
    print("\n🧪 测试登录状态管理器")
    print("-" * 40)
    
    try:
        from login_state_manager import get_login_state_manager
        
        manager = get_login_state_manager()
        print("   ✅ 登录状态管理器创建成功")
        
        # 测试列出会话
        sessions = manager.list_login_sessions()
        print(f"   ✅ 当前登录会话数: {len(sessions)}")
        
        if sessions:
            print("   📋 现有登录会话:")
            for session in sessions[:3]:  # 只显示前3个
                print(f"      • {session['name']} ({session['domain']})")
                print(f"        创建时间: {session['created_at']}")
                print(f"        使用次数: {session['use_count']}")
                print(f"        是否有效: {'✅' if session['is_valid'] else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 登录状态管理器测试失败: {e}")
        return False


async def test_login_state_executor():
    """测试登录状态执行器"""
    print("\n🧪 测试登录状态执行器")
    print("-" * 40)
    
    try:
        from ai_login_workflow_generator import get_login_state_executor
        
        executor = get_login_state_executor()
        print("   ✅ 登录状态执行器创建成功")
        
        # 测试列出可用会话
        sessions = await executor.list_available_sessions()
        print(f"   ✅ 可用登录会话数: {len(sessions)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 登录状态执行器测试失败: {e}")
        return False


async def test_complete_login_and_cache():
    """测试完整的登录和缓存流程"""
    print("\n🧪 测试完整的登录和缓存流程")
    print("-" * 40)
    
    try:
        from ai_login_workflow_generator import get_ai_login_system, LoginWorkflowExecutor
        from login_state_manager import get_login_state_manager
        
        print("   🔐 请输入登录信息进行完整测试:")
        company_name = input("   企业名称: ").strip()
        username = input("   用户名: ").strip()
        
        import getpass
        password = getpass.getpass("   密码: ")
        
        if not all([company_name, username, password]):
            print("   ❌ 登录信息不完整")
            return False
        
        credentials = {
            "company_name": company_name,
            "username": username,
            "password": password
        }
        
        print(f"\n   🚀 开始完整登录和缓存测试...")
        
        # 步骤1: 执行登录工作流
        print("   📋 步骤1: 执行登录工作流")
        ai_system = get_ai_login_system()
        executor = LoginWorkflowExecutor()
        
        # 检查现有工作流
        workflows = ai_system.list_login_workflows()
        if not workflows:
            print("   ❌ 没有可用的登录工作流，请先创建工作流")
            return False
        
        # 使用第一个工作流
        template = ai_system.load_login_workflow(workflows[0]['filepath'])
        print(f"   ✅ 使用工作流: {template.name}")
        
        # 执行登录
        result = await executor.execute_login_workflow(template, credentials, headless=False)
        
        if not result['success']:
            print(f"   ❌ 登录失败: {result.get('error', '未知错误')}")
            return False
        
        print("   ✅ 登录成功")
        
        # 检查是否自动保存了登录状态
        saved_session_id = None
        for log in result.get('execution_log', []):
            if log.get('type') == 'save_login_state' and log.get('status') == 'success':
                saved_session_id = log.get('session_id')
                print(f"   ✅ 登录状态已自动保存: {saved_session_id}")
                break
        
        if not saved_session_id:
            print("   ⚠️ 登录状态未自动保存")
            return False
        
        # 步骤2: 测试使用缓存登录
        print(f"\n   📋 步骤2: 测试使用缓存登录状态")
        
        from ai_login_workflow_generator import get_login_state_executor
        state_executor = get_login_state_executor()
        
        # 使用缓存登录状态
        cache_result = await state_executor.execute_with_cached_login(
            session_id=saved_session_id,
            headless=False
        )
        
        if cache_result['success']:
            print("   ✅ 使用缓存登录状态成功")
            print(f"      会话名称: {cache_result['session_name']}")
            print(f"      当前URL: {cache_result['current_url']}")
            print(f"      执行时间: {cache_result['execution_time']:.2f}秒")
            
            # 关闭浏览器
            try:
                await cache_result['browser'].close()
            except Exception:
                pass
        else:
            print(f"   ❌ 使用缓存登录状态失败: {cache_result['error']}")
            return False
        
        print(f"\n   🎉 完整登录和缓存流程测试成功！")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 完整登录和缓存流程测试失败: {e}")
        return False


async def test_cached_login_only():
    """测试仅使用缓存登录"""
    print("\n🧪 测试仅使用缓存登录")
    print("-" * 40)
    
    try:
        from ai_login_workflow_generator import get_login_state_executor
        
        executor = get_login_state_executor()
        
        # 列出可用会话
        sessions = await executor.list_available_sessions()
        
        if not sessions:
            print("   ❌ 没有可用的登录会话")
            return False
        
        print("   📋 可用的登录会话:")
        for i, session in enumerate(sessions, 1):
            print(f"      {i}. {session['name']}")
            print(f"         域名: {session['domain']}")
            print(f"         创建时间: {session['created_at']}")
            print(f"         使用次数: {session['use_count']}")
            print(f"         是否有效: {'✅' if session['is_valid'] else '❌'}")
        
        # 选择会话
        choice = input(f"\n   选择要使用的会话 (1-{len(sessions)}, 或按回车跳过): ").strip()
        
        if not choice:
            print("   ⚠️ 跳过缓存登录测试")
            return True
        
        try:
            session_index = int(choice) - 1
            if 0 <= session_index < len(sessions):
                selected_session = sessions[session_index]
                session_id = selected_session['session_id']
                
                print(f"   🚀 使用缓存登录: {selected_session['name']}")
                
                # 执行缓存登录
                result = await executor.execute_with_cached_login(
                    session_id=session_id,
                    headless=False
                )
                
                if result['success']:
                    print("   ✅ 缓存登录成功")
                    print(f"      会话名称: {result['session_name']}")
                    print(f"      当前URL: {result['current_url']}")
                    print(f"      执行时间: {result['execution_time']:.2f}秒")
                    
                    # 保持浏览器打开一段时间
                    input("   👀 请查看浏览器中的登录状态，按回车关闭...")
                    
                    # 关闭浏览器
                    try:
                        await result['browser'].close()
                    except Exception:
                        pass
                    
                    return True
                else:
                    print(f"   ❌ 缓存登录失败: {result['error']}")
                    return False
            else:
                print("   ❌ 无效的选择")
                return False
                
        except ValueError:
            print("   ❌ 无效的输入")
            return False
        
    except Exception as e:
        print(f"   ❌ 缓存登录测试失败: {e}")
        return False


def show_usage_examples():
    """显示使用示例"""
    print("\n💡 登录状态缓存使用示例")
    print("=" * 50)
    
    print("\n1. 自动保存登录状态:")
    print("""
# 登录成功后会自动保存登录状态
from ai_login_workflow_generator import LoginWorkflowExecutor

executor = LoginWorkflowExecutor()
result = await executor.execute_login_workflow(template, credentials)

# 检查自动保存的会话ID
for log in result['execution_log']:
    if log['type'] == 'save_login_state':
        session_id = log['session_id']
        print(f"登录状态已保存: {session_id}")
""")
    
    print("\n2. 使用缓存登录状态:")
    print("""
from ai_login_workflow_generator import get_login_state_executor

executor = get_login_state_executor()

# 使用缓存登录状态
result = await executor.execute_with_cached_login(
    session_id="your_session_id",
    target_url="https://example.com/dashboard",  # 可选
    headless=False
)

if result['success']:
    page = result['page']  # 获取页面对象
    # 继续在已登录的页面上操作
    await page.click(".some-button")
""")
    
    print("\n3. 管理登录会话:")
    print("""
from login_state_manager import get_login_state_manager

manager = get_login_state_manager()

# 列出所有会话
sessions = manager.list_login_sessions()

# 删除过期会话
for session in sessions:
    if not session['is_valid']:
        await manager.delete_login_session(session['session_id'])
""")
    
    print("\n4. 手动保存登录状态:")
    print("""
from login_state_manager import get_login_state_manager

manager = get_login_state_manager()

# 在登录成功后手动保存状态
session_id = await manager.save_login_session(
    page, 
    session_name="我的登录会话",
    description="手动保存的登录状态",
    user_info={"username": "user123"}
)
""")


async def main():
    """主函数"""
    print("🎭 登录状态缓存系统测试")
    print("验证登录状态的录制、保存、加载和恢复功能")
    print("=" * 60)
    
    # 加载环境变量
    load_env()
    
    # 检查AI配置
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        print("❌ 缺少GEMINI_API_KEY配置")
        return
    
    print("✅ AI配置检查通过")
    
    # 运行测试
    tests = [
        ("模块导入", test_imports),
        ("登录状态管理器", test_login_state_manager),
        ("登录状态执行器", test_login_state_executor)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                results[test_name] = await test_func()
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 登录状态缓存系统测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！登录状态缓存系统准备就绪！")
        print(f"\n🚀 系统功能:")
        print(f"   ✅ 登录状态自动录制")
        print(f"   ✅ 登录状态JSON保存")
        print(f"   ✅ 登录状态加载恢复")
        print(f"   ✅ 缓存登录状态验证")
        print(f"   ✅ 免登录直接操作")
        print(f"   ✅ 会话管理和清理")
        
        print(f"\n🔧 核心优势:")
        print(f"   • 一次登录，多次使用")
        print(f"   • 自动状态保存")
        print(f"   • 快速状态恢复")
        print(f"   • 会话有效期管理")
        print(f"   • 跨浏览器会话共享")
        
        # 询问是否进行实际测试
        print(f"\n🧪 选择测试类型:")
        print(f"   1. 完整登录和缓存测试 (需要登录信息)")
        print(f"   2. 仅使用现有缓存登录测试")
        print(f"   3. 跳过实际测试")
        
        choice = input(f"\n请选择 (1-3): ").strip()
        
        if choice == "1":
            await test_complete_login_and_cache()
        elif choice == "2":
            await test_cached_login_only()
        else:
            print("   ⚠️ 跳过实际测试")
        
        # 显示使用示例
        show_usage_examples()
        
        print(f"\n🎯 登录状态缓存系统优势:")
        print(f"   • 提高自动化效率")
        print(f"   • 减少重复登录")
        print(f"   • 支持长期会话")
        print(f"   • 简化操作流程")
        
        print(f"\n🏆 现在您可以:")
        print(f"   • 一次登录，保存状态")
        print(f"   • 后续直接使用缓存登录")
        print(f"   • 跳过登录步骤，直接操作")
        print(f"   • 管理多个登录会话")
        
    else:
        print(f"\n❌ 部分测试失败，需要检查配置")


if __name__ == "__main__":
    asyncio.run(main())
