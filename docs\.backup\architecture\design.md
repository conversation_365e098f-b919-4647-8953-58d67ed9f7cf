# 系统架构设计

## 1. 总体架构

系统采用分层架构设计，主要包括以下几层：

### 1.1 架构概览

```
+----------------------------------+
|           界面层                  |
|  (Web界面、CLI、API接口)          |
+----------------------------------+
                |
                v
+----------------------------------+
|           应用层                  |
| (录制、执行、报告生成等业务逻辑)    |
+----------------------------------+
                |
                v
+----------------------------------+
|           服务层                  |
|  (AI服务、数据处理、存储服务)      |
+----------------------------------+
                |
                v
+----------------------------------+
|          基础设施层               |
| (Playwright自动化、浏览器控制)    |
+----------------------------------+
```

### 1.2 层次说明

1. **界面层**：
   - 提供Web界面，用于用户交互
   - 提供命令行接口(CLI)，支持脚本化和自动化
   - 提供API接口，供第三方系统集成

2. **应用层**：
   - 实现业务逻辑，包括录制、执行、报告生成等功能
   - 处理用户请求和业务流程
   - 实现工作流定义和执行逻辑

3. **服务层**：
   - 提供AI服务，包括异常检测、智能修复等
   - 提供数据处理服务，如数据转换、分析等
   - 提供存储服务，管理测试用例、结果和配置

4. **基础设施层**：
   - 基于Playwright的浏览器自动化基础设施
   - 处理浏览器控制和页面交互
   - 管理浏览器环境和资源

## 2. 核心组件

### 2.1 组件概览

```
+----------------+     +----------------+     +----------------+
|   录制器        |     |    运行器       |     |   报告器       |
|  (Recorder)     |---->|   (Runner)     |---->|  (Reporter)    |
+----------------+     +----------------+     +----------------+
         |                     |                      |
         v                     v                      v
+----------------+     +----------------+     +----------------+
| 功能记录器      |     |    AI代理      |     |  工作流引擎    |
|(Function Recorder)|   |   (AI Agent)   |     |(Workflow Engine)|
+----------------+     +----------------+     +----------------+
                                |
                                v
+----------------+     +----------------+     +----------------+
|  变量处理器     |     | 自我修复系统   |     |  集成适配器    |
|(Variable Processor)| |(Self-Healing System)| |(Integration Adapter)|
+----------------+     +----------------+     +----------------+
```

### 2.2 组件说明

#### 2.2.1 录制器 (Recorder)

**职责**：
- 捕获用户操作，生成测试脚本
- 管理录制会话和浏览器实例
- 转换用户操作为标准化的步骤格式

**主要功能**：
- 浏览器操作录制
- 选择器生成和优化
- 断言识别和添加
- 测试用例生成

**接口**：
- `startRecording(options)`: 启动录制会话
- `stopRecording()`: 停止录制并生成测试用例
- `pauseRecording()`: 暂停录制
- `resumeRecording()`: 恢复录制
- `addAssertion(type, params)`: 添加断言

#### 2.2.2 运行器 (Runner)

**职责**：
- 执行测试脚本，收集执行结果
- 管理执行环境和浏览器实例
- 处理执行过程中的异常和错误

**主要功能**：
- 测试用例加载和解析
- 步骤执行和结果收集
- 异常处理和重试
- 执行状态管理

**接口**：
- `loadTestCase(testCaseFile)`: 加载测试用例
- `run(options)`: 执行测试用例
- `stop()`: 停止执行
- `pause()`: 暂停执行
- `resume()`: 恢复执行

#### 2.2.3 报告器 (Reporter)

**职责**：
- 生成测试报告，展示执行情况
- 处理测试结果数据
- 提供结果分析和统计

**主要功能**：
- HTML/JSON报告生成
- 执行结果可视化
- 统计分析和趋势图
- 报告定制和导出

**接口**：
- `generateReport(testResult, format)`: 生成报告
- `saveReport(reportPath)`: 保存报告
- `getStatistics(testResult)`: 获取统计数据
- `compareResults(results)`: 比较多次执行结果

#### 2.2.4 AI代理 (AI Agent)

**职责**：
- 监控执行过程，处理异常情况
- 学习用户操作，提供智能建议
- 执行自然语言指令

**主要功能**：
- 异常检测和分类
- 智能修复和恢复
- 自然语言理解
- 操作习惯学习

**接口**：
- `monitorExecution(runner)`: 监控执行过程
- `detectAnomaly(context)`: 检测异常
- `repairIssue(issue, context)`: 修复问题
- `executeInstruction(instruction)`: 执行自然语言指令

#### 2.2.5 功能记录器 (Function Recorder)

**职责**：
- 记录页面功能元素
- 生成页面功能地图
- 管理元素信息和关系

**主要功能**：
- 页面元素扫描
- 元素属性提取
- 功能地图生成
- 元素关系分析

**接口**：
- `scanPage(page)`: 扫描页面元素
- `recordElement(element, attributes)`: 记录元素信息
- `generateFunctionMap(elements)`: 生成功能地图
- `updateElementRecord(element, changes)`: 更新元素记录

#### 2.2.6 工作流引擎 (Workflow Engine)

**职责**：
- 定义、执行和管理工作流
- 处理工作流逻辑和控制流
- 管理工作流版本和变更

**主要功能**：
- 工作流定义和解析
- 工作流执行和控制
- 条件和循环处理
- 版本管理和差异比较

**接口**：
- `defineWorkflow(definition)`: 定义工作流
- `executeWorkflow(workflow, params)`: 执行工作流
- `validateWorkflow(workflow)`: 验证工作流
- `compareWorkflows(workflow1, workflow2)`: 比较工作流差异

#### 2.2.7 变量处理器 (Variable Processor)

**职责**：
- 提取和管理工作流中的变量
- 处理变量赋值和引用
- 管理变量作用域和生命周期

**主要功能**：
- 变量提取和识别
- 变量类型处理
- 变量作用域管理
- 变量值验证和转换

**接口**：
- `extractVariables(form)`: 从表单提取变量
- `defineVariable(name, type, scope)`: 定义变量
- `getVariableValue(name, context)`: 获取变量值
- `setVariableValue(name, value, context)`: 设置变量值

#### 2.2.8 自我修复系统 (Self-Healing System)

**职责**：
- 处理元素定位失败等异常情况
- 尝试多种定位策略找到目标元素
- 学习并改进修复策略

**主要功能**：
- 选择器修复
- 元素相似性匹配
- 智能等待策略
- 修复历史记录和学习

**接口**：
- `healSelector(failedSelector, context)`: 修复失败的选择器
- `findSimilarElement(element, page)`: 查找相似元素
- `learnFromSuccess(originalSelector, fixedSelector)`: 从成功修复中学习
- `suggestBetterSelector(element)`: 建议更稳定的选择器

#### 2.2.9 集成适配器 (Integration Adapter)

**职责**：
- 与外部系统集成
- 转换数据格式和接口
- 管理集成配置和认证

**主要功能**：
- API集成
- 数据转换
- 认证和授权
- 集成监控和日志

**接口**：
- `connectToSystem(system, config)`: 连接外部系统
- `convertData(data, sourceFormat, targetFormat)`: 转换数据
- `sendRequest(endpoint, data, options)`: 发送请求
- `receiveCallback(data, source)`: 接收回调数据

## 3. 数据模型

### 3.1 测试用例模型

```json
{
  "id": "string",
  "name": "string",
  "description": "string",
  "createdAt": "datetime",
  "updatedAt": "datetime",
  "tags": ["string"],
  "steps": [
    {
      "id": "string",
      "action": "string",
      "selector": "string",
      "value": "string",
      "description": "string",
      "timeout": "number",
      "screenshot": "boolean"
    }
  ],
  "assertions": [
    {
      "id": "string",
      "type": "string",
      "selector": "string",
      "expected": "any",
      "description": "string"
    }
  ],
  "metadata": {
    "browser": "string",
    "viewport": {
      "width": "number",
      "height": "number"
    },
    "baseUrl": "string",
    "environment": "string"
  }
}
```

### 3.2 执行结果模型

```json
{
  "id": "string",
  "testCaseId": "string",
  "startTime": "datetime",
  "endTime": "datetime",
  "duration": "number",
  "status": "string",
  "environment": {
    "browser": "string",
    "os": "string",
    "viewport": {
      "width": "number",
      "height": "number"
    }
  },
  "steps": [
    {
      "id": "string",
      "stepId": "string",
      "status": "string",
      "startTime": "datetime",
      "endTime": "datetime",
      "duration": "number",
      "screenshot": "string",
      "error": {
        "message": "string",
        "stack": "string",
        "type": "string"
      }
    }
  ],
  "assertions": [
    {
      "id": "string",
      "assertionId": "string",
      "status": "string",
      "expected": "any",
      "actual": "any",
      "error": {
        "message": "string",
        "type": "string"
      }
    }
  ],
  "error": {
    "message": "string",
    "stack": "string",
    "type": "string"
  },
  "logs": [
    {
      "level": "string",
      "message": "string",
      "timestamp": "datetime"
    }
  ],
  "metadata": {
    "retries": "number",
    "version": "string",
    "custom": "object"
  }
}
```

### 3.3 功能记录模型

```json
{
  "pageId": "string",
  "url": "string",
  "title": "string",
  "scanTime": "datetime",
  "elements": [
    {
      "id": "string",
      "tagName": "string",
      "type": "string",
      "selectors": {
        "css": "string",
        "xpath": "string",
        "text": "string"
      },
      "attributes": {
        "id": "string",
        "class": "string",
        "name": "string",
        "role": "string"
      },
      "text": "string",
      "isVisible": "boolean",
      "isEnabled": "boolean",
      "position": {
        "x": "number",
        "y": "number",
        "width": "number",
        "height": "number"
      },
      "parent": "string",
      "children": ["string"],
      "actions": ["string"],
      "metadata": {
        "importance": "number",
        "frequency": "number",
        "custom": "object"
      }
    }
  ],
  "relationships": [
    {
      "sourceId": "string",
      "targetId": "string",
      "type": "string",
      "metadata": "object"
    }
  ],
  "version": "string"
}
```

### 3.4 工作流模型

```json
{
  "id": "string",
  "name": "string",
  "description": "string",
  "version": "string",
  "createdAt": "datetime",
  "updatedAt": "datetime",
  "steps": [
    {
      "id": "string",
      "type": "action|condition|loop",
      "name": "string",
      "action": {
        "type": "string",
        "selector": "string",
        "value": "string",
        "options": "object"
      },
      "condition": {
        "expression": "string",
        "thenSteps": ["string"],
        "elseSteps": ["string"]
      },
      "loop": {
        "type": "for|while",
        "expression": "string",
        "steps": ["string"]
      },
      "timeout": "number",
      "retries": "number",
      "errorHandling": {
        "strategy": "string",
        "actions": ["string"]
      },
      "variables": ["string"],
      "next": "string",
      "metadata": "object"
    }
  ],
  "variables": [
    {
      "name": "string",
      "type": "string",
      "scope": "string",
      "defaultValue": "any",
      "validation": {
        "required": "boolean",
        "pattern": "string",
        "min": "number",
        "max": "number"
      },
      "source": {
        "type": "string",
        "selector": "string"
      },
      "description": "string"
    }
  ],
  "entryPoint": "string",
  "tags": ["string"],
  "metadata": "object"
}
```

### 3.5 配置模型

```json
{
  "general": {
    "baseUrl": "string",
    "defaultBrowser": "string",
    "defaultTimeout": "number",
    "screenshotOnFailure": "boolean",
    "recordVideo": "boolean",
    "logLevel": "string",
    "workingDirectory": "string"
  },
  "browsers": [
    {
      "name": "string",
      "channel": "string",
      "headless": "boolean",
      "slowMo": "number",
      "viewport": {
        "width": "number",
        "height": "number"
      },
      "userAgent": "string",
      "deviceScaleFactor": "number",
      "isMobile": "boolean",
      "hasTouch": "boolean"
    }
  ],
  "recording": {
    "includeHover": "boolean",
    "smartSelector": "boolean",
    "autoScreenshot": "boolean",
    "autoWait": "boolean",
    "excludeSelectors": ["string"],
    "assertionTypes": ["string"]
  },
  "execution": {
    "concurrency": "number",
    "retries": "number",
    "abortOnFailure": "boolean",
    "waitForNavigation": "boolean",
    "navigationTimeout": "number",
    "actionDelay": "number",
    "traceDir": "string"
  },
  "reporting": {
    "outputDir": "string",
    "formats": ["string"],
    "includeScreenshots": "boolean",
    "includeConsoleLog": "boolean",
    "maxScreenshots": "number"
  },
  "ai": {
    "enabled": "boolean",
    "provider": "string",
    "apiKey": "string",
    "selfHealing": "boolean",
    "anomalyDetection": "boolean",
    "confidenceThreshold": "number"
  },
  "integration": {
    "browserUse": {
      "enabled": "boolean",
      "endpoint": "string",
      "apiKey": "string"
    },
    "webhook": {
      "url": "string",
      "events": ["string"],
      "headers": "object"
    }
  },
  "security": {
    "encryptedFields": ["string"],
    "accessControl": "object",
    "auditLog": "boolean"
  }
}
```

## 4. 接口设计

### 4.1 用户界面接口

#### 4.1.1 Web界面API

**主要端点**：
- `/api/testcases`: 测试用例管理
- `/api/execution`: 执行管理
- `/api/reports`: 报告管理
- `/api/workflows`: 工作流管理
- `/api/settings`: 系统设置

**数据交换格式**：
- 请求/响应格式：JSON
- 认证方式：JWT令牌
- 错误处理：标准HTTP状态码和错误对象

#### 4.1.2 命令行接口

**主要命令**：
- `record`: 录制测试用例
- `run`: 执行测试用例
- `report`: 生成测试报告
- `workflow`: 管理工作流
- `config`: 管理配置

**示例**：
```bash
# 录制测试用例
playwright-tool record --output test_case.json --headless false

# 执行测试用例
playwright-tool run --file test_case.json --browser chrome

# 生成报告
playwright-tool report --result result.json --format html
```

### 4.2 服务接口

#### 4.2.1 组件间通信接口

**通信模式**：
- 事件驱动
- 发布-订阅
- 直接调用

**主要事件**：
- `recording.started`: 录制开始
- `recording.stopped`: 录制结束
- `execution.started`: 执行开始
- `execution.completed`: 执行完成
- `error.detected`: 检测到错误
- `repair.attempted`: 尝试修复

**数据交换**：
- 使用标准化的事件对象
- 包含事件类型、时间戳和相关数据
- 支持异步处理和回调

#### 4.2.2 AI服务接口

**主要功能**：
- 异常检测
- 选择器修复
- 自然语言处理
- 页面分析

**输入/输出**：
- 输入：上下文数据、页面状态、错误信息
- 输出：分析结果、修复建议、操作计划

**集成方式**：
- REST API
- WebSocket
- 本地库调用

### 4.3 外部系统接口

#### 4.3.1 浏览器集成接口

**支持的浏览器**：
- Chrome/Chromium
- Firefox
- Safari
- Edge

**集成方式**：
- Playwright API
- CDP (Chrome DevTools Protocol)
- 自定义扩展

**主要功能**：
- 浏览器启动和控制
- 页面操作和监控
- 网络请求拦截
- 执行JavaScript

#### 4.3.2 外部工具集成

**支持的系统**：
- CI/CD系统 (Jenkins, GitHub Actions等)
- 测试管理工具 (TestRail, JIRA等)
- 监控系统 (Grafana, Prometheus等)
- 版本控制系统 (Git等)

**集成方式**：
- Webhook
- REST API
- 命令行工具
- 插件/扩展

### 4.4 插件接口

**插件类型**：
- 命令插件
- 报告插件
- 动作插件
- 集成插件

**接口定义**：
- 插件注册和初始化
- 生命周期钩子
- 配置和设置
- 事件订阅

**示例**：
```javascript
// 报告插件示例
module.exports = {
  name: 'custom-report',
  version: '1.0.0',
  init: (api, options) => {
    api.on('report.generate', (data) => {
      // 生成自定义报告
    });
  },
  hooks: {
    beforeExecution: (testCase) => {
      // 执行前处理
    },
    afterExecution: (result) => {
      // 执行后处理
    }
  },
  commands: {
    generateCustomReport: (options) => {
      // 实现自定义命令
    }
  }
};
```
