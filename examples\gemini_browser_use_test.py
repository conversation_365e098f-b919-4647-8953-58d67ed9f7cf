"""
Gemini + browser-use 端到端测试

测试使用Gemini作为AI引擎的browser-use自动化功能
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


async def test_gemini_basic_functionality():
    """测试Gemini基础功能"""
    print("🧪 测试Gemini基础功能")
    print("=" * 40)
    
    try:
        from ai_llm_manager import get_llm_manager, LLMProvider
        
        manager = get_llm_manager()
        available_providers = manager.get_available_providers()
        
        if LLMProvider.GEMINI not in available_providers:
            print("❌ Gemini不可用，请检查API密钥配置")
            return False
        
        print("✅ Gemini提供商可用")
        
        # 测试简单对话
        test_prompts = [
            "请简单说'Hello from Gemini'",
            "请用中文说'你好，我是Gemini'",
            "请回答：1+1等于几？"
        ]
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"\n🔍 测试 {i}: {prompt}")
            try:
                response = await manager.generate(prompt, provider=LLMProvider.GEMINI)
                print(f"   ✅ 响应: {response.content}")
                print(f"   📊 模型: {response.model}")
                if response.usage:
                    print(f"   📈 Token使用: {response.usage}")
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini基础功能测试失败: {e}")
        return False


async def test_gemini_browser_use_adapter():
    """测试Gemini browser-use适配器"""
    print("\n🧪 测试Gemini browser-use适配器")
    print("=" * 40)
    
    try:
        from browser_use_llm_adapter import create_browser_use_llm
        
        # 创建Gemini适配器
        adapter = create_browser_use_llm(provider="gemini")
        print("✅ Gemini适配器创建成功")
        
        # 测试langchain兼容接口
        test_messages = [
            "请说'Hello World'",
            "请用中文介绍一下你自己",
            "请解释什么是人工智能"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n🔍 适配器测试 {i}: {message}")
            try:
                response = await adapter.ainvoke(message)
                print(f"   ✅ 响应: {response.content[:100]}...")
                print(f"   📊 模型: {adapter.model}")
                print(f"   📈 元数据: {response.response_metadata}")
            except Exception as e:
                print(f"   ❌ 适配器测试失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini适配器测试失败: {e}")
        return False


async def test_gemini_real_browser_use():
    """测试Gemini真实browser-use集成"""
    print("\n🧪 测试Gemini真实browser-use集成")
    print("=" * 40)
    
    try:
        from real_browser_use_integration import create_multi_llm_agent
        
        # 创建使用Gemini的代理
        agent = create_multi_llm_agent("gemini")
        print("✅ Gemini代理创建成功")
        
        # 检查前提条件
        if not agent._check_prerequisites():
            print("❌ Gemini代理前提条件检查失败")
            return False
        
        print("✅ Gemini代理前提条件检查通过")
        
        # 测试LLM创建
        try:
            llm = agent._create_llm()
            print(f"✅ Gemini LLM创建成功: {llm.model}")
        except Exception as e:
            print(f"❌ Gemini LLM创建失败: {e}")
            return False
        
        # 测试AI需求分析
        print("\n🔍 测试AI需求分析:")
        test_requests = [
            "请帮我打开百度网站",
            "请帮我搜索人工智能相关信息",
            "请帮我创建一个新的客户档案"
        ]
        
        for i, request in enumerate(test_requests, 1):
            print(f"\n   测试需求 {i}: {request}")
            try:
                session = agent.interaction_manager.start_requirement_analysis_session(request)
                print(f"   ✅ 需求分析成功")
                print(f"   📝 解析意图: {session.user_requirement.parsed_intent}")
                print(f"   🏢 业务领域: {session.user_requirement.business_domain}")
                print(f"   📊 置信度: {session.user_requirement.confidence:.2f}")
                print(f"   🔗 工作流匹配: {len(session.workflow_matches)} 个")
            except Exception as e:
                print(f"   ❌ 需求分析失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini真实browser-use集成测试失败: {e}")
        return False


async def test_gemini_vs_other_providers():
    """测试Gemini与其他提供商的对比"""
    print("\n🧪 测试Gemini与其他提供商对比")
    print("=" * 40)
    
    try:
        from ai_llm_manager import get_llm_manager
        
        manager = get_llm_manager()
        available_providers = manager.get_available_providers()
        
        test_prompt = "请用一句话介绍人工智能"
        
        results = {}
        
        for provider in available_providers:
            print(f"\n🔍 测试 {provider.value} 提供商:")
            try:
                import time
                start_time = time.time()
                
                response = await manager.generate(test_prompt, provider=provider)
                
                end_time = time.time()
                response_time = end_time - start_time
                
                results[provider.value] = {
                    "success": True,
                    "response": response.content,
                    "model": response.model,
                    "response_time": response_time,
                    "usage": response.usage
                }
                
                print(f"   ✅ 响应时间: {response_time:.2f}秒")
                print(f"   📝 响应: {response.content}")
                print(f"   📊 模型: {response.model}")
                
            except Exception as e:
                print(f"   ❌ {provider.value} 测试失败: {e}")
                results[provider.value] = {
                    "success": False,
                    "error": str(e)
                }
        
        # 生成对比报告
        print(f"\n📊 提供商对比报告:")
        print("-" * 30)
        
        for provider, result in results.items():
            if result["success"]:
                print(f"✅ {provider}:")
                print(f"   响应时间: {result['response_time']:.2f}秒")
                print(f"   模型: {result['model']}")
                if result['usage']:
                    print(f"   Token使用: {result['usage']}")
            else:
                print(f"❌ {provider}: {result['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 提供商对比测试失败: {e}")
        return False


async def test_gemini_error_handling():
    """测试Gemini错误处理和降级"""
    print("\n🧪 测试Gemini错误处理和降级")
    print("=" * 40)
    
    try:
        from real_browser_use_integration import get_real_browser_use_agent
        
        # 测试自动选择（应该优先选择可用的提供商）
        print("🔍 测试自动LLM选择:")
        agent = get_real_browser_use_agent()
        
        if agent._check_prerequisites():
            print("✅ 自动选择代理前提条件检查通过")
            
            llm = agent._create_llm()
            print(f"✅ 自动选择LLM成功: {llm.model}")
            
            # 检查实际使用的提供商
            if hasattr(llm, '_llm_type'):
                print(f"📊 实际使用的提供商: {llm._llm_type}")
        
        # 测试指定Gemini
        print("\n🔍 测试指定Gemini:")
        gemini_agent = get_real_browser_use_agent(llm_provider="gemini")
        
        if gemini_agent._check_prerequisites():
            print("✅ Gemini代理前提条件检查通过")
            
            gemini_llm = gemini_agent._create_llm()
            print(f"✅ Gemini LLM创建成功: {gemini_llm.model}")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini错误处理测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🎭 Gemini + browser-use 端到端测试")
    print("验证Gemini在AI+RPA系统中的完整功能")
    print("=" * 60)
    
    # 检查Gemini配置
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        print("❌ 未检测到GEMINI_API_KEY环境变量")
        print("💡 请设置: set GEMINI_API_KEY=your-gemini-api-key")
        return
    
    print(f"✅ 检测到Gemini API密钥 (长度: {len(gemini_key)})")
    
    # 运行测试
    results = {}
    
    results["Gemini基础功能"] = await test_gemini_basic_functionality()
    results["Gemini适配器"] = await test_gemini_browser_use_adapter()
    results["Gemini browser-use集成"] = await test_gemini_real_browser_use()
    results["提供商对比"] = await test_gemini_vs_other_providers()
    results["错误处理和降级"] = await test_gemini_error_handling()
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 Gemini 端到端测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！Gemini集成完全成功！")
        print(f"🚀 Gemini已准备好用于生产环境的browser-use自动化")
        
        print(f"\n💡 使用建议:")
        print(f"   # 使用Gemini执行任务")
        print(f"   agent = get_real_browser_use_agent(llm_provider='gemini')")
        print(f"   result = await agent.execute_user_request('您的任务')")
        
    elif passed_tests >= total_tests * 0.8:
        print(f"\n🔄 大部分测试通过，Gemini基本可用")
        print(f"💡 建议解决剩余问题以获得最佳体验")
    else:
        print(f"\n❌ 多个测试失败，请检查Gemini配置")
        print(f"💡 确保API密钥有效且网络连接正常")


if __name__ == "__main__":
    asyncio.run(main())
