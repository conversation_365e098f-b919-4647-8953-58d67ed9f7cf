id: loop_workflow
name: 循环工作流示例
version: 1.0.0
description: 包含循环结构的工作流示例
start_at: navigate_to_products

nodes:
  navigate_to_products:
    type: operation
    name: 导航到产品列表
    operation: navigate
    value: https://example.com/products
    output: navigation_result
  
  process_items:
    type: loop
    name: 处理产品项
    loop_type: for_each
    items: ["product-1", "product-2", "product-3"]
    loop_var: "item"
    depends_on: [navigate_to_products]
    loop_body:
      - click_product
      - extract_details
  
  click_product:
    type: operation
    name: 点击产品
    operation: click
    selector: "#{{ item }}"
    output: "{{ item }}_click_result"
  
  extract_details:
    type: operation
    name: 提取产品详情
    operation: extract
    selector: ".product-details"
    output: "{{ item }}_details"
  
  complete_processing:
    type: operation
    name: 完成处理
    operation: navigate
    value: https://example.com/checkout
    depends_on: [process_items]
    output: checkout_result
