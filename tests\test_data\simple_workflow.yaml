id: simple_workflow
name: 简单工作流示例
version: 1.0.0
description: 一个简单的工作流示例，包含基本的导航、点击和填充操作
start_at: navigate_home

nodes:
  navigate_home:
    type: operation
    name: 导航到首页
    description: 导航到网站首页
    operation: navigate
    value: https://example.com
    output: navigation_result
  
  click_button:
    type: operation
    name: 点击登录按钮
    description: 点击登录按钮
    operation: click
    selector: "#login-button"
    depends_on: [navigate_home]
    output: click_result
  
  fill_form:
    type: operation
    name: 填写登录表单
    description: 填写用户名和密码
    operation: fill
    selector: "#username"
    value: testuser
    depends_on: [click_button]
    output: fill_result

variables:
  username: testuser
  password: testpass123
