# API 文档

## 录制模块 (recorder.py)

### `record(output_file: str, headless: bool = False) -> None`

录制浏览器操作并保存为JSON文件。

**参数:**
- `output_file`: 输出JSON文件路径
- `headless`: 是否以无头模式运行浏览器

**示例:**
```python
from src.recorder import record

record("test_case.json", headless=False)
```

## 执行模块 (runner.py)

### `class TestRunner`

测试执行器类，负责加载并执行JSON格式的测试用例。

#### `__init__(test_case_file: str)`

初始化测试执行器。

**参数:**
- `test_case_file`: JSON格式的测试用例文件路径

#### `run(browser_type: str = "chromium", headless: bool = True) -> Dict`

执行测试用例。

**参数:**
- `browser_type`: 浏览器类型，可选值: "chromium", "firefox", "webkit"
- `headless`: 是否以无头模式运行

**返回:**
- 包含执行结果的字典

**示例:**
```python
from src.runner import TestRunner

runner = TestRunner("test_case.json")
result = runner.run(browser_type="chromium", headless=False)
print(result)
```

## 报告模块 (reporter.py)

### `generate_report(test_result: Dict, output_format: str = "html") -> str`

生成测试报告。

**参数:**
- `test_result`: 测试执行结果
- `output_format`: 报告格式，支持 "html" 或 "json"

**返回:**
- 报告文件路径

**示例:**
```python
from src.reporter import generate_report
from src.runner import TestRunner

runner = TestRunner("test_case.json")
result = runner.run()
report_path = generate_report(result, output_format="html")
print(f"Report generated at: {report_path}")
```

## 工具模块 (utils/)

### `converter.py`

#### `python_to_json(python_file: str, output_file: str) -> None`

将Python测试脚本转换为JSON格式。

**参数:**
- `python_file`: 输入的Python文件路径
- `output_file`: 输出的JSON文件路径

#### `json_to_python(json_file: str, output_file: str) -> None`

将JSON格式的测试用例转换为Python脚本。

**参数:**
- `json_file`: 输入的JSON文件路径
- `output_file`: 输出的Python文件路径

## 测试数据格式

### 测试用例JSON格式

```json
{
  "name": "example_test",
  "description": "示例测试用例",
  "steps": [
    {
      "action": "navigate",
      "url": "https://example.com",
      "description": "访问示例网站"
    },
    {
      "action": "click",
      "selector": "button#submit",
      "description": "点击提交按钮",
      "timeout": 5000
    }
  ],
  "assertions": [
    {
      "type": "url_contains",
      "value": "success",
      "description": "验证URL包含success"
    }
  ]
}
```

## 支持的操作类型

- `navigate`: 导航到指定URL
- `click`: 点击元素
- `fill`: 填写表单
- `select`: 选择下拉选项
- `hover`: 鼠标悬停
- `check`/`uncheck`: 勾选/取消勾选复选框
- `screenshot`: 截图
- `wait_for_selector`: 等待元素出现
- `wait_for_timeout`: 等待指定时间

## 支持的断言类型

- `url_contains`: URL包含指定文本
- `title_is`: 页面标题等于指定文本
- `text_content`: 元素文本内容匹配
- `is_visible`: 元素可见
- `is_hidden`: 元素隐藏
- `is_enabled`: 元素可用
- `is_checked`: 元素被选中
