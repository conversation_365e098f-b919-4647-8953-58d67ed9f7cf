# 工作流录制指南

本指南详细介绍如何使用系统的工作流录制功能。

## 录制功能概述

工作流录制器基于 Playwright Codegen 实现，具有以下特点：

- 智能元素定位
- 多设备模拟
- 自动等待机制
- AI 辅助优化
- 实时预览
- 录制控制

## 录制器组件

### 1. Playwright Inspector

录制器使用 Playwright Inspector 提供图形化界面，包含：

- 实时预览窗口
- 录制控制面板
- 元素选择器编辑器
- 步骤列表查看器

### 2. AI 优化引擎

录制过程中，AI 引擎会：

- 优化选择器策略
- 识别关键操作
- 合并重复步骤
- 添加智能等待
- 生成步骤描述

### 3. 浏览器监控

录制时会同步启动浏览器监控：

- 页面性能监控
- 网络请求记录
- DOM 变化追踪
- 异常检测

## 使用方法

### 1. 启动录制

```bash
# 基本用法
python -m src.recorder -u https://your-target-url.com

# 完整参数
python -m src.recorder \
  -u https://your-target-url.com \
  --viewport 1280,720 \
  --device "iPhone 13" \
  --lang zh-CN \
  --color-scheme dark \
  --timezone "Asia/Shanghai" \
  --output workflows/custom_workflow.json
```

### 2. 录制控制

在 Playwright Inspector 中：

- **开始录制**: 点击 "Record" 按钮
- **暂停录制**: 点击 "Pause" 按钮
- **结束录制**: 点击 "Stop" 按钮或关闭浏览器窗口
- **编辑步骤**: 双击步骤列表中的项目
- **调整选择器**: 使用选择器编辑器修改

### 3. 元素定位策略

系统支持多种定位策略：

1. **优先级顺序**:
   - `data-testid` 属性
   - ARIA 角色和标签
   - 文本内容
   - CSS 选择器
   - XPath

2. **智能组合**:
   ```javascript
   // 示例选择器
   "button[data-testid='submit-btn']"
   "text=登录 >> nth=0"
   "div[role='dialog'] >> button:has-text('确认')"
   ```

3. **动态调整**:
   - 自动处理动态 ID
   - 适应 Shadow DOM
   - 处理 iframe

### 4. 高级功能

#### 4.1 设备模拟

```bash
# iPhone 13 模拟
--device "iPhone 13"

# 自定义设备
--viewport 390,844 \
--user-agent "custom-ua" \
--device-scale-factor 2
```

#### 4.2 网络条件

```bash
# 限速模拟
--throttling slow3g

# 离线模式
--offline
```

#### 4.3 地理位置

```bash
# 设置位置
--geolocation 39.9042,116.4074
```

#### 4.4 本地存储

```bash
# 保持登录状态
--storage-state auth.json
```

## 最佳实践

### 1. 录制准备

- 使用干净的浏览器环境
- 准备测试数据
- 规划录制流程
- 配置合适的设备参数

### 2. 录制过程

- 操作要清晰明确
- 避免快速连续操作
- 验证每个关键步骤
- 适当添加等待时间

### 3. 录制后优化

- 检查选择器稳定性
- 调整等待策略
- 添加错误处理
- 优化变量配置

## 常见问题解决

### 1. 选择器问题

**问题**: 录制的选择器不稳定
**解决**:
- 添加 `data-testid` 属性
- 使用更多的定位策略组合
- 检查动态生成的属性

### 2. 同步问题

**问题**: 页面加载时机不对
**解决**:
- 添加显式等待
- 使用 `waitForLoadState`
- 配置网络条件

### 3. 设备模拟问题

**问题**: 移动设备显示异常
**解决**:
- 检查 viewport 设置
- 调整 user-agent
- 验证响应式设计

## 进阶主题

### 1. 自定义录制器

可以通过扩展 `CodegenWrapper` 类自定义录制行为：

```python
class CustomCodegenWrapper(CodegenWrapper):
    def __init__(self, output_file: str):
        super().__init__(output_file)
        
    def start_recording(self, url: str, **options):
        # 自定义录制逻辑
        pass
        
    def convert_to_workflow(self):
        # 自定义转换逻辑
        pass
```

### 2. 事件处理

可以注册自定义事件处理器：

```python
def custom_event_handler(event):
    if event.type == "click":
        # 处理点击事件
        pass
    elif event.type == "navigation":
        # 处理导航事件
        pass

recorder.add_event_handler(custom_event_handler)
```

### 3. AI 优化配置

可以调整 AI 优化参数：

```python
recorder.configure_ai({
    "selector_strategy": "strict",
    "merge_threshold": 0.8,
    "wait_timeout": 30000
})
```

## 下一步

- 查看[工作流执行指南](../workflow-execution/README.md)了解如何执行录制的工作流
- 查看[浏览器监控指南](../browser-monitoring/README.md)了解如何监控执行过程
- 查看[API 参考](../../04-developer-guide/api-reference/README.md)了解更多技术细节 