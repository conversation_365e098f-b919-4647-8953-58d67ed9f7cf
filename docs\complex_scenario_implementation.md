# 复杂场景系统实现文档

## 概述

复杂场景系统是AI+RPA框架的高级功能，实现了"协助登录 → 页面分析 → 操作数据生成 → 智能导航"的完整自动化流程。该系统能够智能理解用户需求，自动发现页面操作选项，并根据要求进行智能导航。

## 核心功能

### 1. 协助登录
- **缓存登录状态**: 使用已保存的登录会话快速进入系统
- **会话选择**: 智能选择最适合的登录会话
- **状态验证**: 自动验证登录状态是否有效
- **错误处理**: 完善的登录失败处理机制

### 2. 页面操作分析
- **AI页面分析**: 使用Gemini AI智能分析页面结构
- **链接提取**: 自动提取所有可操作的链接和按钮
- **功能分类**: AI自动分类页面功能（导航、操作、查询等）
- **优先级评估**: 智能评估每个操作的重要性

### 3. 操作数据生成
- **结构化数据**: 生成标准化的页面操作数据
- **JSON存储**: 将操作数据持久化为JSON文件
- **元数据丰富**: 包含描述、分类、优先级等丰富信息
- **版本管理**: 支持操作数据的版本管理

### 4. 智能导航
- **需求理解**: AI理解用户的自然语言需求
- **智能匹配**: 基于关键词和语义匹配相关操作
- **自动导航**: 自动点击或导航到目标页面
- **结果验证**: 验证导航是否成功

## 技术架构

```
复杂场景系统架构
├── 协助登录层 (LoginStateManager)
│   ├── 缓存登录状态管理
│   ├── 会话选择和验证
│   └── 浏览器状态恢复
├── 页面分析层 (PageOperationAnalyzer)
│   ├── AI页面结构分析
│   ├── 链接和操作提取
│   ├── 功能分类和优先级
│   └── 操作数据生成
├── 智能导航层 (IntelligentPageNavigator)
│   ├── 用户需求分析
│   ├── 智能匹配算法
│   ├── 自动导航执行
│   └── 结果验证
└── 工作流编排层 (ComplexScenarioWorkflow)
    ├── 场景会话管理
    ├── 流程编排
    ├── 状态管理
    └── 历史记录
```

## 核心组件

### 1. PageOperationAnalyzer (页面操作分析器)
```python
class PageOperationAnalyzer:
    async def analyze_page_operations(page) -> PageOperationData
    async def _extract_page_links(page) -> List[PageLink]
    async def _ai_analyze_link(text, href, type) -> Tuple[str, str, int]
    async def _ai_analyze_page_structure(page, links) -> Tuple[Dict, Dict, str]
```

**功能特点:**
- 提取所有可点击元素（链接、按钮、菜单）
- AI分析每个元素的功能和重要性
- 生成页面功能结构图
- 支持多种元素类型识别

### 2. IntelligentPageNavigator (智能页面导航器)
```python
class IntelligentPageNavigator:
    async def find_target_pages(operation_data, requirement) -> NavigationResult
    async def navigate_to_target(page, target_link, operation_data) -> Dict
    async def auto_navigate_by_requirement(page, operation_data, requirement) -> Dict
```

**功能特点:**
- 自然语言需求理解
- 智能匹配算法（关键词+语义+优先级）
- 多种导航方式支持
- 导航结果验证

### 3. ComplexScenarioWorkflow (复杂场景工作流)
```python
class ComplexScenarioWorkflow:
    async def start_complex_scenario(session_name, login_session_id) -> Dict
    async def navigate_by_requirement(user_requirement) -> Dict
    async def get_current_page_operations() -> Dict
    async def refresh_page_analysis() -> Dict
    async def close_scenario() -> Dict
```

**功能特点:**
- 完整的场景生命周期管理
- 状态持久化和恢复
- 导航历史记录
- 错误处理和恢复

## 数据模型

### PageLink (页面链接)
```python
@dataclass
class PageLink:
    text: str                    # 链接文本
    href: str                    # 链接地址
    selector: str                # CSS选择器
    element_type: str            # 元素类型
    description: str             # AI生成的描述
    category: str                # 功能分类
    priority: int                # 重要性优先级
    is_external: bool            # 是否外部链接
    requires_auth: bool          # 是否需要认证
```

### PageOperationData (页面操作数据)
```python
@dataclass
class PageOperationData:
    url: str                     # 页面URL
    title: str                   # 页面标题
    domain: str                  # 域名
    links: List[PageLink]        # 所有链接
    navigation_structure: Dict   # 导航结构
    functional_areas: Dict       # 功能区域
    analysis_timestamp: str      # 分析时间戳
    ai_summary: str              # AI生成的页面摘要
```

### NavigationResult (导航结果)
```python
@dataclass
class NavigationResult:
    matched_links: List[PageLink]  # 匹配的链接
    confidence_scores: List[float]  # 置信度分数
    ai_reasoning: str              # AI推理过程
    recommended_action: str        # 推荐操作
```

## 使用流程

### 完整使用示例
```python
from complex_scenario_workflow import get_complex_scenario_workflow

# 1. 获取工作流实例
workflow = get_complex_scenario_workflow()

# 2. 启动复杂场景
result = await workflow.start_complex_scenario(
    session_name="用户管理场景",
    login_session_id="existing_session_id",
    description="用户管理相关操作"
)

# 3. 智能导航
nav_result = await workflow.navigate_by_requirement("我想查看用户列表")

# 4. 获取当前页面操作
operations = await workflow.get_current_page_operations()

# 5. 继续导航
nav_result2 = await workflow.navigate_by_requirement("我要添加新用户")

# 6. 关闭场景
await workflow.close_scenario()
```

### 分步使用示例
```python
# 1. 页面分析
from page_operation_analyzer import get_page_operation_analyzer

analyzer = get_page_operation_analyzer()
operation_data = await analyzer.analyze_page_operations(page)

# 2. 智能导航
from intelligent_page_navigator import get_intelligent_page_navigator

navigator = get_intelligent_page_navigator()
nav_result = await navigator.auto_navigate_by_requirement(
    page, operation_data, "查看系统设置"
)
```

## 智能匹配算法

### 匹配分数计算
```python
def _calculate_link_score(link: PageLink, request: NavigationRequest) -> float:
    score = 0.0
    
    # 关键词匹配 (权重最高)
    for keyword in request.target_keywords:
        if keyword in link.text.lower():
            score += 3.0  # 文本匹配
        elif keyword in link.description.lower():
            score += 2.0  # 描述匹配
        elif keyword in link.href.lower():
            score += 1.0  # URL匹配
    
    # 分类匹配
    if link.category in request.priority_categories:
        score += 2.0
    
    # 优先级加权
    score += link.priority * 0.1
    
    return score
```

### AI需求分析
- **关键词提取**: 从用户需求中提取关键词
- **意图识别**: 识别用户的操作意图
- **分类映射**: 将需求映射到功能分类
- **优先级排序**: 根据匹配度排序结果

## 实际应用场景

### 1. 企业系统管理
```python
# 场景: 企业管理系统操作
await workflow.start_complex_scenario("企业管理", login_session_id)

# 用户需求: "我要查看员工信息"
nav_result = await workflow.navigate_by_requirement("我要查看员工信息")
# 系统自动导航到: 人力资源 -> 员工管理 -> 员工列表

# 用户需求: "我要添加新部门"
nav_result = await workflow.navigate_by_requirement("我要添加新部门")
# 系统自动导航到: 组织架构 -> 部门管理 -> 新增部门
```

### 2. 电商后台管理
```python
# 场景: 电商后台操作
await workflow.start_complex_scenario("电商管理", login_session_id)

# 用户需求: "我要查看订单统计"
nav_result = await workflow.navigate_by_requirement("我要查看订单统计")
# 系统自动导航到: 数据统计 -> 订单分析

# 用户需求: "我要管理商品库存"
nav_result = await workflow.navigate_by_requirement("我要管理商品库存")
# 系统自动导航到: 商品管理 -> 库存管理
```

### 3. 财务系统操作
```python
# 场景: 财务系统操作
await workflow.start_complex_scenario("财务管理", login_session_id)

# 用户需求: "我要查看本月财务报表"
nav_result = await workflow.navigate_by_requirement("我要查看本月财务报表")
# 系统自动导航到: 财务报表 -> 月度报表

# 用户需求: "我要录入费用报销"
nav_result = await workflow.navigate_by_requirement("我要录入费用报销")
# 系统自动导航到: 费用管理 -> 报销申请
```

## 性能优化

### 1. 缓存机制
- **页面分析缓存**: 缓存页面分析结果，避免重复分析
- **AI响应缓存**: 缓存AI分析结果，提高响应速度
- **导航历史**: 记录成功的导航路径，优化后续匹配

### 2. 智能预加载
- **预分析**: 预先分析常用页面
- **预匹配**: 预计算常见需求的匹配结果
- **预导航**: 预加载可能的目标页面

### 3. 并行处理
- **并行分析**: 同时分析多个页面元素
- **异步AI调用**: 异步调用AI分析接口
- **批量处理**: 批量处理相似的操作

## 错误处理

### 1. 登录失败处理
- **会话过期**: 自动尝试刷新或重新登录
- **权限不足**: 提示用户权限问题
- **网络错误**: 自动重试机制

### 2. 页面分析失败
- **元素不存在**: 使用备用选择器
- **AI分析失败**: 使用默认分类和优先级
- **网络超时**: 重试机制

### 3. 导航失败处理
- **目标不存在**: 提供替代选项
- **导航超时**: 自动重试
- **权限限制**: 提示用户权限问题

## 扩展性

### 1. 插件机制
- **自定义分析器**: 支持特定网站的分析器
- **自定义导航器**: 支持特定操作的导航器
- **自定义匹配算法**: 支持领域特定的匹配算法

### 2. 多语言支持
- **国际化**: 支持多语言界面
- **本地化**: 支持本地化的需求理解
- **跨语言匹配**: 支持跨语言的操作匹配

### 3. 多模态支持
- **语音输入**: 支持语音需求输入
- **图像识别**: 支持基于图像的操作识别
- **手势识别**: 支持手势操作

## 总结

复杂场景系统成功实现了AI+RPA的高级自动化功能，通过智能分析、理解和导航，大大提高了用户与复杂系统交互的效率。该系统具有以下特点：

1. **智能化**: AI驱动的页面分析和需求理解
2. **自动化**: 完全自动化的导航和操作
3. **灵活性**: 支持自然语言需求描述
4. **可扩展**: 模块化设计，易于扩展
5. **可靠性**: 完善的错误处理和恢复机制

该系统为AI+RPA框架提供了强大的高级功能，使用户能够通过简单的自然语言描述实现复杂的系统操作，真正实现了"人机协作"的智能自动化。
