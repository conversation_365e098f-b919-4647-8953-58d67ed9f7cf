<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖放测试页面</title>
    <style>
        .container {
            display: flex;
            gap: 20px;
            padding: 20px;
        }
        .drop-zone {
            width: 200px;
            min-height: 200px;
            border: 2px dashed #ccc;
            padding: 10px;
            margin: 10px;
            background-color: #f9f9f9;
        }
        .drop-zone.highlight {
            border-color: #4CAF50;
            background-color: #e8f5e9;
        }
        .draggable {
            padding: 10px;
            margin: 5px 0;
            background-color: #2196F3;
            color: white;
            cursor: move;
            user-select: none;
        }
        .draggable.dragging {
            opacity: 0.5;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            min-height: 50px;
        }
    </style>
</head>
<body>
    <h1>拖放测试页面</h1>
    
    <div class="container">
        <div id="source" class="drop-zone">
            <div id="item1" class="draggable" draggable="true">可拖动项 1</div>
            <div id="item2" class="draggable" draggable="true">可拖动项 2</div>
            <div id="item3" class="draggable" draggable="true">可拖动项 3</div>
        </div>
        
        <div id="target" class="drop-zone">
            <p>拖放到这里</p>
        </div>
    </div>
    
    <div>
        <h3>操作结果：</h3>
        <div id="result"></div>
    </div>
    
    <script>
        // 存储拖拽的元素
        let draggedItem = null;
        
        // 为所有可拖动元素添加事件监听
        document.querySelectorAll('.draggable').forEach(item => {
            item.addEventListener('dragstart', handleDragStart);
            item.addEventListener('dragend', handleDragEnd);
        });
        
        // 为所有放置区域添加事件监听
        document.querySelectorAll('.drop-zone').forEach(zone => {
            zone.addEventListener('dragover', handleDragOver);
            zone.addEventListener('dragenter', handleDragEnter);
            zone.addEventListener('dragleave', handleDragLeave);
            zone.addEventListener('drop', handleDrop);
        });
        
        // 拖拽开始
        function handleDragStart(e) {
            draggedItem = this;
            this.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/plain', this.id);
            
            // 记录日志
            logEvent(`开始拖拽: ${this.id}`);
        }
        
        // 拖拽结束
        function handleDragEnd() {
            this.classList.remove('dragging');
            draggedItem = null;
        }
        
        // 拖拽经过放置区域
        function handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        }
        
        // 拖拽进入放置区域
        function handleDragEnter(e) {
            e.preventDefault();
            if (this !== draggedItem?.parentNode) {
                this.classList.add('highlight');
            }
        }
        
        // 拖拽离开放置区域
        function handleDragLeave() {
            this.classList.remove('highlight');
        }
        
        // 放置
        function handleDrop(e) {
            e.preventDefault();
            this.classList.remove('highlight');
            
            if (draggedItem && this !== draggedItem.parentNode) {
                // 如果拖拽到了不同的容器中，则移动元素
                this.appendChild(draggedItem);
                logEvent(`已将 ${draggedItem.id} 移动到 ${this.id}`);
            }
        }
        
        // 记录事件
        function logEvent(message) {
            const result = document.getElementById('result');
            const log = document.createElement('div');
            log.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            result.prepend(log);
            
            // 输出到控制台
            console.log(`[DragDrop] ${message}`);
        }
        
        // 初始化日志
        logEvent('页面已加载，可以开始拖拽测试');
    </script>
</body>
</html>
