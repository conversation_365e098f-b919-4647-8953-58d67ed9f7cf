"""
键盘和鼠标事件测试

测试键盘和鼠标事件的集成测试
"""
import time
import pytest
from playwright.sync_api import Page, expect
from tests.test_utils import get_test_data_path, save_debug_info

# 测试数据
KEYBOARD_MOUSE_PAGE = get_test_data_path("keyboard_mouse_test_page.html")

class TestKeyboardMouseEvents:
    """键盘和鼠标事件测试类"""
    
    @pytest.fixture(autouse=True)
    def setup(self, page: Page):
        """测试前置条件"""
        self.page = page
        self.page.goto(KEYBOARD_MOUSE_PAGE)
        
        # 等待页面加载完成
        self.page.wait_for_selector("#keyInput", state="visible")
        self.page.wait_for_selector("#mouseTarget", state="visible")
    
    def test_keyboard_events(self, page: Page):
        """测试键盘事件"""
        try:
            # 获取输入框
            input_field = page.locator("#keyInput")
            log_area = page.locator("#keyboardLog .event-item").first
            
            # 聚焦到输入框
            input_field.click()
            
            # 输入文本
            test_text = "Hello, Playwright!"
            input_field.fill(test_text)
            
            # 验证输入
            expect(input_field).to_have_value(test_text)
            
            # 验证键盘事件已记录
            expect(log_area).to_contain_text("keydown")
            expect(log_area).to_contain_text("keypress")
            expect(log_area).to_contain_text("keyup")
            
            # 测试特殊键
            input_field.press("ArrowLeft")
            input_field.press("Backspace")
            input_field.press("Enter")
            
            # 验证特殊键事件
            expect(page.locator("#keyboardLog")).to_contain_text("ArrowLeft")
            expect(page.locator("#keyboardLog")).to_contain_text("Backspace")
            expect(page.locator("#keyboardLog")).to_contain_text("Enter")
            
        except Exception as e:
            save_debug_info(page, "keyboard_events_test_failed")
            raise e
    
    def test_button_click(self, page: Page):
        """测试按钮点击事件"""
        try:
            # 设置对话框处理
            page.once("dialog", lambda dialog: dialog.accept())
            
            # 点击按钮
            click_button = page.locator("#clickMe")
            click_button.click()
            
            # 验证点击事件已记录
            log_area = page.locator("#mouseLog .event-item").first
            expect(log_area).to_contain_text("click")
            expect(log_area).to_contain_text("target: clickMe")
            
        except Exception as e:
            save_debug_info(page, "button_click_test_failed")
            raise e
    
    def test_right_click(self, page: Page):
        """测试右键点击事件"""
        try:
            # 设置对话框处理
            page.once("dialog", lambda dialog: dialog.accept())
            
            # 右键点击按钮
            right_click_button = page.locator("#rightClickMe")
            right_click_button.click(button="right")
            
            # 验证右键点击事件已记录
            log_area = page.locator("#mouseLog .event-item").first
            expect(log_area).to_contain_text("contextmenu")
            expect(log_area).to_contain_text("target: rightClickMe")
            
        except Exception as e:
            save_debug_info(page, "right_click_test_failed")
            raise e
    
    def test_double_click(self, page: Page):
        """测试双击事件"""
        try:
            # 设置对话框处理
            page.once("dialog", lambda dialog: dialog.accept())
            
            # 双击按钮
            double_click_button = page.locator("#doubleClickMe")
            double_click_button.dblclick()
            
            # 验证双击事件已记录
            log_area = page.locator("#mouseLog .event-item").first
            expect(log_area).to_contain_text("dblclick")
            expect(log_area).to_contain_text("target: doubleClickMe")
            
        except Exception as e:
            save_debug_info(page, "double_click_test_failed")
            raise e
    
    def test_mouse_movement(self, page: Page):
        """测试鼠标移动事件"""
        try:
            # 获取目标区域
            target = page.locator("#mouseTarget")
            
            # 移动鼠标到目标区域
            target.hover()
            
            # 验证鼠标移动事件已记录
            log_area = page.locator("#mouseLog .event-item").first
            expect(log_area).to_contain_text("mousemove")
            
            # 验证鼠标进入事件
            expect(log_area).to_contain_text("mouseenter")
            
        except Exception as e:
            save_debug_info(page, "mouse_movement_test_failed")
            raise e
    
    def test_slider_drag(self, page: Page):
        """测试滑块拖拽"""
        try:
            # 获取滑块元素
            slider = page.locator("#slider")
            slider_value = page.locator("#sliderValue")
            
            # 获取滑块边界框
            slider_box = slider.bounding_box()
            
            # 计算滑块中心点
            start_x = slider_box["x"] + slider_box["width"] * 0.1
            start_y = slider_box["y"] + slider_box["height"] / 2
            
            # 计算目标位置（向右拖动到80%位置）
            end_x = slider_box["x"] + slider_box["width"] * 0.8
            end_y = start_y
            
            # 执行拖拽
            page.mouse.move(start_x, start_y)
            page.mouse.down()
            page.mouse.move(end_x, end_y, steps=10)  # 分步移动，模拟真实拖拽
            page.mouse.up()
            
            # 验证滑块值已更新
            expect(slider_value).not_to_have_text("50")
            
            # 验证拖拽事件已记录
            log_area = page.locator("#dragLog .event-item").first
            expect(log_area).to_contain_text("slider input")
            
        except Exception as e:
            save_debug_info(page, "slider_drag_test_failed")
            raise e
    
    def test_keyboard_navigation(self, page: Page):
        """测试键盘导航"""
        try:
            # 点击键盘测试按钮使其获得焦点
            keyboard_button = page.locator("#keyboardButton")
            keyboard_button.click()
            
            # 发送键盘事件
            page.keyboard.press("Tab")
            page.keyboard.press("Enter")
            
            # 验证键盘事件已记录
            log_area = page.locator("#keyboardLog .event-item").first
            expect(log_area).to_contain_text("button keydown")
            
        except Exception as e:
            save_debug_info(page, "keyboard_navigation_test_failed")
            raise e
    
    def test_keyboard_shortcut(self, page: Page):
        """测试键盘快捷键"""
        try:
            # 聚焦到输入框
            input_field = page.locator("#keyInput")
            input_field.click()
            
            # 输入一些文本
            input_field.fill("Test")
            
            # 全选 (Ctrl+A)
            if page.context.browser.browser_type.name == 'webkit':
                # Mac 使用 Meta+A
                page.keyboard.press("Meta+A")
            else:
                page.keyboard.press("Control+A")
            
            # 复制 (Ctrl+C)
            if page.context.browser.browser_type.name == 'webkit':
                page.keyboard.press("Meta+C")
            else:
                page.keyboard.press("Control+C")
            
            # 移动到最后并粘贴
            input_field.press("End")
            
            if page.context.browser.browser_type.name == 'webkit':
                page.keyboard.press("Meta+V")
            else:
                page.keyboard.press("Control+V")
            
            # 验证文本已复制粘贴
            expect(input_field).to_have_value("TestTest")
            
        except Exception as e:
            save_debug_info(page, "keyboard_shortcut_test_failed")
            raise e
    
    def test_mouse_wheel(self, page: Page):
        """测试鼠标滚轮事件"""
        try:
            # 获取日志区域
            log_area = page.locator("#mouseLog")
            
            # 滚动鼠标滚轮
            page.mouse.wheel(0, 100)  # 向下滚动
            page.mouse.wheel(0, -50)   # 向上滚动
            
            # 注意：大多数浏览器不会为编程触发的滚轮事件触发mousemove事件
            # 这里主要是测试API调用是否正常
            
        except Exception as e:
            save_debug_info(page, "mouse_wheel_test_failed")
            raise e
