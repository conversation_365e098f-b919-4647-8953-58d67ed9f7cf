"""
运行单个测试文件
"""
import sys
import os
import importlib
import unittest

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath('.'))

def run_test(test_module_path):
    """运行指定模块的测试"""
    try:
        # 动态导入测试模块
        test_module = importlib.import_module(test_module_path.replace('/', '.').replace('\\', '.').replace('.py', ''))
        
        # 创建测试套件
        test_suite = unittest.TestLoader().loadTestsFromModule(test_module)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(test_suite)
        
        # 输出测试结果
        print("\n测试结果:")
        print(f"运行测试数: {result.testsRun}")
        print(f"失败测试数: {len(result.failures)}")
        print(f"错误测试数: {len(result.errors)}")
        
        # 返回测试结果
        return result.wasSuccessful()
        
    except Exception as e:
        print(f"运行测试时出错: {str(e)}", file=sys.stderr)
        return False

if __name__ == "__main__":
    # 要运行的测试模块路径
    test_module_path = "tests/test_operations.py"
    
    # 运行测试
    success = run_test(test_module_path)
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)
