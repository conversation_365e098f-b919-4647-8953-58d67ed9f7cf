"""
browser-use集成模块

实现与browser-use的集成，提供实时监控、异常检测和用户交互功能。
"""
import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class MonitoringStatus(Enum):
    """监控状态枚举"""
    IDLE = "idle"
    MONITORING = "monitoring"
    EXCEPTION_DETECTED = "exception_detected"
    USER_INTERACTION = "user_interaction"
    RECOVERING = "recovering"


@dataclass
class ExceptionInfo:
    """异常信息"""
    timestamp: float
    exception_type: str
    message: str
    screenshot_path: Optional[str] = None
    page_url: Optional[str] = None
    element_info: Optional[Dict[str, Any]] = None
    suggested_fix: Optional[str] = None


@dataclass
class UserFeedback:
    """用户反馈"""
    action: str  # "retry", "manual_fix", "skip", "abort"
    message: Optional[str] = None
    manual_steps: Optional[List[str]] = None
    parameters: Optional[Dict[str, Any]] = None


class BrowserUseMonitor:
    """browser-use监控器"""
    
    def __init__(self):
        """初始化监控器"""
        self.status = MonitoringStatus.IDLE
        self.current_operation = None
        self.exception_history: List[ExceptionInfo] = []
        self.user_feedback_callback: Optional[Callable] = None
        self.auto_recovery_enabled = True
        self.monitoring_interval = 0.5  # 500ms监控间隔
        
        # 监控配置
        self.max_operation_timeout = 30.0  # 操作超时时间
        self.max_page_load_time = 10.0     # 页面加载超时
        self.element_wait_timeout = 5.0    # 元素等待超时
        
    def set_user_feedback_callback(self, callback: Callable[[ExceptionInfo], UserFeedback]):
        """设置用户反馈回调函数"""
        self.user_feedback_callback = callback
    
    async def start_monitoring(self, page, operation_info: Dict[str, Any]):
        """
        开始监控操作
        
        Args:
            page: Playwright页面对象
            operation_info: 操作信息
        """
        self.status = MonitoringStatus.MONITORING
        self.current_operation = operation_info
        
        logger.info(f"开始监控操作: {operation_info.get('name', 'Unknown')}")
        
        try:
            # 启动监控任务
            monitoring_task = asyncio.create_task(self._monitor_operation(page))
            
            # 等待操作完成或异常
            await monitoring_task
            
        except Exception as e:
            logger.error(f"监控过程中发生错误: {e}")
            await self._handle_exception(page, e)
        finally:
            self.status = MonitoringStatus.IDLE
            self.current_operation = None
    
    async def _monitor_operation(self, page):
        """监控操作执行"""
        start_time = time.time()
        
        while self.status == MonitoringStatus.MONITORING:
            try:
                # 检查操作超时
                if time.time() - start_time > self.max_operation_timeout:
                    raise TimeoutError(f"操作超时: {self.max_operation_timeout}秒")
                
                # 检查页面状态
                await self._check_page_status(page)
                
                # 检查元素状态
                await self._check_element_status(page)
                
                # 检查网络状态
                await self._check_network_status(page)
                
                # 等待下次检查
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                await self._handle_exception(page, e)
                break
    
    async def _check_page_status(self, page):
        """检查页面状态"""
        try:
            # 检查页面是否响应
            await page.evaluate("document.readyState")
            
            # 检查是否有JavaScript错误
            errors = await page.evaluate("""
                () => {
                    const errors = window.jsErrors || [];
                    window.jsErrors = [];
                    return errors;
                }
            """)
            
            if errors:
                raise Exception(f"页面JavaScript错误: {errors}")
                
        except Exception as e:
            logger.warning(f"页面状态检查异常: {e}")
            raise
    
    async def _check_element_status(self, page):
        """检查元素状态"""
        if not self.current_operation:
            return
        
        operation_type = self.current_operation.get('type')
        target_selector = self.current_operation.get('selector')
        
        if not target_selector:
            return
        
        try:
            # 检查目标元素是否存在
            element = await page.query_selector(target_selector)
            if not element and operation_type in ['click', 'fill', 'select']:
                raise Exception(f"目标元素不存在: {target_selector}")
            
            # 检查元素是否可交互
            if element and operation_type == 'click':
                is_visible = await element.is_visible()
                is_enabled = await element.is_enabled()
                
                if not is_visible:
                    raise Exception(f"元素不可见: {target_selector}")
                if not is_enabled:
                    raise Exception(f"元素被禁用: {target_selector}")
                    
        except Exception as e:
            logger.warning(f"元素状态检查异常: {e}")
            raise
    
    async def _check_network_status(self, page):
        """检查网络状态"""
        try:
            # 检查是否有网络请求失败
            # 这里可以集成更复杂的网络监控逻辑
            pass
        except Exception as e:
            logger.warning(f"网络状态检查异常: {e}")
            raise
    
    async def _handle_exception(self, page, exception: Exception):
        """处理异常"""
        self.status = MonitoringStatus.EXCEPTION_DETECTED
        
        # 创建异常信息
        exception_info = ExceptionInfo(
            timestamp=time.time(),
            exception_type=type(exception).__name__,
            message=str(exception),
            page_url=page.url if page else None
        )
        
        # 截图保存
        try:
            screenshot_path = f"exception_{int(time.time())}.png"
            await page.screenshot(path=screenshot_path)
            exception_info.screenshot_path = screenshot_path
        except:
            pass
        
        # 获取页面信息
        try:
            exception_info.element_info = await self._get_page_info(page)
        except:
            pass
        
        # 生成修复建议
        exception_info.suggested_fix = self._generate_fix_suggestion(exception_info)
        
        # 记录异常
        self.exception_history.append(exception_info)
        logger.error(f"检测到异常: {exception_info.message}")
        
        # 尝试自动恢复或请求用户反馈
        if self.auto_recovery_enabled:
            recovery_success = await self._attempt_auto_recovery(page, exception_info)
            if recovery_success:
                return
        
        # 请求用户反馈
        await self._request_user_feedback(page, exception_info)
    
    async def _get_page_info(self, page) -> Dict[str, Any]:
        """获取页面信息"""
        try:
            return await page.evaluate("""
                () => {
                    return {
                        title: document.title,
                        url: window.location.href,
                        readyState: document.readyState,
                        activeElement: document.activeElement ? {
                            tagName: document.activeElement.tagName,
                            id: document.activeElement.id,
                            className: document.activeElement.className
                        } : null,
                        errorCount: (window.jsErrors || []).length
                    };
                }
            """)
        except:
            return {}
    
    def _generate_fix_suggestion(self, exception_info: ExceptionInfo) -> str:
        """生成修复建议"""
        exception_type = exception_info.exception_type
        message = exception_info.message
        
        suggestions = {
            "TimeoutError": "建议增加等待时间或检查网络连接",
            "ElementNotFoundError": "建议检查元素选择器是否正确，或等待页面加载完成",
            "ElementNotVisibleError": "建议滚动到元素位置或等待元素显示",
            "ElementNotEnabledError": "建议等待元素启用或检查页面状态"
        }
        
        # 基于异常类型返回建议
        for error_type, suggestion in suggestions.items():
            if error_type in exception_type or error_type.lower() in message.lower():
                return suggestion
        
        return "建议检查页面状态和网络连接，或联系技术支持"
    
    async def _attempt_auto_recovery(self, page, exception_info: ExceptionInfo) -> bool:
        """尝试自动恢复"""
        self.status = MonitoringStatus.RECOVERING
        
        try:
            exception_type = exception_info.exception_type
            
            # 基于异常类型尝试不同的恢复策略
            if "timeout" in exception_type.lower():
                # 超时异常：等待更长时间
                logger.info("尝试自动恢复：延长等待时间")
                await asyncio.sleep(2.0)
                return True
                
            elif "element" in exception_type.lower():
                # 元素异常：刷新页面或重新定位
                logger.info("尝试自动恢复：刷新页面")
                await page.reload()
                await page.wait_for_load_state("domcontentloaded")
                return True
                
            elif "network" in exception_type.lower():
                # 网络异常：重试请求
                logger.info("尝试自动恢复：重试网络请求")
                await asyncio.sleep(1.0)
                return True
                
        except Exception as e:
            logger.error(f"自动恢复失败: {e}")
        
        return False
    
    async def _request_user_feedback(self, page, exception_info: ExceptionInfo):
        """请求用户反馈"""
        self.status = MonitoringStatus.USER_INTERACTION
        
        if not self.user_feedback_callback:
            logger.error("未设置用户反馈回调函数")
            return
        
        try:
            # 调用用户反馈回调
            feedback = self.user_feedback_callback(exception_info)
            
            # 处理用户反馈
            await self._process_user_feedback(page, feedback, exception_info)
            
        except Exception as e:
            logger.error(f"处理用户反馈时发生错误: {e}")
    
    async def _process_user_feedback(self, page, feedback: UserFeedback, exception_info: ExceptionInfo):
        """处理用户反馈"""
        action = feedback.action
        
        if action == "retry":
            logger.info("用户选择重试操作")
            self.status = MonitoringStatus.MONITORING
            
        elif action == "manual_fix":
            logger.info("用户选择手动修复")
            # 等待用户手动操作
            if feedback.manual_steps:
                for step in feedback.manual_steps:
                    logger.info(f"执行手动步骤: {step}")
                    # 这里可以集成具体的手动操作逻辑
            
        elif action == "skip":
            logger.info("用户选择跳过当前操作")
            self.status = MonitoringStatus.IDLE
            
        elif action == "abort":
            logger.info("用户选择中止执行")
            raise Exception("用户中止执行")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            "status": self.status.value,
            "current_operation": self.current_operation,
            "exception_count": len(self.exception_history),
            "last_exception": self.exception_history[-1] if self.exception_history else None
        }
    
    def get_exception_history(self) -> List[ExceptionInfo]:
        """获取异常历史"""
        return self.exception_history.copy()
    
    def clear_exception_history(self):
        """清空异常历史"""
        self.exception_history.clear()


# 全局监控器实例
global_monitor = BrowserUseMonitor()


def get_monitor() -> BrowserUseMonitor:
    """获取全局监控器实例"""
    return global_monitor
