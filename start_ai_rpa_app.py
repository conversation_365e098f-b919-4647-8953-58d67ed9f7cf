"""
AI+RPA 应用启动器

同时启动前端React应用和后端FastAPI服务
"""
import subprocess
import sys
import os
import time
import signal
from pathlib import Path
import threading


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"✅ 加载环境变量: {key}")


def check_requirements():
    """检查运行要求"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本需要3.8或更高")
        return False
    
    # 检查Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js版本: {result.stdout.strip()}")
        else:
            print("❌ 未找到Node.js")
            return False
    except FileNotFoundError:
        print("❌ 未找到Node.js，请安装Node.js")
        return False
    
    # 检查npm
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm版本: {result.stdout.strip()}")
        else:
            print("❌ 未找到npm")
            return False
    except FileNotFoundError:
        print("❌ 未找到npm")
        return False
    
    # 检查前端目录
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ 未找到frontend目录")
        return False
    
    # 检查package.json
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("❌ 未找到frontend/package.json")
        return False
    
    print("✅ 运行环境检查通过")
    return True


def install_dependencies():
    """安装依赖"""
    print("📦 检查并安装依赖...")
    
    # 安装Python依赖
    print("🐍 检查Python依赖...")
    try:
        import fastapi
        import uvicorn
        import websockets
        print("✅ Python依赖已安装")
    except ImportError:
        print("📦 安装Python依赖...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'fastapi', 'uvicorn', 'websockets', 'python-multipart'])
    
    # 检查前端依赖
    frontend_dir = Path("frontend")
    node_modules = frontend_dir / "node_modules"
    
    if not node_modules.exists():
        print("📦 安装前端依赖...")
        try:
            subprocess.run(['npm', 'install'], cwd=frontend_dir, check=True)
            print("✅ 前端依赖安装成功")
        except subprocess.CalledProcessError:
            print("❌ 前端依赖安装失败")
            return False
    else:
        print("✅ 前端依赖已安装")
    
    return True


def start_backend():
    """启动后端服务"""
    print("🚀 启动后端API服务...")
    
    backend_dir = Path("backend_api")
    if not backend_dir.exists():
        backend_dir.mkdir()
    
    # 启动FastAPI服务
    try:
        process = subprocess.Popen([
            sys.executable, '-m', 'uvicorn', 'main:app',
            '--host', '0.0.0.0',
            '--port', '8000',
            '--reload'
        ], cwd=backend_dir)
        
        print("✅ 后端服务启动成功 (http://localhost:8000)")
        return process
        
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return None


def start_frontend():
    """启动前端服务"""
    print("🎨 启动前端React应用...")
    
    frontend_dir = Path("frontend")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['BROWSER'] = 'none'  # 不自动打开浏览器
        
        process = subprocess.Popen([
            'npm', 'start'
        ], cwd=frontend_dir, env=env)
        
        print("✅ 前端服务启动成功 (http://localhost:3000)")
        return process
        
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")
        return None


def wait_for_services():
    """等待服务启动"""
    print("⏳ 等待服务启动...")
    
    import requests
    import time
    
    # 等待后端服务
    backend_ready = False
    for i in range(30):  # 等待30秒
        try:
            response = requests.get('http://localhost:8000/health', timeout=1)
            if response.status_code == 200:
                backend_ready = True
                print("✅ 后端服务就绪")
                break
        except:
            pass
        time.sleep(1)
    
    if not backend_ready:
        print("⚠️ 后端服务启动超时")
    
    # 等待前端服务
    frontend_ready = False
    for i in range(60):  # 等待60秒
        try:
            response = requests.get('http://localhost:3000', timeout=1)
            if response.status_code == 200:
                frontend_ready = True
                print("✅ 前端服务就绪")
                break
        except:
            pass
        time.sleep(1)
    
    if not frontend_ready:
        print("⚠️ 前端服务启动超时")
    
    return backend_ready and frontend_ready


def open_browser():
    """打开浏览器"""
    import webbrowser
    print("🌐 打开浏览器...")
    webbrowser.open('http://localhost:3000')


def main():
    """主函数"""
    print("🤖 AI+RPA 智能自动化平台启动器")
    print("=" * 50)
    
    # 加载环境变量
    load_env()
    
    # 检查运行要求
    if not check_requirements():
        print("❌ 环境检查失败，请检查并安装必要的依赖")
        return
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        return
    
    processes = []
    
    try:
        # 启动后端服务
        backend_process = start_backend()
        if backend_process:
            processes.append(backend_process)
        
        # 等待后端启动
        time.sleep(3)
        
        # 启动前端服务
        frontend_process = start_frontend()
        if frontend_process:
            processes.append(frontend_process)
        
        # 等待服务启动
        if wait_for_services():
            print("\n🎉 AI+RPA 平台启动成功！")
            print("=" * 50)
            print("📱 前端应用: http://localhost:3000")
            print("🔧 后端API: http://localhost:8000")
            print("📚 API文档: http://localhost:8000/docs")
            print("=" * 50)
            print("\n💡 功能特点:")
            print("• 🤖 AI智能助手 - 自然语言交互")
            print("• 🎨 可视化工作流设计器")
            print("• 📋 工作流管理和执行")
            print("• 🔄 实时状态监控")
            print("• 💾 工作流导入导出")
            print("\n🚀 开始您的AI+RPA自动化之旅！")
            
            # 打开浏览器
            time.sleep(2)
            open_browser()
            
            # 等待用户中断
            print("\n按 Ctrl+C 停止服务...")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n👋 正在停止服务...")
        else:
            print("❌ 服务启动失败")
    
    except KeyboardInterrupt:
        print("\n👋 正在停止服务...")
    
    finally:
        # 停止所有进程
        for process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
        
        print("✅ 所有服务已停止")


if __name__ == "__main__":
    main()
