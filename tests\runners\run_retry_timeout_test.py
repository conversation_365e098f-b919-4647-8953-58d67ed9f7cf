"""
独立的集成测试：重试机制和超时处理
"""
import asyncio
import time
import uuid
from typing import Dict, Any, Optional, List, Callable, Type, Tuple

class MockFailingOperation:
    """模拟会失败的操作"""
    def __init__(self, fail_times=0, success_after=None, exception=Exception("Test error"), 
                 id=None, timeout=None, max_retries=3, retry_delay=0.1):
        self.fail_times = fail_times
        self.success_after = success_after
        self.exception = exception
        self.attempts = 0
        self.id = id or f"mock_operation_{str(uuid.uuid4())[:8]}"
        self.type = "mock"
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.retry_on = (Exception,)
        self.timeout = timeout
        self.continue_on_failure = False
        self.parameters = {}
        self.wait_conditions = []
        self.metadata = {}
    
    async def execute(self, *args, **kwargs):
        self.attempts += 1
        print(f"执行操作 {self.id}, 尝试次数: {self.attempts}")
        
        # 模拟超时
        if self.timeout and self.attempts == 1:
            print(f"操作 {self.id} 模拟超时...")
            await asyncio.sleep(self.timeout * 1.5)
            
        # 模拟在指定次数后成功
        if self.success_after is not None and self.attempts > self.success_after:
            print(f"操作 {self.id} 第 {self.attempts} 次尝试成功")
            return "success"
            
        # 模拟失败
        if self.attempts <= self.fail_times:
            print(f"操作 {self.id} 第 {self.attempts} 次尝试失败: {self.exception}")
            raise self.exception
            
        print(f"操作 {self.id} 第 {self.attempts} 次尝试成功")
        return "success"

class OperationError(Exception):
    """操作错误"""
    pass

class OperationExecutor:
    """操作执行器"""
    def __init__(self, default_timeout: float = 30.0):
        self.default_timeout = default_timeout
        self.retry_attempts = {}
    
    async def execute_operation(self, operation, **kwargs):
        """执行操作，支持重试和超时"""
        operation_id = getattr(operation, 'id', str(id(operation)))
        self.retry_attempts[operation_id] = 0
        
        # 获取重试参数
        max_retries = getattr(operation, 'max_retries', 0)
        retry_delay = getattr(operation, 'retry_delay', 0.1)
        retry_on = getattr(operation, 'retry_on', (Exception,))
        timeout = getattr(operation, 'timeout', self.default_timeout)
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            self.retry_attempts[operation_id] = attempt
            
            try:
                # 设置超时
                if timeout:
                    result = await asyncio.wait_for(
                        operation.execute(**kwargs),
                        timeout=timeout
                    )
                else:
                    result = await operation.execute(**kwargs)
                    
                # 如果执行成功，返回结果
                return result
                
            except asyncio.TimeoutError as e:
                last_exception = asyncio.TimeoutError(f"操作 {operation_id} 超时 (超时时间: {timeout}秒)")
                print(f"操作 {operation_id} 超时 (尝试 {attempt + 1}/{max_retries + 1})")
                
            except retry_on as e:
                last_exception = e
                print(f"操作 {operation_id} 失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                
            # 如果不是最后一次尝试，等待重试延迟
            if attempt < max_retries:
                await asyncio.sleep(retry_delay)
        
        # 如果所有重试都失败，抛出异常
        raise OperationError(f"操作 {operation_id} 重试 {max_retries} 次后仍然失败: {last_exception}")

async def test_retry_mechanism():
    """测试重试机制"""
    print("\n=== 测试重试机制 ===")
    executor = OperationExecutor(default_timeout=2.0)
    
    # 创建一个会失败2次然后成功的操作
    operation = MockFailingOperation(fail_times=2, max_retries=3, retry_delay=0.1)
    
    try:
        # 执行操作
        result = await executor.execute_operation(operation)
        
        # 验证结果
        assert result == "success"
        assert operation.attempts == 3  # 初始尝试 + 2次重试
        print("✅ 测试通过：重试机制")
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        raise

async def test_retry_exhausted():
    """测试重试次数用尽"""
    print("\n=== 测试重试次数用尽 ===")
    executor = OperationExecutor(default_timeout=2.0)
    
    # 创建一个总是失败的操作
    operation = MockFailingOperation(fail_times=10, max_retries=2, retry_delay=0.1)
    
    try:
        # 执行操作，应该抛出异常
        try:
            await executor.execute_operation(operation)
            print("❌ 测试失败：预期抛出 OperationError 异常")
            return
        except OperationError as e:
            # 验证重试次数
            assert operation.attempts == 3  # 初始尝试 + 2次重试
            assert "重试 2 次后仍然失败" in str(e)
            print("✅ 测试通过：重试次数用尽")
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        raise

async def test_operation_timeout():
    """测试操作超时"""
    print("\n=== 测试操作超时 ===")
    executor = OperationExecutor(default_timeout=1.0)
    
    # 创建一个会超时的操作
    operation = MockFailingOperation(timeout=0.5, max_retries=0)
    
    try:
        # 执行操作，应该抛出超时异常
        try:
            await executor.execute_operation(operation)
            print("❌ 测试失败：预期抛出 OperationError 异常")
            return
        except OperationError as e:
            # 验证错误信息
            assert "超时" in str(e)
            print("✅ 测试通过：操作超时")
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        raise

async def test_retry_after_timeout():
    """测试超时后的重试"""
    print("\n=== 测试超时后的重试 ===")
    executor = OperationExecutor(default_timeout=1.0)
    
    # 创建一个第一次会超时，然后成功的操作
    operation = MockFailingOperation(
        success_after=1,  # 第二次成功
        timeout=0.5,      # 0.5秒超时
        max_retries=3,    # 最多重试3次
        retry_delay=0.1   # 重试延迟0.1秒
    )
    
    try:
        # 执行操作
        result = await executor.execute_operation(operation)
        
        # 验证结果
        assert result == "success"
        assert operation.attempts == 2  # 初始尝试(超时) + 1次重试(成功)
        print("✅ 测试通过：超时后的重试")
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        raise

async def test_operation_sequence_with_retry():
    """测试带重试的操作序列"""
    print("\n=== 测试带重试的操作序列 ===")
    executor = OperationExecutor(default_timeout=2.0)
    
    # 创建多个操作，其中一些会失败
    operations = [
        MockFailingOperation(id="op1", fail_times=1, max_retries=2, retry_delay=0.1),
        MockFailingOperation(id="op2", fail_times=2, max_retries=3, retry_delay=0.1),
        MockFailingOperation(id="op3", fail_times=0, max_retries=2, retry_delay=0.1)
    ]
    
    try:
        # 执行操作序列
        results = []
        for op in operations:
            result = await executor.execute_operation(op)
            results.append((op.id, result))
        
        # 验证所有操作都成功执行
        assert len(results) == 3
        for op_id, result in results:
            assert result == "success", f"操作 {op_id} 失败"
        
        # 验证重试次数
        assert operations[0].attempts == 2  # 失败1次，重试1次
        assert operations[1].attempts == 3  # 失败2次，重试2次
        assert operations[2].attempts == 1  # 没有失败，不需要重试
        
        print("✅ 测试通过：带重试的操作序列")
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        raise

async def run_tests():
    """运行所有测试"""
    print("=== 开始集成测试 ===")
    
    tests = [
        test_retry_mechanism,
        test_retry_exhausted,
        test_operation_timeout,
        test_retry_after_timeout,
        test_operation_sequence_with_retry
    ]
    
    for test in tests:
        try:
            await test()
        except Exception as e:
            print(f"测试 {test.__name__} 失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(run_tests())
