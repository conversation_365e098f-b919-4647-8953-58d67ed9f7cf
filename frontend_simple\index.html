<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI+RPA 智能自动化平台</title>
    
    <!-- Material-UI CSS -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    
    <!-- React和相关库 -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Material-UI -->
    <script src="https://unpkg.com/@mui/material@latest/umd/material-ui.development.js"></script>
    
    <!-- Axios for HTTP requests -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <style>
        body {
            margin: 0;
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
        }
        
        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background-color: #1976d2;
            color: white;
            padding: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
            margin: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .chat-header {
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
            background-color: #f8f9fa;
            border-radius: 8px 8px 0 0;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }
        
        .message {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .message-avatar.user {
            background-color: #1976d2;
        }
        
        .message-avatar.assistant {
            background-color: #4caf50;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 16px;
            white-space: pre-wrap;
            word-break: break-word;
        }
        
        .message.user .message-content {
            background-color: #1976d2;
            color: white;
            border-bottom-right-radius: 4px;
        }
        
        .message.assistant .message-content {
            background-color: #f0f0f0;
            color: #333;
            border-bottom-left-radius: 4px;
        }
        
        .chat-input {
            padding: 16px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }
        
        .input-field {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 20px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            outline: none;
        }
        
        .input-field:focus {
            border-color: #1976d2;
        }
        
        .send-button {
            width: 48px;
            height: 48px;
            border: none;
            border-radius: 50%;
            background-color: #1976d2;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }
        
        .send-button:hover {
            background-color: #1565c0;
        }
        
        .send-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .quick-commands {
            padding: 8px 16px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .quick-command {
            padding: 6px 12px;
            border: 1px solid #1976d2;
            border-radius: 16px;
            background: white;
            color: #1976d2;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .quick-command:hover {
            background-color: #1976d2;
            color: white;
        }
        
        .status-indicator {
            padding: 8px 16px;
            background-color: #e3f2fd;
            border-left: 4px solid #1976d2;
            margin: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .loading {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-style: italic;
        }
        
        .loading::before {
            content: "🤖";
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;
        
        // API基础URL
        const API_BASE_URL = 'http://localhost:8000';
        
        function AIRPAApp() {
            const [messages, setMessages] = useState([
                {
                    id: 1,
                    type: 'assistant',
                    content: '🎉 欢迎使用 AI+RPA 智能助手！\n\n我可以帮助您：\n• 🎯 智能导航和页面操作\n• 🔍 分析页面结构\n• 📝 生成和执行工作流\n• 🤖 AI协助处理命令\n\n请输入您的需求开始对话！',
                    timestamp: new Date()
                }
            ]);
            
            const [inputValue, setInputValue] = useState('');
            const [isLoading, setIsLoading] = useState(false);
            const [connectionStatus, setConnectionStatus] = useState('connecting');
            const messagesEndRef = useRef(null);
            
            // 自动滚动到底部
            const scrollToBottom = () => {
                messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            };
            
            useEffect(() => {
                scrollToBottom();
            }, [messages]);
            
            // 检查后端连接
            useEffect(() => {
                checkBackendConnection();
            }, []);
            
            const checkBackendConnection = async () => {
                try {
                    const response = await axios.get(`${API_BASE_URL}/health`);
                    if (response.status === 200) {
                        setConnectionStatus('connected');
                    }
                } catch (error) {
                    setConnectionStatus('disconnected');
                }
            };
            
            // 发送消息
            const handleSendMessage = async () => {
                if (!inputValue.trim() || isLoading) return;
                
                const userMessage = {
                    id: Date.now(),
                    type: 'user',
                    content: inputValue.trim(),
                    timestamp: new Date()
                };
                
                setMessages(prev => [...prev, userMessage]);
                setInputValue('');
                setIsLoading(true);
                
                try {
                    const response = await axios.post(`${API_BASE_URL}/api/chat/message`, {
                        content: userMessage.content,
                        type: 'user'
                    });
                    
                    const assistantMessage = {
                        id: Date.now() + 1,
                        type: 'assistant',
                        content: response.data.message || '处理完成',
                        timestamp: new Date(),
                        confidence: response.data.confidence,
                        commandType: response.data.commandType,
                        success: response.data.success
                    };
                    
                    setMessages(prev => [...prev, assistantMessage]);
                    
                } catch (error) {
                    const errorMessage = {
                        id: Date.now() + 1,
                        type: 'assistant',
                        content: `❌ 处理失败: ${error.message}`,
                        timestamp: new Date(),
                        success: false
                    };
                    setMessages(prev => [...prev, errorMessage]);
                } finally {
                    setIsLoading(false);
                }
            };
            
            // 处理回车发送
            const handleKeyPress = (event) => {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    handleSendMessage();
                }
            };
            
            // 快捷命令
            const quickCommands = [
                '分析页面',
                '打开百度',
                '搜索天气',
                '系统状态',
                '帮助'
            ];
            
            const handleQuickCommand = (command) => {
                setInputValue(command);
            };
            
            // 渲染消息
            const renderMessage = (message) => {
                return (
                    <div key={message.id} className={`message ${message.type}`}>
                        <div className={`message-avatar ${message.type}`}>
                            {message.type === 'user' ? '👤' : '🤖'}
                        </div>
                        <div className="message-content">
                            {message.content}
                            {message.confidence !== undefined && (
                                <div style={{ marginTop: '8px', fontSize: '12px', opacity: 0.7 }}>
                                    置信度: {(message.confidence * 100).toFixed(0)}%
                                    {message.commandType && ` | 类型: ${message.commandType}`}
                                </div>
                            )}
                            <div style={{ marginTop: '4px', fontSize: '11px', opacity: 0.6 }}>
                                {message.timestamp.toLocaleTimeString()}
                            </div>
                        </div>
                    </div>
                );
            };
            
            return (
                <div className="app-container">
                    <div className="header">
                        <h1 style={{ margin: 0, fontSize: '24px' }}>
                            🤖 AI+RPA 智能自动化平台
                        </h1>
                        <div style={{ fontSize: '14px', marginTop: '4px', opacity: 0.9 }}>
                            状态: {connectionStatus === 'connected' ? '✅ 已连接' : 
                                   connectionStatus === 'connecting' ? '🔄 连接中...' : '❌ 连接失败'}
                        </div>
                    </div>
                    
                    <div className="content">
                        <div className="chat-panel">
                            <div className="chat-header">
                                <h3 style={{ margin: 0 }}>💬 AI智能助手</h3>
                                <p style={{ margin: '4px 0 0 0', color: '#666', fontSize: '14px' }}>
                                    与AI助手对话，执行智能自动化任务
                                </p>
                            </div>
                            
                            <div className="quick-commands">
                                {quickCommands.map((cmd, index) => (
                                    <div
                                        key={index}
                                        className="quick-command"
                                        onClick={() => handleQuickCommand(cmd)}
                                    >
                                        {cmd}
                                    </div>
                                ))}
                            </div>
                            
                            <div className="chat-messages">
                                {messages.map(renderMessage)}
                                {isLoading && (
                                    <div className="message assistant">
                                        <div className="message-avatar assistant">🤖</div>
                                        <div className="message-content loading">
                                            AI正在思考...
                                        </div>
                                    </div>
                                )}
                                <div ref={messagesEndRef} />
                            </div>
                            
                            <div className="chat-input">
                                <textarea
                                    className="input-field"
                                    value={inputValue}
                                    onChange={(e) => setInputValue(e.target.value)}
                                    onKeyPress={handleKeyPress}
                                    placeholder="输入您的需求或命令..."
                                    disabled={isLoading}
                                    rows={1}
                                />
                                <button
                                    className="send-button"
                                    onClick={handleSendMessage}
                                    disabled={!inputValue.trim() || isLoading}
                                >
                                    ➤
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }
        
        // 渲染应用
        ReactDOM.render(<AIRPAApp />, document.getElementById('root'));
    </script>
</body>
</html>
