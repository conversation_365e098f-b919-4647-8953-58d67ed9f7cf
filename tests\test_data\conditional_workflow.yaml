id: conditional_workflow
name: 条件工作流示例
version: 1.0.0
description: 包含条件分支的工作流示例
start_at: navigate_home

nodes:
  navigate_home:
    type: operation
    name: 导航到首页
    operation: navigate
    value: https://example.com/login
    output: navigation_result
  
  login:
    type: operation
    name: 执行登录
    operation: fill
    selector: "#login-form"
    value: "{{ variables.username }}"
    depends_on: [navigate_home]
    output: login_result
  
  check_login:
    type: condition
    name: 检查登录状态
    condition:
      type: exists
      selector: ".welcome-message"
    depends_on: [login]
    true_branch: [show_welcome]
    false_branch: [show_error]
  
  show_welcome:
    type: operation
    name: 显示欢迎信息
    operation: extract
    selector: ".welcome-message"
    output: welcome_message
  
  show_error:
    type: operation
    name: 显示错误信息
    operation: extract
    selector: ".error-message"
    output: error_message
    continue_on_failure: true

variables:
  username: testuser
