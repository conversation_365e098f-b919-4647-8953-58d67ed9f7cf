<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>键盘鼠标事件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-area {
            border: 2px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .event-log {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 10px;
            background-color: #f9f9f9;
            font-family: monospace;
        }
        .event-item {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .event-item:last-child {
            border-bottom: none;
        }
        .highlight {
            background-color: #ffffcc;
            transition: background-color 0.5s;
        }
        #mouseTarget {
            width: 200px;
            height: 100px;
            background-color: #e9e9e9;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
            border: 1px solid #ccc;
        }
        #keyInput {
            width: 100%;
            padding: 8px;
            margin: 10px 0;
            font-size: 16px;
        }
        .button-group {
            margin: 10px 0;
        }
        button {
            padding: 8px 16px;
            margin-right: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>键盘鼠标事件测试</h1>
    
    <div class="test-area">
        <h2>键盘事件测试</h2>
        <div>
            <label for="keyInput">在此输入框测试键盘事件：</label>
            <input type="text" id="keyInput" placeholder="输入内容测试键盘事件...">
        </div>
        
        <div class="button-group">
            <button id="focusButton">点击我测试焦点</button>
            <button id="keyboardButton">点击我测试键盘事件</button>
        </div>
        
        <h3>键盘事件日志：</h3>
        <div id="keyboardLog" class="event-log"></div>
    </div>
    
    <div class="test-area">
        <h2>鼠标事件测试</h2>
        <p>在下方区域测试鼠标事件：</p>
        <div id="mouseTarget">
            在此区域移动、点击鼠标
        </div>
        
        <div class="button-group">
            <button id="clickMe">点击我</button>
            <button id="rightClickMe">右键点击我</button>
            <button id="doubleClickMe">双击我</button>
        </div>
        
        <h3>鼠标事件日志：</h3>
        <div id="mouseLog" class="event-log"></div>
    </div>
    
    <div class="test-area">
        <h2>拖拽测试</h2>
        <p>拖拽下方滑块：</p>
        <input type="range" id="slider" min="0" max="100" value="50">
        <p>滑块值: <span id="sliderValue">50</span></p>
        
        <h3>拖拽事件日志：</h3>
        <div id="dragLog" class="event-log"></div>
    </div>
    
    <script>
        // 键盘事件处理
        const keyInput = document.getElementById('keyInput');
        const keyboardLog = document.getElementById('keyboardLog');
        const keyboardButton = document.getElementById('keyboardButton');
        
        // 鼠标事件处理
        const mouseTarget = document.getElementById('mouseTarget');
        const mouseLog = document.getElementById('mouseLog');
        const clickMe = document.getElementById('clickMe');
        const rightClickMe = document.getElementById('rightClickMe');
        const doubleClickMe = document.getElementById('doubleClickMe');
        
        // 拖拽事件处理
        const slider = document.getElementById('slider');
        const sliderValue = document.getElementById('sliderValue');
        const dragLog = document.getElementById('dragLog');
        
        // 日志函数
        function logEvent(logElement, eventType, details = '') {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            const logItem = document.createElement('div');
            logItem.className = 'event-item';
            logItem.textContent = `[${timeString}] ${eventType} ${details ? ': ' + details : ''}`;
            logElement.prepend(logItem);
            
            // 添加高亮效果
            logItem.classList.add('highlight');
            setTimeout(() => {
                logItem.classList.remove('highlight');
            }, 500);
            
            // 限制日志数量
            while (logElement.children.length > 50) {
                logElement.removeChild(logElement.lastChild);
            }
            
            // 输出到控制台
            console.log(`[${eventType}] ${details}`);
        }
        
        // 键盘事件监听
        keyInput.addEventListener('keydown', (e) => {
            logEvent(keyboardLog, 'keydown', `key: ${e.key}, code: ${e.code}`);
        });
        
        keyInput.addEventListener('keyup', (e) => {
            logEvent(keyboardLog, 'keyup', `key: ${e.key}, code: ${e.code}`);
        });
        
        keyInput.addEventListener('keypress', (e) => {
            logEvent(keyboardLog, 'keypress', `key: ${e.key}, charCode: ${e.charCode}`);
        });
        
        // 按钮键盘事件
        keyboardButton.addEventListener('keydown', (e) => {
            logEvent(keyboardLog, 'button keydown', `key: ${e.key}, target: ${e.target.id}`);
        });
        
        // 鼠标事件监听
        const mouseEvents = ['mousedown', 'mouseup', 'click', 'dblclick', 'contextmenu', 'mousemove', 'mouseenter', 'mouseleave'];
        
        mouseEvents.forEach(eventType => {
            mouseTarget.addEventListener(eventType, (e) => {
                // 对于mousemove事件，限制日志频率
                if (eventType === 'mousemove') {
                    if (!mouseTarget.lastMove) mouseTarget.lastMove = 0;
                    const now = Date.now();
                    if (now - mouseTarget.lastMove < 200) return; // 限制为每200ms记录一次
                    mouseTarget.lastMove = now;
                }
                
                logEvent(mouseLog, eventType, 
                    `button: ${e.button}, buttons: ${e.buttons}, ` +
                    `clientX: ${e.clientX}, clientY: ${e.clientY}`);
            });
        });
        
        // 按钮点击事件
        clickMe.addEventListener('click', (e) => {
            logEvent(mouseLog, 'button click', `target: ${e.target.id}`);
            alert('按钮被点击了!');
        });
        
        rightClickMe.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            logEvent(mouseLog, 'right click', `target: ${e.target.id}`);
            alert('右键菜单被触发!');
            return false;
        });
        
        doubleClickMe.addEventListener('dblclick', (e) => {
            logEvent(mouseLog, 'double click', `target: ${e.target.id}`);
            alert('双击事件触发!');
        });
        
        // 拖拽事件
        slider.addEventListener('input', (e) => {
            sliderValue.textContent = e.target.value;
            logEvent(dragLog, 'slider input', `value: ${e.target.value}`);
        });
        
        slider.addEventListener('change', (e) => {
            logEvent(dragLog, 'slider change', `final value: ${e.target.value}`);
        });
        
        // 初始化日志
        logEvent(keyboardLog, '系统', '键盘事件测试已就绪');
        logEvent(mouseLog, '系统', '鼠标事件测试已就绪');
        logEvent(dragLog, '系统', '拖拽测试已就绪');
    </script>
</body>
</html>
