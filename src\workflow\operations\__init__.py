"""
操作抽象层 - 定义标准化的操作模型

该模块提供了用于表示和操作浏览器交互的抽象操作类。
"""
from dataclasses import dataclass, field, asdict
from enum import Enum
from typing import Any, Dict, List, Optional, Type, TypeVar, ClassVar
from dataclasses_json import dataclass_json

# 导出操作工厂
from .factory import OperationFactory, operation_factory, create_operation, create_operation_from_dict, register_operation

# 导出基础类和模型
from .base import (
    Operation, 
    ElementSelector, 
    WaitCondition, 
    RetryStrategy, 
    OperationType
)

# 导出具体操作类
from .operations import (
    ClickOperation,
    FillOperation,
    NavigateOperation,
    WaitOperation,
    ExtractOperation
)

# 导出操作监听器
from .listener import OperationListener

# 导出操作执行器
from .executor import OperationExecutor

# 导出异常类
from .exceptions import (
    OperationError,
    OperationTimeoutError,
    ElementNotFoundError,
    InvalidOperationError
)

# 导出等待条件
from .wait_conditions import (
    BaseWaitCondition,
    ElementVisible,
    ElementHidden,
    ElementContainsText,
    ElementClickable,
    ElementEnabled,
    PageLoaded,
    ElementCount,
    CustomWaitCondition,
    create_wait_condition,
    BUILTIN_WAIT_CONDITIONS
)

# 类型变量
T = TypeVar('T', bound=Operation)

__all__ = [
    # 工厂相关
    'OperationFactory', 'operation_factory', 'create_operation', 'create_operation_from_dict', 'register_operation',
    
    # 基础类和模型
    'Operation', 'ElementSelector', 'WaitCondition', 'RetryStrategy', 'OperationType',
    
    # 具体操作类
    'ClickOperation', 'FillOperation', 'NavigateOperation', 'WaitOperation', 'ExtractOperation',
    
    # 操作监听器
    'OperationListener',
    
    # 操作执行器
    'OperationExecutor',
    
    # 异常类
    'OperationError',
    'OperationTimeoutError',
    'ElementNotFoundError',
    'InvalidOperationError',
    
    # 等待条件
    'BaseWaitCondition', 'ElementVisible', 'ElementHidden', 'ElementContainsText',
    'ElementClickable', 'ElementEnabled', 'PageLoaded', 'ElementCount',
    'CustomWaitCondition', 'create_wait_condition', 'BUILTIN_WAIT_CONDITIONS',
    
    # 类型变量
    'T'
]

class OperationType(str, Enum):
    """操作类型枚举"""
    CLICK = "click"
    FILL = "fill"
    NAVIGATE = "navigate"
    WAIT = "wait"
    EXTRACT = "extract"

class ElementSelector:
    """元素选择器"""
    def __init__(self, selector: str, selector_type: str = "css"):
        """
        初始化元素选择器
        
        Args:
            selector: 选择器值
            selector_type: 选择器类型，支持 css, xpath, text 等
        """
        self.selector = selector
        self.selector_type = selector_type
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典"""
        return {
            "selector": self.selector,
            "type": self.selector_type
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, str]) -> 'ElementSelector':
        """从字典创建实例"""
        return cls(
            selector=data["selector"],
            selector_type=data.get("type", "css")
        )

class WaitCondition:
    """等待条件"""
    def __init__(self, condition: str, value: Any, timeout: float = 30.0):
        """
        初始化等待条件
        
        Args:
            condition: 等待条件类型
            value: 条件值
            timeout: 超时时间(秒)
        """
        self.condition = condition
        self.value = value
        self.timeout = timeout
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "condition": self.condition,
            "value": self.value,
            "timeout": self.timeout
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WaitCondition':
        """从字典创建实例"""
        return cls(
            condition=data["condition"],
            value=data["value"],
            timeout=data.get("timeout", 30.0)
        )

class RetryStrategy:
    """重试策略"""
    def __init__(self, max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0):
        """
        初始化重试策略
        
        Args:
            max_attempts: 最大重试次数
            delay: 初始延迟(秒)
            backoff: 退避乘数
        """
        self.max_attempts = max_attempts
        self.delay = delay
        self.backoff = backoff
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "max_attempts": self.max_attempts,
            "delay": self.delay,
            "backoff": self.backoff
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RetryStrategy':
        """从字典创建实例"""
        return cls(
            max_attempts=data.get("max_attempts", 3),
            delay=data.get("delay", 1.0),
            backoff=data.get("backoff", 2.0)
        )

@dataclass_json
@dataclass
class Operation:
    """基础操作类"""
    id: str
    type: str
    name: str = ""
    description: str = ""
    target: Optional[ElementSelector] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    wait_conditions: List[WaitCondition] = field(default_factory=list)
    retry_strategy: Optional[RetryStrategy] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        if self.target:
            data["target"] = self.target.to_dict()
        if self.retry_strategy:
            data["retry_strategy"] = self.retry_strategy.to_dict()
        data["wait_conditions"] = [wc.to_dict() for wc in self.wait_conditions]
        data["__class__"] = self.__class__.__name__
        return data
    
    @classmethod
    def from_dict(cls: Type[T], data: Dict[str, Any]) -> T:
        """从字典创建实例"""
        data = data.copy()
        
        # 处理target
        if "target" in data and data["target"]:
            data["target"] = ElementSelector.from_dict(data["target"])
        
        # 处理wait_conditions
        if "wait_conditions" in data:
            data["wait_conditions"] = [
                WaitCondition.from_dict(wc) for wc in data["wait_conditions"]
            ]
        
        # 处理retry_strategy
        if "retry_strategy" in data and data["retry_strategy"]:
            data["retry_strategy"] = RetryStrategy.from_dict(data["retry_strategy"])
        
        # 移除可能存在的类信息
        data.pop("__class__", None)
        
        return cls(**data)
    
    def validate(self) -> bool:
        """验证操作是否有效"""
        if not self.id:
            raise ValueError("Operation ID is required")
        if not self.type:
            raise ValueError("Operation type is required")
        return True

@dataclass_json
@dataclass
class ClickOperation(Operation):
    """点击操作"""
    def __post_init__(self):
        self.type = OperationType.CLICK.value
        if not self.target:
            raise ValueError("Target element is required for click operation")

@dataclass_json
@dataclass
class FillOperation(Operation):
    """填充操作"""
    value: str = ""
    
    def __post_init__(self):
        self.type = OperationType.FILL.value
        if not self.target:
            raise ValueError("Target element is required for fill operation")
        if not self.value:
            raise ValueError("Value is required for fill operation")

@dataclass_json
@dataclass
class NavigateOperation(Operation):
    """导航操作"""
    url: str = ""
    
    def __post_init__(self):
        self.type = OperationType.NAVIGATE.value
        if not self.url:
            raise ValueError("URL is required for navigate operation")

@dataclass_json
@dataclass
class WaitOperation(Operation):
    """等待操作"""
    duration: float = 0  # 秒
    
    def __post_init__(self):
        self.type = OperationType.WAIT.value
        if self.duration <= 0 and not self.wait_conditions:
            raise ValueError("Either duration or wait_conditions must be specified for wait operation")

@dataclass_json
@dataclass
class ExtractOperation(Operation):
    """提取操作"""
    attribute: str = "text"
    variable_name: str = ""
    
    def __post_init__(self):
        self.type = OperationType.EXTRACT.value
        if not self.target:
            raise ValueError("Target element is required for extract operation")
        if not self.variable_name:
            raise ValueError("Variable name is required for extract operation")
