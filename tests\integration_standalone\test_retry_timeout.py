"""
独立的集成测试：重试机制和超时处理
"""
import asyncio
import time
import uuid
from typing import Dict, Any, Optional, List, Callable, Type, Tuple

import pytest
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON>er, BrowserContext

class MockFailingOperation:
    """模拟会失败的操作"""
    def __init__(self, fail_times=0, success_after=None, exception=Exception("Test error"), 
                 id=None, timeout=None, max_retries=3, retry_delay=0.1):
        self.fail_times = fail_times
        self.success_after = success_after
        self.exception = exception
        self.attempts = 0
        self.id = id or f"mock_operation_{str(uuid.uuid4())[:8]}"
        self.type = "mock"
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.retry_on = (Exception,)
        self.timeout = timeout
        self.continue_on_failure = False
        self.parameters = {}
        self.wait_conditions = []
        self.metadata = {}
    
    async def execute(self, *args, **kwargs):
        self.attempts += 1
        print(f"执行操作 {self.id}, 尝试次数: {self.attempts}")
        
        # 模拟超时
        if self.timeout and self.attempts == 1:
            print(f"操作 {self.id} 模拟超时...")
            await asyncio.sleep(self.timeout * 1.5)
            
        # 模拟在指定次数后成功
        if self.success_after is not None and self.attempts > self.success_after:
            print(f"操作 {self.id} 第 {self.attempts} 次尝试成功")
            return "success"
            
        # 模拟失败
        if self.attempts <= self.fail_times:
            print(f"操作 {self.id} 第 {self.attempts} 次尝试失败: {self.exception}")
            raise self.exception
            
        print(f"操作 {self.id} 第 {self.attempts} 次尝试成功")
        return "success"
    
    def to_dict(self):
        return {
            "id": self.id,
            "type": self.type,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "timeout": self.timeout
        }

class OperationExecutor:
    """操作执行器"""
    def __init__(self, page: Page, default_timeout: float = 30.0):
        self.page = page
        self.default_timeout = default_timeout
        self.retry_attempts = {}
    
    async def execute_operation(self, operation, **kwargs):
        """执行操作，支持重试和超时"""
        operation_id = getattr(operation, 'id', str(id(operation)))
        self.retry_attempts[operation_id] = 0
        
        # 获取重试参数
        max_retries = getattr(operation, 'max_retries', 0)
        retry_delay = getattr(operation, 'retry_delay', 0.1)
        retry_on = getattr(operation, 'retry_on', (Exception,))
        timeout = getattr(operation, 'timeout', self.default_timeout)
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            self.retry_attempts[operation_id] = attempt
            
            try:
                # 设置超时
                if timeout:
                    result = await asyncio.wait_for(
                        operation.execute(**kwargs),
                        timeout=timeout
                    )
                else:
                    result = await operation.execute(**kwargs)
                    
                # 如果执行成功，返回结果
                return result
                
            except asyncio.TimeoutError:
                last_exception = asyncio.TimeoutError(f"操作 {operation_id} 超时 (超时时间: {timeout}秒)")
                print(f"操作 {operation_id} 超时 (尝试 {attempt + 1}/{max_retries + 1})")
                
            except retry_on as e:
                last_exception = e
                print(f"操作 {operation_id} 失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                
            # 如果不是最后一次尝试，等待重试延迟
            if attempt < max_retries:
                await asyncio.sleep(retry_delay)
        
        # 如果所有重试都失败，抛出异常
        raise OperationError(f"操作 {operation_id} 重试 {max_retries} 次后仍然失败: {last_exception}")

class OperationError(Exception):
    """操作错误"""
    pass

# 测试固件
@pytest.fixture
def event_loop():
    """为测试创建事件循环"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()

@pytest.fixture
async def browser():
    """启动Playwright浏览器"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        yield browser
        await browser.close()

@pytest.fixture
async def page(browser):
    """创建测试页面"""
    page = await browser.new_page()
    await page.goto('about:blank')
    return page

@pytest.fixture
def executor(page):
    """创建操作执行器"""
    return OperationExecutor(page, default_timeout=2.0)

# 测试用例
@pytest.mark.asyncio
async def test_retry_mechanism(executor, page):
    """测试重试机制"""
    # 创建一个会失败2次然后成功的操作
    operation = MockFailingOperation(fail_times=2, max_retries=3, retry_delay=0.1)
    
    # 执行操作
    result = await executor.execute_operation(operation)
    
    # 验证结果
    assert result == "success"
    assert operation.attempts == 3  # 初始尝试 + 2次重试

@pytest.mark.asyncio
async def test_retry_exhausted(executor, page):
    """测试重试次数用尽"""
    # 创建一个总是失败的操作
    operation = MockFailingOperation(fail_times=10, max_retries=2, retry_delay=0.1)
    
    # 执行操作，应该抛出异常
    with pytest.raises(OperationError) as exc_info:
        await executor.execute_operation(operation)
    
    # 验证重试次数
    assert operation.attempts == 3  # 初始尝试 + 2次重试
    assert "重试 2 次后仍然失败" in str(exc_info.value)

@pytest.mark.asyncio
async def test_operation_timeout(executor, page):
    """测试操作超时"""
    # 创建一个会超时的操作
    operation = MockFailingOperation(timeout=1.0, max_retries=0)
    
    # 执行操作，应该抛出超时异常
    with pytest.raises(OperationError) as exc_info:
        await executor.execute_operation(operation)
    
    # 验证错误信息
    assert "超时" in str(exc_info.value)

@pytest.mark.asyncio
async def test_retry_after_timeout(executor, page):
    """测试超时后的重试"""
    # 创建一个第一次会超时，然后成功的操作
    operation = MockFailingOperation(
        success_after=1,  # 第二次成功
        timeout=1.0,      # 1秒超时
        max_retries=3,    # 最多重试3次
        retry_delay=0.1   # 重试延迟0.1秒
    )
    
    # 执行操作
    result = await executor.execute_operation(operation)
    
    # 验证结果
    assert result == "success"
    assert operation.attempts == 2  # 初始尝试(超时) + 1次重试(成功)

@pytest.mark.asyncio
async def test_operation_sequence_with_retry(executor, page):
    """测试带重试的操作序列"""
    # 创建多个操作，其中一些会失败
    operations = [
        MockFailingOperation(id="op1", fail_times=1, max_retries=2, retry_delay=0.1),
        MockFailingOperation(id="op2", fail_times=2, max_retries=3, retry_delay=0.1),
        MockFailingOperation(id="op3", fail_times=0, max_retries=2, retry_delay=0.1)
    ]
    
    # 执行操作序列
    results = []
    for op in operations:
        result = await executor.execute_operation(op)
        results.append((op.id, result))
    
    # 验证所有操作都成功执行
    assert len(results) == 3
    for op_id, result in results:
        assert result == "success", f"操作 {op_id} 失败"
    
    # 验证重试次数
    assert operations[0].attempts == 2  # 失败1次，重试1次
    assert operations[1].attempts == 3  # 失败2次，重试2次
    assert operations[2].attempts == 1  # 没有失败，不需要重试

if __name__ == "__main__":
    # 直接运行测试
    import sys
    pytest.main([sys.argv[0], "-v"])
