"""
真实浏览器操作的聊天窗口测试

能够实际执行浏览器操作的交互模式
"""
import asyncio
import os
import sys
import re
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value


async def test_real_browser_chat():
    """测试真实浏览器操作的聊天窗口"""
    try:
        print("🎭 AI+RPA 真实浏览器操作演示")
        print("=" * 50)
        
        # 加载环境变量
        load_env()
        
        # 导入组件
        from interactive_chat_window import InteractiveChatWindow
        from intelligent_command_processor import get_intelligent_command_processor
        from playwright.async_api import async_playwright
        
        # 创建命令处理器
        command_processor = get_intelligent_command_processor()
        
        # 创建聊天窗口
        chat_window = InteractiveChatWindow(
            title="AI+RPA 真实浏览器操作",
            width=700,
            height=800
        )
        
        # 启动浏览器
        print("🌐 启动浏览器...")
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        print("✅ 浏览器启动成功")
        print("💡 即将打开图形化聊天窗口...")
        
        # 设置消息处理器
        async def handle_message(message):
            """处理用户消息并执行真实操作"""
            try:
                print(f"📝 处理用户消息: {message}")
                
                # 使用命令处理器分析
                result = await command_processor.process_command(message)
                
                # 检查是否是URL导航命令
                url_patterns = [
                    r'打开\s*(https?://[^\s]+)',
                    r'访问\s*(https?://[^\s]+)',
                    r'导航到\s*(https?://[^\s]+)',
                    r'(https?://[^\s]+)',
                ]
                
                # 检查是否是网站导航命令
                site_patterns = [
                    (r'打开\s*百度', 'https://www.baidu.com'),
                    (r'打开\s*谷歌', 'https://www.google.com'),
                    (r'打开\s*GitHub', 'https://github.com'),
                    (r'打开\s*测试网站', 'https://test.yushanyun.net/ac/web/'),
                ]
                
                # 尝试匹配URL
                target_url = None
                for pattern in url_patterns:
                    match = re.search(pattern, message, re.IGNORECASE)
                    if match:
                        target_url = match.group(1)
                        break
                
                # 尝试匹配网站名称
                if not target_url:
                    for pattern, url in site_patterns:
                        if re.search(pattern, message, re.IGNORECASE):
                            target_url = url
                            break
                
                # 执行浏览器操作
                if target_url:
                    try:
                        chat_window.send_response(f"🚀 正在导航到: {target_url}")
                        
                        # 实际导航到URL
                        await page.goto(target_url)
                        await page.wait_for_load_state('networkidle')
                        
                        # 获取页面信息
                        title = await page.title()
                        url = page.url
                        
                        response = f"""✅ 浏览器导航成功！

🎯 目标网站: {target_url}
📍 当前页面: {title}
🔗 当前URL: {url}
🕐 导航时间: {asyncio.get_event_loop().time():.2f}秒

🤖 AI分析:
• 成功识别导航命令
• 自动提取目标URL
• 执行浏览器导航操作
• 验证页面加载完成

💡 您现在可以:
• 输入"分析当前页面"查看页面结构
• 输入其他网站名称继续导航
• 使用自然语言描述操作需求"""
                        
                        chat_window.send_response(response)
                        
                    except Exception as e:
                        error_response = f"""❌ 浏览器导航失败

🎯 目标: {target_url}
❌ 错误: {str(e)}

💡 可能的原因:
• 网络连接问题
• URL格式错误
• 网站无法访问

🔧 建议:
• 检查网络连接
• 尝试其他网站
• 使用完整的URL格式"""
                        
                        chat_window.send_error_message(error_response)
                
                elif result.command_type.value == "analysis":
                    # 页面分析命令
                    try:
                        chat_window.send_response("🔍 正在分析当前页面...")
                        
                        # 获取页面信息
                        title = await page.title()
                        url = page.url
                        
                        # 获取所有链接
                        links = await page.query_selector_all('a[href]')
                        link_texts = []
                        for link in links[:10]:  # 只取前10个
                            text = await link.inner_text()
                            href = await link.get_attribute('href')
                            if text.strip():
                                link_texts.append(f"• {text.strip()} → {href}")
                        
                        # 获取按钮
                        buttons = await page.query_selector_all('button, input[type="button"], input[type="submit"]')
                        button_texts = []
                        for button in buttons[:5]:  # 只取前5个
                            text = await button.inner_text()
                            if text.strip():
                                button_texts.append(f"• {text.strip()}")
                        
                        response = f"""🔍 页面分析完成！

📄 页面标题: {title}
🔗 当前URL: {url}
🔗 发现链接: {len(links)} 个
🔘 发现按钮: {len(buttons)} 个

📊 主要链接:
{chr(10).join(link_texts[:5]) if link_texts else '• 未发现链接'}

🔘 主要按钮:
{chr(10).join(button_texts[:3]) if button_texts else '• 未发现按钮'}

💡 您可以:
• 输入"点击 [链接文本]"进行点击操作
• 输入"导航到 [网站]"访问其他网站
• 输入"帮助"查看更多命令"""
                        
                        chat_window.send_response(response)
                        
                    except Exception as e:
                        chat_window.send_error_message(f"❌ 页面分析失败: {e}")
                
                elif message.startswith("点击"):
                    # 点击操作
                    try:
                        # 提取要点击的文本
                        click_text = message.replace("点击", "").strip()
                        
                        chat_window.send_response(f"🖱️ 正在查找并点击: {click_text}")
                        
                        # 查找包含指定文本的元素
                        elements = await page.query_selector_all(f'a, button, input[type="button"], input[type="submit"]')
                        
                        clicked = False
                        for element in elements:
                            text = await element.inner_text()
                            if click_text.lower() in text.lower():
                                await element.click()
                                clicked = True
                                
                                # 等待页面加载
                                await page.wait_for_load_state('networkidle')
                                
                                new_title = await page.title()
                                new_url = page.url
                                
                                response = f"""✅ 点击操作成功！

🖱️ 点击元素: {text}
📍 新页面: {new_title}
🔗 新URL: {new_url}

💡 页面已更新，您可以继续操作"""
                                
                                chat_window.send_response(response)
                                break
                        
                        if not clicked:
                            chat_window.send_response(f"❌ 未找到包含'{click_text}'的可点击元素")
                            
                    except Exception as e:
                        chat_window.send_error_message(f"❌ 点击操作失败: {e}")
                
                else:
                    # 其他命令使用原有逻辑
                    if result.success:
                        response = f"""✅ 命令识别成功！

🏷️ 命令类型: {result.command_type.value}
⚡ 动作: {result.action}
📊 置信度: {result.confidence:.2f}
🔧 处理方法: {result.processing_method}

💬 AI响应: {result.response}

💡 支持的浏览器操作:
• "打开 [网站名/URL]" - 导航到网站
• "分析当前页面" - 分析页面结构
• "点击 [链接文本]" - 点击页面元素
• "帮助" - 查看使用说明

📋 参数: {result.parameters}"""
                    else:
                        response = f"""❌ 命令识别失败

💬 {result.response}

💡 您可以尝试:
• "打开百度" - 导航到百度
• "打开 https://www.google.com" - 导航到指定URL
• "分析当前页面" - 分析页面结构
• "点击 登录" - 点击页面元素
• "帮助" - 查看使用说明"""
                    
                    chat_window.send_response(response)
                
            except Exception as e:
                error_msg = f"❌ 处理消息时发生错误: {e}"
                print(f"错误详情: {e}")
                chat_window.send_error_message(error_msg)
        
        # 设置消息处理器
        chat_window.set_message_handler(handle_message)
        
        # 启动聊天窗口（异步）
        chat_thread = chat_window.run_async()
        
        # 等待窗口创建
        await asyncio.sleep(2)
        
        # 发送欢迎消息
        welcome_msg = """🎉 欢迎使用 AI+RPA 真实浏览器操作！

这是一个完整的浏览器自动化演示，支持:
• 🌐 真实浏览器导航
• 🔍 页面分析和信息提取
• 🖱️ 元素点击和交互
• 🤖 AI智能命令识别

🚀 试试这些命令:
• "打开百度" - 导航到百度网站
• "打开 https://www.google.com" - 导航到指定URL
• "分析当前页面" - 分析页面结构
• "点击 搜索" - 点击页面元素

现在您可以用自然语言控制浏览器了！"""
        
        chat_window.send_response(welcome_msg)
        
        # 消息处理循环
        print("\n🚀 真实浏览器操作聊天窗口已启动！")
        print("💬 请在图形窗口中输入命令...")
        print("🌐 浏览器已准备就绪，等待您的指令...")
        
        try:
            while chat_window.is_running:
                # 检查用户消息
                user_message = chat_window.get_user_message()
                
                if user_message:
                    await handle_message(user_message)
                
                # 短暂休眠
                await asyncio.sleep(0.1)
        finally:
            # 清理资源
            await browser.close()
            await playwright.stop()
        
        print("👋 聊天窗口已关闭，浏览器已清理")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


async def main():
    """主函数"""
    try:
        await test_real_browser_chat()
    except KeyboardInterrupt:
        print("\n👋 用户取消测试")
    except Exception as e:
        print(f"❌ 程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
