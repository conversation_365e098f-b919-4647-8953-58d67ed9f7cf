"""
简单测试文件操作
"""
import os
import sys
import logging
from pathlib import Path
import pytest
from playwright.sync_api import Page, expect

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入测试辅助函数
from tests.test_utils import get_test_data_path, save_debug_info

def test_simple_page_load(page: Page):
    """测试页面加载"""
    test_url = get_test_data_path("simple_test_page.html")
    logger.info(f"正在导航到: {test_url}")
    
    try:
        # 导航到测试页面
        page.goto(test_url, wait_until="domcontentloaded")
        
        # 等待页面加载完成
        page.wait_for_load_state("networkidle")
        
        # 验证页面标题
        expect(page).to_have_title("Simple Test Page")
        
        # 验证页面内容
        heading = page.locator("h1")
        expect(heading).to_have_text("Simple Test Page")
        
        logger.info("简单页面加载测试通过")
        return True
        
    except Exception as e:
        logger.error(f"简单页面加载测试失败: {str(e)}")
        save_debug_info(page, "test_simple_page_load_failed")
        raise
