"""
操作重试机制集成测试
"""
import pytest
import asyncio
import time
from pathlib import Path
from unittest.mock import patch, MagicMock

from playwright.async_api import async_playwright
from src.workflow.operations.executor import OperationExecutor
from src.workflow.operations.operations import (
    ClickOperation, FillOperation, NavigateOperation, WaitOperation, ExtractOperation
)
from src.workflow.operations.exceptions import OperationError, OperationTimeoutError
from src.workflow.operations.base import ElementSelector

# 测试页面路径
TEST_HTML = Path(__file__).parent.parent.parent.parent / "test_data" / "test_page.html"

# 测试用的元素选择器
TEST_BUTTON_SELECTOR = ElementSelector(css="#test-button")
TEST_INPUT_SELECTOR = ElementSelector(css="#username")

class MockFailingOperation:
    """模拟会失败的操作"""
    def __init__(self, fail_times=0, success_after=None, exception=Exception("Test error"), id=None):
        self.fail_times = fail_times
        self.success_after = success_after
        self.exception = exception
        self.attempts = 0
        self.id = id or f"mock_operation_{id(self)}"
        self.type = "mock"
        self.max_retries = 3  # 默认最大重试次数
        self.retry_delay = 0.1  # 默认重试延迟
        self.retry_on = (Exception,)  # 默认重试所有异常
        self.retry_strategy = None  # 默认使用指数退避
        self.continue_on_failure = False  # 默认失败后不继续
    
    async def execute(self, *args, **kwargs):
        self.attempts += 1
        if self.success_after is not None and self.attempts > self.success_after:
            return "success"
        if self.attempts <= self.fail_times:
            raise self.exception
        return "success"
    
    def to_dict(self):
        """转换为字典，用于序列化"""
        return {
            "id": self.id,
            "type": self.type,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "continue_on_failure": self.continue_on_failure
        }

@pytest.fixture(scope="module")
def event_loop():
    """为测试创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="module")
async def browser():
    """启动Playwright浏览器"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        yield browser
        await browser.close()

@pytest.fixture(scope="module")
async def page(browser):
    """创建测试页面"""
    page = await browser.new_page()
    await page.goto(f"file://{TEST_HTML.absolute()}")
    yield page
    await page.close()

@pytest.fixture
def executor(page):
    """创建操作执行器"""
    return OperationExecutor(page)

@pytest.mark.asyncio
async def test_retry_on_failure(executor, page):
    """测试操作失败时的重试机制"""
    # 创建一个会失败2次然后成功的操作
    operation = MockFailingOperation(fail_times=2)
    
    # 设置重试配置
    operation.max_retries = 3
    operation.retry_delay = 0.1
    operation.retry_on = (Exception,)
    
    # 执行操作
    result = await executor._execute_operation(operation)
    
    # 验证结果
    assert result == "success"
    assert operation.attempts == 3  # 1次初始尝试 + 2次重试

@pytest.mark.asyncio
async def test_retry_with_success_after(executor, page):
    """测试操作在重试后成功的情况"""
    # 创建一个会在第3次尝试时成功的操作
    operation = MockFailingOperation(success_after=2)
    
    # 设置重试配置
    operation.max_retries = 3
    operation.retry_delay = 0.1
    operation.retry_on = (Exception,)
    
    # 执行操作
    result = await executor._execute_operation(operation)
    
    # 验证结果
    assert result == "success"
    assert operation.attempts == 3  # 1次初始尝试 + 2次重试

@pytest.mark.asyncio
async def test_retry_exhausted(executor, page):
    """测试重试次数用尽的情况"""
    # 创建一个总是失败的操作
    operation = MockFailingOperation(fail_times=100)
    
    # 设置重试配置
    operation.max_retries = 2
    operation.retry_delay = 0.1
    operation.retry_on = (Exception,)
    
    # 执行操作，预期会抛出异常
    with pytest.raises(Exception) as exc_info:
        await executor._execute_operation(operation)
    
    # 验证重试次数
    assert operation.attempts == 3  # 1次初始尝试 + 2次重试
    assert "Test error" in str(exc_info.value)

@pytest.mark.asyncio
async def test_retry_specific_exception(executor, page):
    """测试只重试特定异常"""
    class CustomError(Exception):
        pass
    
    # 创建一个会抛出自定义异常的操作
    operation = MockFailingOperation(fail_times=2, exception=CustomError("Custom error"))
    
    # 设置只重试CustomError
    operation.max_retries = 3
    operation.retry_delay = 0.1
    operation.retry_on = (CustomError,)
    
    # 执行操作，预期会重试
    result = await executor._execute_operation(operation)
    assert result == "success"
    assert operation.attempts == 3
    
    # 测试不匹配的异常类型
    operation = MockFailingOperation(fail_times=2, exception=ValueError("Value error"))
    operation.max_retries = 1
    operation.retry_delay = 0.1
    operation.retry_on = (CustomError,)
    
    # 执行操作，预期不会重试
    with pytest.raises(ValueError):
        await executor._execute_operation(operation)
    assert operation.attempts == 1

@pytest.mark.asyncio
async def test_retry_with_exponential_backoff(executor, page):
    """测试指数退避重试策略"""
    # 创建一个会失败3次的操作
    operation = MockFailingOperation(fail_times=3)
    operation.max_retries = 3
    operation.retry_delay = 0.5  # 初始延迟0.5秒
    operation.retry_on = (Exception,)
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行操作
    with pytest.raises(Exception):
        await executor._execute_operation(operation)
    
    # 验证总执行时间应该在合理范围内
    # 预期延迟: 0.5s (第一次重试) + 1.0s (第二次重试) + 2.0s (第三次重试) = ~3.5s
    # 加上一些执行时间缓冲，总共应该在2.5-5秒之间
    elapsed = time.time() - start_time
    assert 2.5 <= elapsed <= 5.0, f"Expected delay between 2.5-5s, got {elapsed:.2f}s"

@pytest.mark.asyncio
async def test_retry_with_custom_retry_on(executor, page):
    """测试自定义重试异常类型"""
    class CustomError(Exception):
        pass
    
    class OtherError(Exception):
        pass
    
    # 创建一个会抛出特定异常的操作
    operation = MockFailingOperation(
        fail_times=2, 
        success_after=2,
        exception=CustomError("Custom error")
    )
    operation.retry_on = (CustomError,)
    
    # 测试重试特定异常
    result = await executor.execute_operation(operation)
    assert result == "success"
    assert operation.attempts == 3
    
    # 测试不重试其他异常
    operation = MockFailingOperation(
        fail_times=1,
        exception=OtherError("Other error")
    )
    operation.retry_on = (CustomError,)
    
    with pytest.raises(OperationError) as exc_info:
        await executor.execute_operation(operation)
    
    assert "OtherError" in str(exc_info.value)
    assert operation.attempts == 1  # 不应该重试

@pytest.mark.asyncio
async def test_retry_with_custom_strategy(executor, page):
    """测试自定义重试策略"""
    # 记录每次重试的时间间隔
    retry_times = []
    
    def custom_strategy(attempt):
        retry_times.append(time.time())
        return 0.1 * (2 ** attempt)  # 指数退避
    
    # 创建一个会失败多次的操作
    operation = MockFailingOperation(fail_times=3, success_after=3)
    operation.retry_strategy = custom_strategy
    
    # 执行操作
    start_time = time.time()
    result = await executor.execute_operation(operation)
    end_time = time.time()
    
    # 验证结果
    assert result == "success"
    assert operation.attempts == 4  # 1次失败 + 3次重试
    
    # 验证重试时间间隔
    assert len(retry_times) == 3
    assert 0.09 < retry_times[0] - start_time < 0.11  # 第一次重试延迟约0.1秒
    assert 0.19 < retry_times[1] - retry_times[0] < 0.21  # 第二次重试延迟约0.2秒
    assert 0.39 < retry_times[2] - retry_times[1] < 0.41  # 第三次重试延迟约0.4秒至少重试了2次，每次间隔0.1秒
    
    # 测试重置统计信息
    executor.reset_retry_stats()
    stats_after_reset = executor.get_retry_stats()
    assert stats_after_reset['total_retries'] == 0
    assert stats_after_reset['operations_retried'] == 0
    assert stats_after_reset['operations_failed'] == 0
    assert not stats_after_reset['operation_retry_count']

@pytest.mark.asyncio
async def test_retry_statistics(executor, page):
    """测试重试统计信息"""
    # 创建两个操作，一个成功，一个失败
    op1 = MockFailingOperation(fail_times=1, success_after=1, id="op1")
    op1.max_retries = 3
    op1.retry_delay = 0.1
    
    op2 = MockFailingOperation(fail_times=3, success_after=4, id="op2")  # 会耗尽重试次数
    op2.max_retries = 2
    op2.retry_delay = 0.1
    
    # 执行操作序列
    results = await executor.execute_sequence([op1, op2])
    
    # 验证统计信息
    assert results["retry_stats"]["total_retries"] == 3  # op1:1 + op2:2
    assert results["retry_stats"]["operations_with_retries"] == 2  # 两个操作都重试了
    assert results["retry_stats"]["failed_after_retries"] == 1  # op2重试后仍然失败
    
    # 验证错误信息
    assert len(results["errors"]) == 1
    assert results["errors"][0]["operation_id"] == "op2"
    assert results["errors"][0]["retries"] == 2  # op2重试了2次
    
    # 验证操作结果
    assert results["operations"][0]["status"] == "success"
    assert results["operations"][1]["status"] == "failed"

@pytest.mark.asyncio
async def test_operation_sequence_with_retry(executor, page):
    """测试带重试的操作序列执行"""
    # 创建多个操作，其中一些会失败
    operations = [
        MockFailingOperation(fail_times=1, success_after=1, id="op1"),  # 会重试1次
        MockFailingOperation(fail_times=2, success_after=2, id="op2"),  # 会重试2次
        MockFailingOperation(fail_times=0, id="op3"),  # 不会失败
        MockFailingOperation(fail_times=3, success_after=3, id="op4"),  # 会重试3次
    ]
    
    # 执行操作序列
    results = await executor.execute_sequence(operations)
    
    # 验证执行结果
    assert results["success"] == True
    assert results["executed"] == 4  # 所有操作都执行了
    assert results["failed"] == 0  # 没有操作最终失败
    
    # 验证重试统计
    assert results["retry_stats"]["total_retries"] == 6  # 1 + 2 + 0 + 3
    assert results["retry_stats"]["operations_with_retries"] == 3  # op1, op2, op4
    assert results["retry_stats"]["failed_after_retries"] == 0  # 没有操作在重试后仍然失败
    
    # 验证每个操作的结果
    assert len(results["operations"]) == 4
    assert all(op["status"] == "success" for op in results["operations"])
    
    # 验证每个操作的重试次数
    retry_counts = {op["id"]: op["retries"] for op in results["operations"]}
    assert retry_counts == {"op1": 1, "op2": 2, "op3": 0, "op4": 3}

@pytest.mark.asyncio
async def test_operation_sequence_with_failure(executor, page):
    """测试操作序列中的失败情况"""
    # 创建一个会失败且不会成功的操作
    operations = [
        MockFailingOperation(fail_times=1, success_after=1, id="op1"),
        MockFailingOperation(fail_times=3, success_after=4, id="op2"),  # 会失败，因为最多重试3次
        MockFailingOperation(fail_times=0, id="op3"),  # 这个操作不会执行
    ]
    
    # 执行操作序列
    results = await executor.execute_sequence(operations)
    
    # 验证执行结果
    assert results["success"] == False  # 整体执行失败
    assert results["executed"] == 2  # 只有前两个操作执行了
    assert results["failed"] == 1  # 一个操作失败
    
    # 验证重试统计
    assert results["retry_stats"]["total_retries"] == 4  # 1 + 3
    assert results["retry_stats"]["operations_with_retries"] == 2  # op1, op2
    assert results["retry_stats"]["failed_after_retries"] == 1  # op2在重试后仍然失败
    
    # 验证操作结果
    assert len(results["operations"]) == 2
    assert results["operations"][0]["status"] == "success"
    assert results["operations"][1]["status"] == "failed"
    assert results["operations"][1]["error"] == "Test error"

@pytest.mark.asyncio
async def test_retry_statistics(executor, page):
    """测试重试统计信息"""
    # 创建两个操作，一个成功，一个失败
    op1 = MockFailingOperation(fail_times=1)
    op1.max_retries = 3
    op1.retry_delay = 0.1
    
    op2 = MockFailingOperation(fail_times=3)  # 会耗尽重试次数
    op2.max_retries = 2
    op2.retry_delay = 0.1
    
    # 执行操作序列
    results = await executor.execute_sequence([op1, op2])
    
    # 验证统计信息
    assert results["retry_stats"]["total_retries"] == 3  # op1:1 + op2:2
    assert results["retry_stats"]["operations_with_retries"] == 2  # 两个操作都重试了
    assert results["retry_stats"]["failed_after_retries"] == 1  # op2重试后仍然失败
    
    # 验证错误信息
    assert len(results["errors"]) == 1
    assert results["errors"][0]["operation_id"] == "mock_operation"
    assert results["errors"][0]["retry_attempted"] is True
    assert results["errors"][0].get("retry_count") == 2  # op2重试了2次
