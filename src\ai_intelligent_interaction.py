"""
AI智能交互模块

实现AI驱动的业务分析、工作流匹配和用户交互功能。
"""
import logging
import time
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class InteractionMode(Enum):
    """交互模式"""
    REQUIREMENT_ANALYSIS = "requirement_analysis"  # 需求分析模式
    WORKFLOW_ANALYSIS = "workflow_analysis"        # 工作流分析模式


@dataclass
class UserRequirement:
    """用户需求"""
    original_text: str
    parsed_intent: str
    required_parameters: List[str]
    confidence: float
    business_domain: Optional[str] = None


@dataclass
class WorkflowMatch:
    """工作流匹配结果"""
    workflow_id: str
    workflow_name: str
    match_confidence: float
    required_parameters: Dict[str, Any]
    estimated_duration: float
    description: str


@dataclass
class InteractionSession:
    """交互会话"""
    session_id: str
    mode: InteractionMode
    user_requirement: UserRequirement
    workflow_matches: List[WorkflowMatch]
    user_parameters: Dict[str, Any]
    status: str  # "analyzing", "waiting_parameters", "ready", "executing", "completed", "error"
    created_at: float
    updated_at: float


class AIBusinessAnalyzer:
    """AI业务分析器"""
    
    def __init__(self):
        """初始化AI业务分析器"""
        self.business_domains = {
            "财务管理": ["财务", "会计", "报表", "发票", "付款", "收款"],
            "人力资源": ["员工", "人事", "薪资", "考勤", "招聘", "培训"],
            "销售管理": ["客户", "订单", "销售", "合同", "报价", "跟进"],
            "库存管理": ["库存", "入库", "出库", "盘点", "采购", "供应商"],
            "项目管理": ["项目", "任务", "进度", "里程碑", "资源", "团队"]
        }
        
        # 模拟已有的基础工作流库
        self.base_workflows = {
            "login_workflow": {
                "name": "用户登录",
                "description": "执行系统登录操作",
                "parameters": ["username", "password"],
                "duration": 10.0,
                "domain": "通用"
            },
            "create_customer": {
                "name": "创建客户",
                "description": "在CRM系统中创建新客户",
                "parameters": ["customer_name", "contact_person", "phone", "email"],
                "duration": 30.0,
                "domain": "销售管理"
            },
            "generate_report": {
                "name": "生成报表",
                "description": "生成业务报表",
                "parameters": ["report_type", "date_range", "department"],
                "duration": 60.0,
                "domain": "财务管理"
            },
            "employee_onboarding": {
                "name": "员工入职",
                "description": "处理新员工入职流程",
                "parameters": ["employee_name", "department", "position", "start_date"],
                "duration": 120.0,
                "domain": "人力资源"
            }
        }
    
    def analyze_user_requirement(self, user_input: str) -> UserRequirement:
        """
        分析用户需求
        
        Args:
            user_input: 用户输入的需求描述
            
        Returns:
            UserRequirement: 解析后的用户需求
        """
        logger.info(f"分析用户需求: {user_input}")
        
        # 简化的需求分析逻辑（实际应该使用NLP模型）
        user_input_lower = user_input.lower()
        
        # 识别业务领域
        business_domain = self._identify_business_domain(user_input_lower)
        
        # 解析意图
        intent = self._parse_intent(user_input_lower)
        
        # 提取所需参数
        required_parameters = self._extract_required_parameters(user_input_lower, intent)
        
        # 计算置信度
        confidence = self._calculate_confidence(user_input_lower, intent, business_domain)
        
        return UserRequirement(
            original_text=user_input,
            parsed_intent=intent,
            required_parameters=required_parameters,
            confidence=confidence,
            business_domain=business_domain
        )
    
    def _identify_business_domain(self, text: str) -> Optional[str]:
        """识别业务领域"""
        for domain, keywords in self.business_domains.items():
            if any(keyword in text for keyword in keywords):
                return domain
        return None
    
    def _parse_intent(self, text: str) -> str:
        """解析用户意图"""
        # 简化的意图识别
        if any(word in text for word in ["创建", "新建", "添加", "录入"]):
            return "create"
        elif any(word in text for word in ["查询", "搜索", "查找", "查看"]):
            return "query"
        elif any(word in text for word in ["修改", "更新", "编辑", "变更"]):
            return "update"
        elif any(word in text for word in ["删除", "移除", "取消"]):
            return "delete"
        elif any(word in text for word in ["生成", "导出", "报表", "统计"]):
            return "generate"
        elif any(word in text for word in ["登录", "登陆", "进入"]):
            return "login"
        else:
            return "unknown"
    
    def _extract_required_parameters(self, text: str, intent: str) -> List[str]:
        """提取所需参数"""
        parameters = []
        
        # 基于意图和文本内容提取参数
        if intent == "create":
            if "客户" in text:
                parameters.extend(["customer_name", "contact_person", "phone", "email"])
            elif "员工" in text:
                parameters.extend(["employee_name", "department", "position", "start_date"])
            elif "订单" in text:
                parameters.extend(["customer_id", "product_list", "quantity", "price"])
        
        elif intent == "query":
            parameters.extend(["search_criteria", "date_range"])
        
        elif intent == "generate":
            if "报表" in text:
                parameters.extend(["report_type", "date_range", "department"])
        
        elif intent == "login":
            parameters.extend(["username", "password"])
        
        return parameters
    
    def _calculate_confidence(self, text: str, intent: str, business_domain: Optional[str]) -> float:
        """计算置信度"""
        confidence = 0.5  # 基础置信度
        
        # 如果识别出业务领域，增加置信度
        if business_domain:
            confidence += 0.2
        
        # 如果意图明确，增加置信度
        if intent != "unknown":
            confidence += 0.2
        
        # 如果文本包含关键词，增加置信度
        if len(text.split()) >= 3:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def find_matching_workflows(self, requirement: UserRequirement) -> List[WorkflowMatch]:
        """
        查找匹配的工作流
        
        Args:
            requirement: 用户需求
            
        Returns:
            List[WorkflowMatch]: 匹配的工作流列表
        """
        logger.info(f"查找匹配工作流: {requirement.parsed_intent}")
        
        matches = []
        
        for workflow_id, workflow_info in self.base_workflows.items():
            # 计算匹配度
            match_confidence = self._calculate_workflow_match(requirement, workflow_info)
            
            if match_confidence > 0.3:  # 只返回匹配度较高的工作流
                matches.append(WorkflowMatch(
                    workflow_id=workflow_id,
                    workflow_name=workflow_info["name"],
                    match_confidence=match_confidence,
                    required_parameters=self._map_parameters(requirement.required_parameters, workflow_info["parameters"]),
                    estimated_duration=workflow_info["duration"],
                    description=workflow_info["description"]
                ))
        
        # 按匹配度排序
        matches.sort(key=lambda x: x.match_confidence, reverse=True)
        
        return matches
    
    def _calculate_workflow_match(self, requirement: UserRequirement, workflow_info: Dict[str, Any]) -> float:
        """计算工作流匹配度"""
        match_score = 0.0
        
        # 业务领域匹配
        if requirement.business_domain and requirement.business_domain == workflow_info.get("domain"):
            match_score += 0.4
        
        # 意图匹配
        intent_keywords = {
            "create": ["创建", "新建"],
            "login": ["登录", "登陆"],
            "generate": ["生成", "报表"]
        }
        
        workflow_name = workflow_info["name"].lower()
        if requirement.parsed_intent in intent_keywords:
            if any(keyword in workflow_name for keyword in intent_keywords[requirement.parsed_intent]):
                match_score += 0.4
        
        # 参数匹配
        common_params = set(requirement.required_parameters) & set(workflow_info["parameters"])
        if workflow_info["parameters"]:
            param_match_ratio = len(common_params) / len(workflow_info["parameters"])
            match_score += param_match_ratio * 0.2
        
        return min(match_score, 1.0)
    
    def _map_parameters(self, required_params: List[str], workflow_params: List[str]) -> Dict[str, Any]:
        """映射参数"""
        mapped_params = {}
        
        for param in workflow_params:
            if param in required_params:
                mapped_params[param] = {"required": True, "source": "user_input"}
            else:
                mapped_params[param] = {"required": True, "source": "user_prompt"}
        
        return mapped_params


class AIInteractionManager:
    """AI交互管理器"""
    
    def __init__(self):
        """初始化AI交互管理器"""
        self.analyzer = AIBusinessAnalyzer()
        self.active_sessions: Dict[str, InteractionSession] = {}
        self.session_counter = 0
    
    def start_requirement_analysis_session(self, user_input: str) -> InteractionSession:
        """
        开始需求分析会话
        
        Args:
            user_input: 用户输入
            
        Returns:
            InteractionSession: 交互会话
        """
        # 生成会话ID
        self.session_counter += 1
        session_id = f"req_session_{self.session_counter}_{int(time.time())}"
        
        # 分析用户需求
        requirement = self.analyzer.analyze_user_requirement(user_input)
        
        # 查找匹配的工作流
        workflow_matches = self.analyzer.find_matching_workflows(requirement)
        
        # 创建会话
        session = InteractionSession(
            session_id=session_id,
            mode=InteractionMode.REQUIREMENT_ANALYSIS,
            user_requirement=requirement,
            workflow_matches=workflow_matches,
            user_parameters={},
            status="analyzing",
            created_at=time.time(),
            updated_at=time.time()
        )
        
        # 更新会话状态
        if workflow_matches:
            session.status = "waiting_parameters"
        else:
            session.status = "error"
        
        self.active_sessions[session_id] = session
        
        logger.info(f"创建需求分析会话: {session_id}")
        return session
    
    def start_workflow_analysis_session(self, user_input: str) -> InteractionSession:
        """
        开始工作流分析会话
        
        Args:
            user_input: 用户输入
            
        Returns:
            InteractionSession: 交互会话
        """
        # 生成会话ID
        self.session_counter += 1
        session_id = f"wf_session_{self.session_counter}_{int(time.time())}"
        
        # 分析用户需求
        requirement = self.analyzer.analyze_user_requirement(user_input)
        
        # 分析现有基础工作流
        workflow_matches = self._analyze_existing_workflows(requirement)
        
        # 创建会话
        session = InteractionSession(
            session_id=session_id,
            mode=InteractionMode.WORKFLOW_ANALYSIS,
            user_requirement=requirement,
            workflow_matches=workflow_matches,
            user_parameters={},
            status="analyzing",
            created_at=time.time(),
            updated_at=time.time()
        )
        
        # 更新会话状态
        if workflow_matches:
            session.status = "waiting_parameters"
        else:
            session.status = "error"
        
        self.active_sessions[session_id] = session
        
        logger.info(f"创建工作流分析会话: {session_id}")
        return session
    
    def _analyze_existing_workflows(self, requirement: UserRequirement) -> List[WorkflowMatch]:
        """分析现有工作流"""
        # 这里应该分析所有已有的基础工作流
        # 并智能组合出满足需求的工作流序列
        return self.analyzer.find_matching_workflows(requirement)
    
    def provide_user_parameters(self, session_id: str, parameters: Dict[str, Any]) -> bool:
        """
        提供用户参数
        
        Args:
            session_id: 会话ID
            parameters: 用户提供的参数
            
        Returns:
            bool: 是否成功
        """
        if session_id not in self.active_sessions:
            logger.error(f"会话不存在: {session_id}")
            return False
        
        session = self.active_sessions[session_id]
        session.user_parameters.update(parameters)
        session.updated_at = time.time()
        
        # 检查是否所有必需参数都已提供
        if self._check_parameters_complete(session):
            session.status = "ready"
            logger.info(f"会话参数完整，准备执行: {session_id}")
        else:
            session.status = "waiting_parameters"
            logger.info(f"会话仍需要更多参数: {session_id}")
        
        return True
    
    def _check_parameters_complete(self, session: InteractionSession) -> bool:
        """检查参数是否完整"""
        if not session.workflow_matches:
            return False
        
        # 检查第一个匹配的工作流的参数
        first_match = session.workflow_matches[0]
        required_params = [param for param, info in first_match.required_parameters.items() 
                          if info.get("required", True)]
        
        provided_params = set(session.user_parameters.keys())
        required_params_set = set(required_params)
        
        return required_params_set.issubset(provided_params)
    
    def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话状态"""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        
        return {
            "session_id": session.session_id,
            "mode": session.mode.value,
            "status": session.status,
            "requirement": {
                "original_text": session.user_requirement.original_text,
                "parsed_intent": session.user_requirement.parsed_intent,
                "confidence": session.user_requirement.confidence,
                "business_domain": session.user_requirement.business_domain
            },
            "workflow_matches": [
                {
                    "workflow_id": match.workflow_id,
                    "workflow_name": match.workflow_name,
                    "match_confidence": match.match_confidence,
                    "description": match.description,
                    "estimated_duration": match.estimated_duration
                }
                for match in session.workflow_matches
            ],
            "provided_parameters": list(session.user_parameters.keys()),
            "missing_parameters": self._get_missing_parameters(session),
            "created_at": session.created_at,
            "updated_at": session.updated_at
        }
    
    def _get_missing_parameters(self, session: InteractionSession) -> List[str]:
        """获取缺失的参数"""
        if not session.workflow_matches:
            return []
        
        first_match = session.workflow_matches[0]
        required_params = [param for param, info in first_match.required_parameters.items() 
                          if info.get("required", True)]
        
        provided_params = set(session.user_parameters.keys())
        required_params_set = set(required_params)
        
        return list(required_params_set - provided_params)
    
    def generate_user_feedback(self, session_id: str) -> Dict[str, Any]:
        """生成用户反馈"""
        if session_id not in self.active_sessions:
            return {"error": "会话不存在"}
        
        session = self.active_sessions[session_id]
        
        feedback = {
            "session_id": session_id,
            "message": "",
            "suggested_workflows": [],
            "required_parameters": [],
            "next_action": ""
        }
        
        if session.status == "analyzing":
            feedback["message"] = "正在分析您的需求，请稍候..."
            feedback["next_action"] = "wait"
            
        elif session.status == "waiting_parameters":
            missing_params = self._get_missing_parameters(session)
            feedback["message"] = f"需要您提供以下参数：{', '.join(missing_params)}"
            feedback["required_parameters"] = missing_params
            feedback["suggested_workflows"] = [
                {
                    "name": match.workflow_name,
                    "description": match.description,
                    "confidence": match.match_confidence
                }
                for match in session.workflow_matches[:3]  # 只显示前3个
            ]
            feedback["next_action"] = "provide_parameters"
            
        elif session.status == "ready":
            feedback["message"] = "参数已完整，准备执行工作流。请确认是否继续？"
            feedback["next_action"] = "confirm_execution"
            
        elif session.status == "error":
            feedback["message"] = "抱歉，无法找到匹配的工作流。请重新描述您的需求。"
            feedback["next_action"] = "retry"
        
        return feedback


# 全局AI交互管理器实例
global_interaction_manager = AIInteractionManager()


def get_interaction_manager() -> AIInteractionManager:
    """获取全局AI交互管理器实例"""
    return global_interaction_manager
