"""
增强OCR服务集成

集成多个OCR服务提供商，提供高精度的文字识别和页面分析功能
"""
import asyncio
import base64
import io
import logging
import os
import time
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from dataclasses import dataclass
from PIL import Image
import json

logger = logging.getLogger(__name__)

# 检查OCR服务可用性
GOOGLE_VISION_AVAILABLE = False
AZURE_CV_AVAILABLE = False
TESSERACT_AVAILABLE = False

try:
    from google.cloud import vision
    GOOGLE_VISION_AVAILABLE = True
    logger.info("Google Vision API 可用")
except ImportError:
    logger.warning("Google Vision API 不可用，请安装: pip install google-cloud-vision")

try:
    from azure.cognitiveservices.vision.computervision import ComputerVisionClient
    from azure.cognitiveservices.vision.computervision.models import OperationStatusCodes
    from msrest.authentication import CognitiveServicesCredentials
    AZURE_CV_AVAILABLE = True
    logger.info("Azure Computer Vision API 可用")
except ImportError:
    logger.warning("Azure Computer Vision API 不可用，请安装: pip install azure-cognitiveservices-vision-computervision")

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
    logger.info("Tesseract OCR 可用")
except ImportError:
    logger.warning("Tesseract OCR 不可用，请安装: pip install pytesseract")


@dataclass
class OCRResult:
    """OCR识别结果"""
    text: str
    confidence: float
    bounding_box: Optional[Dict[str, int]] = None
    language: Optional[str] = None
    provider: Optional[str] = None


@dataclass
class PageAnalysis:
    """页面分析结果"""
    ocr_results: List[OCRResult]
    ui_elements: List[Dict[str, Any]]
    layout_info: Dict[str, Any]
    analysis_time: float
    provider: str
    confidence: float


class GoogleVisionOCR:
    """Google Vision OCR服务"""
    
    def __init__(self):
        """初始化Google Vision客户端"""
        self.client = None
        self.available = GOOGLE_VISION_AVAILABLE
        
        if self.available:
            try:
                # 检查认证
                credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
                if credentials_path and os.path.exists(credentials_path):
                    self.client = vision.ImageAnnotatorClient()
                    logger.info("Google Vision OCR 初始化成功")
                else:
                    logger.warning("Google Vision 认证文件未找到")
                    self.available = False
            except Exception as e:
                logger.error(f"Google Vision OCR 初始化失败: {e}")
                self.available = False
    
    async def analyze_image(self, image_data: bytes) -> List[OCRResult]:
        """分析图像"""
        if not self.available or not self.client:
            raise Exception("Google Vision OCR 不可用")
        
        try:
            # 创建图像对象
            image = vision.Image(content=image_data)
            
            # 执行文字检测
            response = self.client.text_detection(image=image)
            texts = response.text_annotations
            
            if response.error.message:
                raise Exception(f'Google Vision API 错误: {response.error.message}')
            
            results = []
            for text in texts:
                # 获取边界框
                vertices = text.bounding_poly.vertices
                bounding_box = {
                    'x': min(v.x for v in vertices),
                    'y': min(v.y for v in vertices),
                    'width': max(v.x for v in vertices) - min(v.x for v in vertices),
                    'height': max(v.y for v in vertices) - min(v.y for v in vertices)
                }
                
                result = OCRResult(
                    text=text.description,
                    confidence=0.95,  # Google Vision 通常有很高的准确率
                    bounding_box=bounding_box,
                    provider="google_vision"
                )
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Google Vision OCR 分析失败: {e}")
            raise


class AzureComputerVisionOCR:
    """Azure Computer Vision OCR服务"""
    
    def __init__(self):
        """初始化Azure Computer Vision客户端"""
        self.client = None
        self.available = AZURE_CV_AVAILABLE
        
        if self.available:
            try:
                # 检查认证
                key = os.getenv('AZURE_COMPUTER_VISION_KEY')
                endpoint = os.getenv('AZURE_COMPUTER_VISION_ENDPOINT')
                
                if key and endpoint:
                    self.client = ComputerVisionClient(
                        endpoint, 
                        CognitiveServicesCredentials(key)
                    )
                    logger.info("Azure Computer Vision OCR 初始化成功")
                else:
                    logger.warning("Azure Computer Vision 认证信息未配置")
                    self.available = False
            except Exception as e:
                logger.error(f"Azure Computer Vision OCR 初始化失败: {e}")
                self.available = False
    
    async def analyze_image(self, image_data: bytes) -> List[OCRResult]:
        """分析图像"""
        if not self.available or not self.client:
            raise Exception("Azure Computer Vision OCR 不可用")
        
        try:
            # 执行OCR
            read_response = self.client.read_in_stream(
                io.BytesIO(image_data), 
                raw=True
            )
            
            # 获取操作ID
            operation_id = read_response.headers["Operation-Location"].split("/")[-1]
            
            # 等待结果
            while True:
                read_result = self.client.get_read_result(operation_id)
                if read_result.status not in ['notStarted', 'running']:
                    break
                await asyncio.sleep(1)
            
            results = []
            if read_result.status == OperationStatusCodes.succeeded:
                for text_result in read_result.analyze_result.read_results:
                    for line in text_result.lines:
                        # 获取边界框
                        bbox = line.bounding_box
                        bounding_box = {
                            'x': int(min(bbox[0], bbox[2], bbox[4], bbox[6])),
                            'y': int(min(bbox[1], bbox[3], bbox[5], bbox[7])),
                            'width': int(max(bbox[0], bbox[2], bbox[4], bbox[6]) - min(bbox[0], bbox[2], bbox[4], bbox[6])),
                            'height': int(max(bbox[1], bbox[3], bbox[5], bbox[7]) - min(bbox[1], bbox[3], bbox[5], bbox[7]))
                        }
                        
                        result = OCRResult(
                            text=line.text,
                            confidence=0.90,  # Azure CV 也有很高的准确率
                            bounding_box=bounding_box,
                            provider="azure_cv"
                        )
                        results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Azure Computer Vision OCR 分析失败: {e}")
            raise


class TesseractOCR:
    """Tesseract OCR服务"""
    
    def __init__(self):
        """初始化Tesseract OCR"""
        self.available = TESSERACT_AVAILABLE
        
        if self.available:
            try:
                # 测试Tesseract是否可用
                pytesseract.get_tesseract_version()
                logger.info("Tesseract OCR 初始化成功")
            except Exception as e:
                logger.error(f"Tesseract OCR 初始化失败: {e}")
                self.available = False
    
    async def analyze_image(self, image_data: bytes) -> List[OCRResult]:
        """分析图像"""
        if not self.available:
            raise Exception("Tesseract OCR 不可用")
        
        try:
            # 将字节数据转换为PIL图像
            image = Image.open(io.BytesIO(image_data))
            
            # 执行OCR
            text = pytesseract.image_to_string(image, lang='chi_sim+eng')
            
            # 获取详细信息
            data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
            
            results = []
            for i, word in enumerate(data['text']):
                if word.strip():  # 忽略空文本
                    confidence = float(data['conf'][i]) / 100.0  # 转换为0-1范围
                    
                    bounding_box = {
                        'x': data['left'][i],
                        'y': data['top'][i],
                        'width': data['width'][i],
                        'height': data['height'][i]
                    }
                    
                    result = OCRResult(
                        text=word,
                        confidence=confidence,
                        bounding_box=bounding_box,
                        provider="tesseract"
                    )
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Tesseract OCR 分析失败: {e}")
            raise


class EnhancedOCRAnalyzer:
    """增强OCR分析器"""
    
    def __init__(self):
        """初始化OCR分析器"""
        self.providers = {}
        
        # 初始化各个OCR提供商
        if GOOGLE_VISION_AVAILABLE:
            self.providers['google_vision'] = GoogleVisionOCR()
        
        if AZURE_CV_AVAILABLE:
            self.providers['azure_cv'] = AzureComputerVisionOCR()
        
        if TESSERACT_AVAILABLE:
            self.providers['tesseract'] = TesseractOCR()
        
        # 设置默认提供商优先级
        self.provider_priority = ['google_vision', 'azure_cv', 'tesseract']
        
        logger.info(f"OCR分析器初始化完成，可用提供商: {list(self.providers.keys())}")
    
    def get_available_providers(self) -> List[str]:
        """获取可用的OCR提供商"""
        return [name for name, provider in self.providers.items() if provider.available]
    
    async def analyze_page(self, page, screenshot_path: Optional[str] = None) -> PageAnalysis:
        """分析页面"""
        start_time = time.time()
        
        try:
            # 获取页面截图
            if screenshot_path:
                with open(screenshot_path, 'rb') as f:
                    image_data = f.read()
            else:
                # 使用Playwright截图
                screenshot_bytes = await page.screenshot()
                image_data = screenshot_bytes
            
            # 选择最佳OCR提供商
            provider_name = self._select_best_provider()
            if not provider_name:
                raise Exception("没有可用的OCR提供商")
            
            provider = self.providers[provider_name]
            
            # 执行OCR分析
            ocr_results = await provider.analyze_image(image_data)
            
            # 分析UI元素
            ui_elements = await self._analyze_ui_elements(page, ocr_results)
            
            # 分析页面布局
            layout_info = await self._analyze_layout(page, ocr_results)
            
            analysis_time = time.time() - start_time
            
            # 计算整体置信度
            if ocr_results:
                avg_confidence = sum(r.confidence for r in ocr_results) / len(ocr_results)
            else:
                avg_confidence = 0.0
            
            return PageAnalysis(
                ocr_results=ocr_results,
                ui_elements=ui_elements,
                layout_info=layout_info,
                analysis_time=analysis_time,
                provider=provider_name,
                confidence=avg_confidence
            )
            
        except Exception as e:
            logger.error(f"页面分析失败: {e}")
            raise
    
    def _select_best_provider(self) -> Optional[str]:
        """选择最佳OCR提供商"""
        for provider_name in self.provider_priority:
            if provider_name in self.providers and self.providers[provider_name].available:
                return provider_name
        return None
    
    async def _analyze_ui_elements(self, page, ocr_results: List[OCRResult]) -> List[Dict[str, Any]]:
        """分析UI元素"""
        try:
            # 获取页面元素
            elements = await page.query_selector_all('*')
            ui_elements = []
            
            for element in elements[:50]:  # 限制元素数量
                try:
                    tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                    is_visible = await element.is_visible()
                    
                    if is_visible and tag_name in ['button', 'input', 'a', 'select', 'textarea']:
                        bounding_box = await element.bounding_box()
                        text_content = await element.text_content()
                        
                        ui_element = {
                            'tag_name': tag_name,
                            'text': text_content or '',
                            'bounding_box': bounding_box,
                            'is_visible': is_visible
                        }
                        ui_elements.append(ui_element)
                        
                except Exception:
                    continue  # 忽略单个元素的错误
            
            return ui_elements
            
        except Exception as e:
            logger.error(f"UI元素分析失败: {e}")
            return []
    
    async def _analyze_layout(self, page, ocr_results: List[OCRResult]) -> Dict[str, Any]:
        """分析页面布局"""
        try:
            # 获取页面基本信息
            title = await page.title()
            url = page.url
            viewport = page.viewport_size
            
            # 分析文本分布
            text_regions = []
            for result in ocr_results:
                if result.bounding_box:
                    text_regions.append({
                        'text': result.text,
                        'x': result.bounding_box['x'],
                        'y': result.bounding_box['y'],
                        'width': result.bounding_box['width'],
                        'height': result.bounding_box['height']
                    })
            
            layout_info = {
                'title': title,
                'url': url,
                'viewport': viewport,
                'text_regions_count': len(text_regions),
                'total_text_length': sum(len(r.text) for r in ocr_results),
                'text_regions': text_regions[:20]  # 限制返回的区域数量
            }
            
            return layout_info
            
        except Exception as e:
            logger.error(f"布局分析失败: {e}")
            return {}


# 全局实例
global_ocr_analyzer = EnhancedOCRAnalyzer()


def get_enhanced_ocr_analyzer() -> EnhancedOCRAnalyzer:
    """获取全局增强OCR分析器实例"""
    return global_ocr_analyzer


async def test_ocr_integration():
    """测试OCR集成"""
    print("🧪 测试增强OCR集成")

    analyzer = get_enhanced_ocr_analyzer()

    print(f"可用OCR提供商: {analyzer.get_available_providers()}")

    if not analyzer.get_available_providers():
        print("❌ 没有可用的OCR提供商")
        print("💡 配置建议:")
        print("  - Google Vision: 设置 GOOGLE_APPLICATION_CREDENTIALS")
        print("  - Azure CV: 设置 AZURE_COMPUTER_VISION_KEY 和 AZURE_COMPUTER_VISION_ENDPOINT")
        print("  - Tesseract: 安装 pytesseract")
        return

    # 测试每个可用的提供商
    for provider_name in analyzer.get_available_providers():
        print(f"\n🔍 测试 {provider_name} 提供商:")
        provider = analyzer.providers[provider_name]

        try:
            # 创建一个简单的测试图像（白底黑字）
            test_image = create_test_image()
            results = await provider.analyze_image(test_image)

            if results:
                print(f"   ✅ {provider_name} 测试成功，识别到 {len(results)} 个文本区域")
                for i, result in enumerate(results[:3]):  # 只显示前3个结果
                    print(f"   📝 文本{i+1}: {result.text[:50]}...")
            else:
                print(f"   ⚠️  {provider_name} 测试完成，但未识别到文本")

        except Exception as e:
            print(f"   ❌ {provider_name} 测试失败: {e}")

    print("\n✅ OCR集成测试完成")


def create_test_image() -> bytes:
    """创建测试图像"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import io

        # 创建白底图像
        img = Image.new('RGB', (400, 100), color='white')
        draw = ImageDraw.Draw(img)

        # 添加测试文本
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            # 使用默认字体
            font = ImageFont.load_default()

        draw.text((10, 30), "Hello World! 测试文本", fill='black', font=font)

        # 转换为字节
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        return img_bytes.getvalue()

    except ImportError:
        # 如果PIL不可用，返回空字节
        logger.warning("PIL不可用，无法创建测试图像")
        return b''


if __name__ == "__main__":
    asyncio.run(test_ocr_integration())
