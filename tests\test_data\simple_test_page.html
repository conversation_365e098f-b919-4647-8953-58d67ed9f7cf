<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #test-button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 20px 0;
        }
        #test-button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>Simple Test Page</h1>
    <p>This is a simple test page for Playwright automation.</p>
    
    <button id="test-button" data-testid="test-button">
        Click Me
    </button>

    <script>
        // 初始化页面状态
        window.buttonClicked = false;
        
        // 添加按钮点击事件监听
        document.getElementById('test-button').addEventListener('click', function() {
            window.buttonClicked = true;
            console.log('Test button clicked');
        });
        
        // 页面加载完成后打印消息
        window.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
        });
    </script>
</body>
</html>
