"""
通用图片识别业务节点测试

测试通用图片识别方案和JSON工作流集成
"""
import asyncio
import os
import sys
import json
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"✅ 加载环境变量: {key}")


def test_imports():
    """测试导入"""
    print("🧪 测试通用图片识别业务节点导入")
    print("-" * 40)
    
    try:
        from universal_image_recognition import (
            UniversalImageRecognizer,
            ImageRecognitionConfig,
            ImageRecognitionType,
            RecognitionMethod,
            ImageRecognitionResult
        )
        print("   ✅ universal_image_recognition 导入成功")
        
        from workflow_image_recognition_node import (
            ImageRecognitionWorkflowNode,
            WorkflowImageRecognitionGenerator,
            ImageRecognitionWorkflowTemplates,
            get_image_recognition_workflow_node
        )
        print("   ✅ workflow_image_recognition_node 导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        return False


async def test_universal_recognizer():
    """测试通用图片识别器"""
    print("\n🧪 测试通用图片识别器")
    print("-" * 40)
    
    try:
        from universal_image_recognition import (
            UniversalImageRecognizer,
            ImageRecognitionConfig,
            ImageRecognitionType,
            RecognitionMethod
        )
        
        recognizer = UniversalImageRecognizer()
        print("   ✅ 通用图片识别器创建成功")
        
        # 测试配置创建
        config = ImageRecognitionConfig(
            recognition_type=ImageRecognitionType.CAPTCHA,
            recognition_method=RecognitionMethod.OCR_THEN_AI,
            target_selectors=["img[alt*='验证码']"],
            input_selectors=["input[name='captcha']"]
        )
        print("   ✅ 图片识别配置创建成功")
        
        # 测试配置序列化
        config_dict = config.to_dict()
        print(f"   ✅ 配置序列化成功: {len(config_dict)} 个字段")
        
        # 测试配置反序列化
        config_restored = ImageRecognitionConfig.from_dict(config_dict)
        print("   ✅ 配置反序列化成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 通用图片识别器测试失败: {e}")
        return False


async def test_workflow_node():
    """测试工作流节点"""
    print("\n🧪 测试图片识别工作流节点")
    print("-" * 40)
    
    try:
        from workflow_image_recognition_node import (
            get_image_recognition_workflow_node,
            WorkflowImageRecognitionGenerator
        )
        from workflow.models import Step
        from universal_image_recognition import RecognitionMethod
        
        node = get_image_recognition_workflow_node()
        print("   ✅ 图片识别工作流节点创建成功")
        
        # 测试验证码识别步骤生成
        captcha_step = WorkflowImageRecognitionGenerator.create_captcha_recognition_step(
            step_id="test_captcha",
            target_selectors=["img[alt*='验证码']"],
            input_selectors=["input[name='captcha']"],
            recognition_method=RecognitionMethod.OCR_THEN_AI
        )
        print("   ✅ 验证码识别步骤生成成功")
        print(f"      步骤ID: {captcha_step.id}")
        print(f"      步骤类型: {captcha_step.type}")
        print(f"      步骤描述: {captcha_step.description}")
        
        # 测试二维码识别步骤生成
        qr_step = WorkflowImageRecognitionGenerator.create_qr_code_recognition_step(
            step_id="test_qr",
            target_selectors=[".qr-code img"],
            recognition_method=RecognitionMethod.AI_ONLY
        )
        print("   ✅ 二维码识别步骤生成成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 工作流节点测试失败: {e}")
        return False


def test_workflow_templates():
    """测试工作流模板"""
    print("\n🧪 测试图片识别工作流模板")
    print("-" * 40)
    
    try:
        from workflow_image_recognition_node import ImageRecognitionWorkflowTemplates
        
        # 测试验证码登录工作流模板
        captcha_template = ImageRecognitionWorkflowTemplates.get_captcha_login_workflow_template()
        print("   ✅ 验证码登录工作流模板生成成功")
        print(f"      模板名称: {captcha_template['name']}")
        print(f"      步骤数量: {len(captcha_template['steps'])}")
        print(f"      变量数量: {len(captcha_template['variables'])}")
        
        # 检查图片识别步骤
        image_recognition_steps = [
            step for step in captcha_template['steps'] 
            if step['type'] == 'image_recognition'
        ]
        print(f"      图片识别步骤: {len(image_recognition_steps)} 个")
        
        if image_recognition_steps:
            step = image_recognition_steps[0]
            config = step['metadata']['recognition_config']
            print(f"      识别类型: {config['recognition_type']}")
            print(f"      识别方法: {config['recognition_method']}")
            print(f"      目标选择器: {len(config['target_selectors'])} 个")
            print(f"      输入选择器: {len(config['input_selectors'])} 个")
        
        # 测试多图片识别工作流模板
        multi_template = ImageRecognitionWorkflowTemplates.get_multi_image_recognition_workflow_template()
        print("   ✅ 多图片识别工作流模板生成成功")
        print(f"      模板名称: {multi_template['name']}")
        print(f"      步骤数量: {len(multi_template['steps'])}")
        
        # 测试自适应识别工作流模板
        adaptive_template = ImageRecognitionWorkflowTemplates.get_adaptive_recognition_workflow_template()
        print("   ✅ 自适应识别工作流模板生成成功")
        print(f"      模板名称: {adaptive_template['name']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 工作流模板测试失败: {e}")
        return False


def test_json_serialization():
    """测试JSON序列化"""
    print("\n🧪 测试JSON工作流序列化")
    print("-" * 40)
    
    try:
        from workflow_image_recognition_node import ImageRecognitionWorkflowTemplates
        
        # 获取验证码登录工作流模板
        template = ImageRecognitionWorkflowTemplates.get_captcha_login_workflow_template()
        
        # 序列化为JSON
        json_str = json.dumps(template, indent=2, ensure_ascii=False)
        print("   ✅ JSON序列化成功")
        print(f"      JSON长度: {len(json_str)} 字符")
        
        # 反序列化
        restored_template = json.loads(json_str)
        print("   ✅ JSON反序列化成功")
        
        # 验证关键字段
        assert restored_template['name'] == template['name']
        assert len(restored_template['steps']) == len(template['steps'])
        assert len(restored_template['variables']) == len(template['variables'])
        print("   ✅ JSON数据完整性验证通过")
        
        # 保存到文件
        output_file = "test_image_recognition_workflow.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, indent=2, ensure_ascii=False)
        print(f"   ✅ JSON工作流已保存: {output_file}")
        
        # 清理测试文件
        try:
            Path(output_file).unlink()
        except Exception:
            pass
        
        return True
        
    except Exception as e:
        print(f"   ❌ JSON序列化测试失败: {e}")
        return False


async def test_real_workflow_execution():
    """测试真实工作流执行"""
    print("\n🧪 测试真实工作流执行")
    print("-" * 40)
    
    try:
        from playwright.async_api import async_playwright
        from workflow_image_recognition_node import get_image_recognition_workflow_node
        from workflow.models import Step
        from universal_image_recognition import (
            ImageRecognitionConfig,
            ImageRecognitionType,
            RecognitionMethod
        )
        
        print("   🌐 启动浏览器进行真实测试...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            
            try:
                # 访问登录页面
                print("   📱 访问登录页面...")
                await page.goto("https://test.yushanyun.net/ac/web/")
                await page.wait_for_load_state('networkidle')
                
                # 创建图片识别步骤
                config = ImageRecognitionConfig(
                    recognition_type=ImageRecognitionType.CAPTCHA,
                    recognition_method=RecognitionMethod.OCR_THEN_AI,
                    target_selectors=[
                        "img[alt*='验证码']",
                        "img[src*='captcha']",
                        "#captcha_img"
                    ],
                    input_selectors=[
                        "input[name='captcha']",
                        "input[placeholder*='验证码']"
                    ],
                    ocr_confidence_threshold=0.7,
                    ai_confidence_threshold=0.6,
                    max_retry_count=2
                )
                
                step = Step(
                    id="test_image_recognition",
                    type="image_recognition",
                    description="测试图片识别",
                    metadata={
                        "recognition_config": config.to_dict(),
                        "node_type": "captcha_recognition"
                    }
                )
                
                # 执行图片识别节点
                print("   🚀 执行图片识别节点...")
                node = get_image_recognition_workflow_node()
                result = await node.execute(page, step)
                
                print(f"   📊 图片识别节点执行结果:")
                print(f"      执行成功: {'✅' if result['success'] else '❌'}")
                print(f"      识别文本: '{result.get('recognized_text', '')}'")
                print(f"      识别方法: {result.get('method_used', '')}")
                print(f"      识别置信度: {result.get('confidence', 0):.2f}")
                print(f"      处理时间: {result.get('processing_time', 0):.2f}秒")
                
                if result.get('error_message'):
                    print(f"      错误信息: {result['error_message']}")
                
                if result['success']:
                    print(f"   🎉 通用图片识别业务节点执行成功！")
                else:
                    print(f"   ❌ 通用图片识别业务节点执行失败")
                
                # 保持浏览器打开以便查看
                input("\n   👀 请查看浏览器中的结果，按回车关闭...")
                
            finally:
                await browser.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 真实工作流执行测试失败: {e}")
        return False


def show_usage_examples():
    """显示使用示例"""
    print("\n💡 通用图片识别业务节点使用示例")
    print("=" * 60)
    
    print("\n1. 创建验证码识别步骤:")
    print("""
from workflow_image_recognition_node import WorkflowImageRecognitionGenerator
from universal_image_recognition import RecognitionMethod

# 创建验证码识别步骤
step = WorkflowImageRecognitionGenerator.create_captcha_recognition_step(
    step_id="captcha_step",
    target_selectors=["img[alt*='验证码']", "#captcha_img"],
    input_selectors=["input[name='captcha']"],
    recognition_method=RecognitionMethod.OCR_THEN_AI,
    ocr_confidence_threshold=0.7,
    ai_confidence_threshold=0.6
)
""")
    
    print("\n2. JSON工作流模板:")
    print("""
from workflow_image_recognition_node import ImageRecognitionWorkflowTemplates
import json

# 获取验证码登录工作流模板
template = ImageRecognitionWorkflowTemplates.get_captcha_login_workflow_template()

# 保存为JSON文件
with open("login_workflow.json", "w", encoding="utf-8") as f:
    json.dump(template, f, indent=2, ensure_ascii=False)

# 后续可以直接加载JSON工作流执行
""")
    
    print("\n3. 执行图片识别节点:")
    print("""
from workflow_image_recognition_node import get_image_recognition_workflow_node

# 获取节点实例
node = get_image_recognition_workflow_node()

# 执行图片识别
result = await node.execute(page, step)

if result['success']:
    print(f"识别成功: {result['recognized_text']}")
    print(f"使用方法: {result['method_used']}")
""")
    
    print("\n4. 自定义识别配置:")
    print("""
from universal_image_recognition import ImageRecognitionConfig, ImageRecognitionType, RecognitionMethod

# 自定义二维码识别配置
config = ImageRecognitionConfig(
    recognition_type=ImageRecognitionType.QR_CODE,
    recognition_method=RecognitionMethod.AI_ONLY,
    target_selectors=[".qr-code img"],
    input_selectors=[],
    ai_prompt_template="请识别这个二维码中的内容，直接返回链接或文本。"
)
""")


async def main():
    """主函数"""
    print("🎭 通用图片识别业务节点系统测试")
    print("验证通用图片识别方案和JSON工作流集成")
    print("=" * 70)
    
    # 加载环境变量
    load_env()
    
    # 检查AI配置
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        print("❌ 缺少GEMINI_API_KEY配置")
        return
    
    print("✅ AI配置检查通过")
    
    # 检查OpenCV
    try:
        import cv2
        print(f"✅ OpenCV可用 (版本: {cv2.__version__})")
    except ImportError:
        print("❌ OpenCV不可用，请安装: pip install opencv-python")
        return
    
    # 运行测试
    tests = [
        ("模块导入", test_imports),
        ("通用图片识别器", test_universal_recognizer),
        ("工作流节点", test_workflow_node),
        ("工作流模板", test_workflow_templates),
        ("JSON序列化", test_json_serialization)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                results[test_name] = await test_func()
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    print("\n" + "=" * 70)
    print("📊 通用图片识别业务节点测试报告")
    print("=" * 70)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！通用图片识别业务节点系统准备就绪！")
        print(f"\n🚀 系统功能:")
        print(f"   ✅ 通用图片识别器")
        print(f"   ✅ 多种识别类型支持")
        print(f"   ✅ 灵活的识别方法")
        print(f"   ✅ 工作流节点集成")
        print(f"   ✅ JSON工作流模板")
        print(f"   ✅ 配置序列化/反序列化")
        
        print(f"\n🔧 支持的识别类型:")
        print(f"   • 验证码 (CAPTCHA)")
        print(f"   • 二维码 (QR_CODE)")
        print(f"   • 条形码 (BARCODE)")
        print(f"   • 文本OCR (TEXT_OCR)")
        print(f"   • 文档识别 (DOCUMENT)")
        print(f"   • 截图分析 (SCREENSHOT)")
        print(f"   • 自定义识别 (CUSTOM)")
        
        print(f"\n⚙️ 支持的识别方法:")
        print(f"   • 仅OCR (OCR_ONLY)")
        print(f"   • 仅AI (AI_ONLY)")
        print(f"   • OCR失败后AI (OCR_THEN_AI)")
        print(f"   • AI失败后OCR (AI_THEN_OCR)")
        print(f"   • 并行识别 (PARALLEL)")
        print(f"   • 手动输入 (MANUAL)")
        
        # 询问是否进行真实测试
        real_test = input(f"\n🧪 是否进行真实工作流执行测试? (y/n): ").strip().lower()
        if real_test in ['y', 'yes']:
            await test_real_workflow_execution()
        
        # 显示使用示例
        show_usage_examples()
        
        print(f"\n🎯 通用图片识别业务节点优势:")
        print(f"   • 标准化的工作流节点")
        print(f"   • JSON配置驱动")
        print(f"   • 多种识别场景支持")
        print(f"   • 灵活的AI协助策略")
        print(f"   • 完整的错误处理")
        print(f"   • 可复用的工作流模板")
        
        print(f"\n🏆 现在您可以:")
        print(f"   • 创建包含图片识别的JSON工作流")
        print(f"   • 灵活适配各种图片识别场景")
        print(f"   • 使用AI协助提高识别准确率")
        print(f"   • 保存和复用工作流模板")
        
    else:
        print(f"\n❌ 部分测试失败，需要检查配置")


if __name__ == "__main__":
    asyncio.run(main())
