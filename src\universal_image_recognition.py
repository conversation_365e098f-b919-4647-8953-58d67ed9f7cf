"""
通用图片识别业务节点

支持验证码、二维码、文档等各种图片识别场景的通用解决方案
"""
import asyncio
import logging
import time
import json
import base64
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

from ai_llm_manager import get_llm_manager, LLMProvider
from opencv_ocr_processor import OpenCVOCREngine
from ai_captcha_handler import AICaptchaDetector

logger = logging.getLogger(__name__)


class ImageRecognitionType(Enum):
    """图片识别类型"""
    CAPTCHA = "captcha"          # 验证码
    QR_CODE = "qr_code"          # 二维码
    BARCODE = "barcode"          # 条形码
    TEXT_OCR = "text_ocr"        # 文本OCR
    DOCUMENT = "document"        # 文档识别
    SCREENSHOT = "screenshot"    # 截图分析
    CUSTOM = "custom"            # 自定义识别


class RecognitionMethod(Enum):
    """识别方法"""
    OCR_ONLY = "ocr_only"        # 仅OCR
    AI_ONLY = "ai_only"          # 仅AI
    OCR_THEN_AI = "ocr_then_ai"  # OCR失败后AI
    AI_THEN_OCR = "ai_then_ocr"  # AI失败后OCR
    PARALLEL = "parallel"        # 并行识别
    MANUAL = "manual"            # 手动输入


@dataclass
class ImageRecognitionConfig:
    """图片识别配置"""
    recognition_type: ImageRecognitionType
    recognition_method: RecognitionMethod
    target_selectors: List[str]  # 目标图片选择器
    input_selectors: List[str]   # 输入框选择器
    ocr_confidence_threshold: float = 0.7
    ai_confidence_threshold: float = 0.6
    max_retry_count: int = 3
    timeout_seconds: int = 30
    preprocessing_options: Dict[str, Any] = None
    ai_prompt_template: str = ""
    validation_rules: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "recognition_type": self.recognition_type.value,
            "recognition_method": self.recognition_method.value,
            "target_selectors": self.target_selectors,
            "input_selectors": self.input_selectors,
            "ocr_confidence_threshold": self.ocr_confidence_threshold,
            "ai_confidence_threshold": self.ai_confidence_threshold,
            "max_retry_count": self.max_retry_count,
            "timeout_seconds": self.timeout_seconds,
            "preprocessing_options": self.preprocessing_options or {},
            "ai_prompt_template": self.ai_prompt_template,
            "validation_rules": self.validation_rules or {}
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ImageRecognitionConfig':
        return cls(
            recognition_type=ImageRecognitionType(data["recognition_type"]),
            recognition_method=RecognitionMethod(data["recognition_method"]),
            target_selectors=data["target_selectors"],
            input_selectors=data["input_selectors"],
            ocr_confidence_threshold=data.get("ocr_confidence_threshold", 0.7),
            ai_confidence_threshold=data.get("ai_confidence_threshold", 0.6),
            max_retry_count=data.get("max_retry_count", 3),
            timeout_seconds=data.get("timeout_seconds", 30),
            preprocessing_options=data.get("preprocessing_options", {}),
            ai_prompt_template=data.get("ai_prompt_template", ""),
            validation_rules=data.get("validation_rules", {})
        )


@dataclass
class ImageRecognitionResult:
    """图片识别结果"""
    success: bool
    recognized_text: str
    confidence: float
    method_used: str
    processing_time: float
    image_path: str
    retry_count: int = 0
    error_message: str = ""
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class UniversalImageRecognizer:
    """通用图片识别器"""
    
    def __init__(self):
        self.llm_manager = get_llm_manager()
        self.ocr_engine = OpenCVOCREngine()
        self.detector = AICaptchaDetector()
    
    async def recognize_image(self, page, config: ImageRecognitionConfig) -> ImageRecognitionResult:
        """通用图片识别"""
        start_time = time.time()
        
        try:
            logger.info(f"开始通用图片识别: {config.recognition_type.value}")
            
            # 查找目标图片
            image_path = await self._capture_target_image(page, config)
            if not image_path:
                return ImageRecognitionResult(
                    success=False,
                    recognized_text="",
                    confidence=0.0,
                    method_used="capture",
                    processing_time=time.time() - start_time,
                    image_path="",
                    error_message="未找到目标图片"
                )
            
            # 根据配置选择识别方法
            result = await self._execute_recognition_method(image_path, config)
            
            # 验证识别结果
            if result.success and config.validation_rules:
                result = self._validate_recognition_result(result, config.validation_rules)
            
            # 填写识别结果
            if result.success and config.input_selectors:
                fill_success = await self._fill_recognition_result(page, result.recognized_text, config.input_selectors)
                if not fill_success:
                    result.success = False
                    result.error_message = "填写识别结果失败"
            
            result.processing_time = time.time() - start_time
            result.image_path = image_path
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"通用图片识别异常: {e}"
            logger.error(error_msg)
            
            return ImageRecognitionResult(
                success=False,
                recognized_text="",
                confidence=0.0,
                method_used="error",
                processing_time=processing_time,
                image_path="",
                error_message=error_msg
            )
    
    async def _capture_target_image(self, page, config: ImageRecognitionConfig) -> Optional[str]:
        """截取目标图片"""
        try:
            for selector in config.target_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=5000)
                    if element and await element.is_visible():
                        timestamp = int(time.time())
                        image_path = f"recognition_{config.recognition_type.value}_{timestamp}.png"
                        await element.screenshot(path=image_path)
                        logger.info(f"目标图片已截取: {image_path}")
                        return image_path
                except Exception:
                    continue
            
            logger.warning("未找到目标图片元素")
            return None
            
        except Exception as e:
            logger.error(f"截取目标图片失败: {e}")
            return None
    
    async def _execute_recognition_method(self, image_path: str, config: ImageRecognitionConfig) -> ImageRecognitionResult:
        """执行识别方法"""
        method = config.recognition_method
        
        if method == RecognitionMethod.OCR_ONLY:
            return await self._ocr_recognize(image_path, config)
        elif method == RecognitionMethod.AI_ONLY:
            return await self._ai_recognize(image_path, config)
        elif method == RecognitionMethod.OCR_THEN_AI:
            return await self._ocr_then_ai_recognize(image_path, config)
        elif method == RecognitionMethod.AI_THEN_OCR:
            return await self._ai_then_ocr_recognize(image_path, config)
        elif method == RecognitionMethod.PARALLEL:
            return await self._parallel_recognize(image_path, config)
        elif method == RecognitionMethod.MANUAL:
            return await self._manual_recognize(image_path, config)
        else:
            return ImageRecognitionResult(
                success=False,
                recognized_text="",
                confidence=0.0,
                method_used="unknown",
                processing_time=0.0,
                image_path=image_path,
                error_message=f"未知识别方法: {method}"
            )
    
    async def _ocr_recognize(self, image_path: str, config: ImageRecognitionConfig) -> ImageRecognitionResult:
        """OCR识别"""
        try:
            ocr_result = await self.ocr_engine.recognize_text(image_path)
            
            return ImageRecognitionResult(
                success=bool(ocr_result.text and ocr_result.confidence >= config.ocr_confidence_threshold),
                recognized_text=ocr_result.text,
                confidence=ocr_result.confidence,
                method_used="ocr",
                processing_time=ocr_result.processing_time,
                image_path=image_path
            )
            
        except Exception as e:
            return ImageRecognitionResult(
                success=False,
                recognized_text="",
                confidence=0.0,
                method_used="ocr",
                processing_time=0.0,
                image_path=image_path,
                error_message=f"OCR识别失败: {e}"
            )
    
    async def _ai_recognize(self, image_path: str, config: ImageRecognitionConfig) -> ImageRecognitionResult:
        """AI识别"""
        try:
            # 构建AI提示
            prompt = self._build_ai_prompt(config)
            
            # 读取图像并转换为base64
            with open(image_path, 'rb') as f:
                image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # AI识别
            vision_prompt = f"""
{prompt}

[图像数据: data:image/png;base64,{image_base64}]

请直接返回识别结果，不要其他内容。
"""
            
            response = await self.llm_manager.generate(vision_prompt, provider=LLMProvider.GEMINI)
            
            # 解析结果
            recognized_text = self._parse_ai_response(response.content, config)
            confidence = self._calculate_ai_confidence(recognized_text, response.content, config)
            
            return ImageRecognitionResult(
                success=bool(recognized_text and confidence >= config.ai_confidence_threshold),
                recognized_text=recognized_text,
                confidence=confidence,
                method_used="ai",
                processing_time=0.0,
                image_path=image_path
            )
            
        except Exception as e:
            return ImageRecognitionResult(
                success=False,
                recognized_text="",
                confidence=0.0,
                method_used="ai",
                processing_time=0.0,
                image_path=image_path,
                error_message=f"AI识别失败: {e}"
            )
    
    async def _ocr_then_ai_recognize(self, image_path: str, config: ImageRecognitionConfig) -> ImageRecognitionResult:
        """OCR失败后AI识别"""
        # 先尝试OCR
        ocr_result = await self._ocr_recognize(image_path, config)
        
        if ocr_result.success:
            return ocr_result
        
        # OCR失败，尝试AI
        ai_result = await self._ai_recognize(image_path, config)
        ai_result.method_used = "ocr_then_ai"
        
        return ai_result
    
    async def _ai_then_ocr_recognize(self, image_path: str, config: ImageRecognitionConfig) -> ImageRecognitionResult:
        """AI失败后OCR识别"""
        # 先尝试AI
        ai_result = await self._ai_recognize(image_path, config)
        
        if ai_result.success:
            ai_result.method_used = "ai_then_ocr"
            return ai_result
        
        # AI失败，尝试OCR
        ocr_result = await self._ocr_recognize(image_path, config)
        ocr_result.method_used = "ai_then_ocr"
        
        return ocr_result
    
    async def _parallel_recognize(self, image_path: str, config: ImageRecognitionConfig) -> ImageRecognitionResult:
        """并行识别"""
        try:
            # 并行执行OCR和AI识别
            ocr_task = self._ocr_recognize(image_path, config)
            ai_task = self._ai_recognize(image_path, config)
            
            ocr_result, ai_result = await asyncio.gather(ocr_task, ai_task, return_exceptions=True)
            
            # 选择最佳结果
            if isinstance(ocr_result, ImageRecognitionResult) and ocr_result.success:
                if isinstance(ai_result, ImageRecognitionResult) and ai_result.success:
                    # 两个都成功，选择置信度更高的
                    if ai_result.confidence > ocr_result.confidence:
                        ai_result.method_used = "parallel_ai"
                        return ai_result
                    else:
                        ocr_result.method_used = "parallel_ocr"
                        return ocr_result
                else:
                    ocr_result.method_used = "parallel_ocr"
                    return ocr_result
            elif isinstance(ai_result, ImageRecognitionResult) and ai_result.success:
                ai_result.method_used = "parallel_ai"
                return ai_result
            else:
                return ImageRecognitionResult(
                    success=False,
                    recognized_text="",
                    confidence=0.0,
                    method_used="parallel",
                    processing_time=0.0,
                    image_path=image_path,
                    error_message="并行识别都失败"
                )
                
        except Exception as e:
            return ImageRecognitionResult(
                success=False,
                recognized_text="",
                confidence=0.0,
                method_used="parallel",
                processing_time=0.0,
                image_path=image_path,
                error_message=f"并行识别异常: {e}"
            )
    
    async def _manual_recognize(self, image_path: str, config: ImageRecognitionConfig) -> ImageRecognitionResult:
        """手动识别"""
        try:
            print(f"\n📷 图片已保存到: {image_path}")
            print(f"🔍 请查看图片，然后手动输入识别内容")
            
            # 尝试打开图片
            try:
                import os
                import platform
                
                if platform.system() == "Windows":
                    os.startfile(image_path)
                elif platform.system() == "Darwin":  # macOS
                    os.system(f"open {image_path}")
                else:  # Linux
                    os.system(f"xdg-open {image_path}")
                
                print("📖 图片已在默认程序中打开")
            except Exception:
                print("⚠️ 无法自动打开图片，请手动查看图片文件")
            
            # 获取用户输入
            while True:
                user_input = input(f"\n请输入{config.recognition_type.value}内容 (输入'skip'跳过): ").strip()
                
                if user_input.lower() == 'skip':
                    return ImageRecognitionResult(
                        success=False,
                        recognized_text="",
                        confidence=0.0,
                        method_used="manual",
                        processing_time=0.0,
                        image_path=image_path,
                        error_message="用户跳过输入"
                    )
                
                if user_input:
                    return ImageRecognitionResult(
                        success=True,
                        recognized_text=user_input,
                        confidence=1.0,  # 用户输入给予最高置信度
                        method_used="manual",
                        processing_time=0.0,
                        image_path=image_path
                    )
                
                print("❌ 输入不能为空，请重新输入")
                
        except KeyboardInterrupt:
            return ImageRecognitionResult(
                success=False,
                recognized_text="",
                confidence=0.0,
                method_used="manual",
                processing_time=0.0,
                image_path=image_path,
                error_message="用户取消输入"
            )
        except Exception as e:
            return ImageRecognitionResult(
                success=False,
                recognized_text="",
                confidence=0.0,
                method_used="manual",
                processing_time=0.0,
                image_path=image_path,
                error_message=f"手动输入失败: {e}"
            )
    
    def _build_ai_prompt(self, config: ImageRecognitionConfig) -> str:
        """构建AI提示"""
        if config.ai_prompt_template:
            return config.ai_prompt_template
        
        # 根据识别类型生成默认提示
        type_prompts = {
            ImageRecognitionType.CAPTCHA: "请识别这张验证码图片中的文字内容。只返回识别出的字符，不要其他解释。",
            ImageRecognitionType.QR_CODE: "请识别这张二维码图片中的内容。直接返回二维码包含的文本信息。",
            ImageRecognitionType.BARCODE: "请识别这张条形码图片中的数字。只返回条形码的数字内容。",
            ImageRecognitionType.TEXT_OCR: "请识别这张图片中的所有文字内容。按原文返回识别的文本。",
            ImageRecognitionType.DOCUMENT: "请识别这张文档图片中的文字内容。保持原有格式返回文本。",
            ImageRecognitionType.SCREENSHOT: "请分析这张截图并描述其中的主要内容。",
            ImageRecognitionType.CUSTOM: "请识别这张图片中的内容。"
        }
        
        return type_prompts.get(config.recognition_type, "请识别这张图片中的内容。")
    
    def _parse_ai_response(self, response_content: str, config: ImageRecognitionConfig) -> str:
        """解析AI响应"""
        if not response_content:
            return ""
        
        text = response_content.strip()
        
        # 根据识别类型进行特定解析
        if config.recognition_type == ImageRecognitionType.CAPTCHA:
            # 验证码解析
            import re
            matches = re.findall(r'[A-Za-z0-9]+', text)
            if matches:
                valid_matches = [m for m in matches if 3 <= len(m) <= 8]
                return valid_matches[0] if valid_matches else matches[0]
        
        return text
    
    def _calculate_ai_confidence(self, recognized_text: str, response_content: str, config: ImageRecognitionConfig) -> float:
        """计算AI识别置信度"""
        if not recognized_text:
            return 0.0
        
        base_confidence = 0.7
        
        # 根据识别类型调整置信度
        if config.recognition_type == ImageRecognitionType.CAPTCHA:
            if 3 <= len(recognized_text) <= 6 and recognized_text.isalnum():
                base_confidence += 0.1
        
        # 根据响应确定性调整
        if "确定" in response_content or "清楚" in response_content:
            base_confidence += 0.05
        elif "可能" in response_content or "不确定" in response_content:
            base_confidence -= 0.1
        
        return min(base_confidence, 1.0)
    
    def _validate_recognition_result(self, result: ImageRecognitionResult, validation_rules: Dict[str, Any]) -> ImageRecognitionResult:
        """验证识别结果"""
        try:
            text = result.recognized_text
            
            # 长度验证
            if "min_length" in validation_rules:
                if len(text) < validation_rules["min_length"]:
                    result.success = False
                    result.error_message = f"文本长度不足，最少需要{validation_rules['min_length']}位"
                    return result
            
            if "max_length" in validation_rules:
                if len(text) > validation_rules["max_length"]:
                    result.success = False
                    result.error_message = f"文本长度超限，最多允许{validation_rules['max_length']}位"
                    return result
            
            # 字符类型验证
            if validation_rules.get("alphanumeric_only", False):
                if not text.isalnum():
                    result.success = False
                    result.error_message = "文本必须是字母数字组合"
                    return result
            
            # 正则表达式验证
            if "pattern" in validation_rules:
                import re
                if not re.match(validation_rules["pattern"], text):
                    result.success = False
                    result.error_message = f"文本格式不符合要求: {validation_rules['pattern']}"
                    return result
            
            return result
            
        except Exception as e:
            result.success = False
            result.error_message = f"验证识别结果失败: {e}"
            return result
    
    async def _fill_recognition_result(self, page, text: str, input_selectors: List[str]) -> bool:
        """填写识别结果"""
        try:
            for selector in input_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=5000)
                    if element and await element.is_visible():
                        await element.fill("")  # 先清空
                        await element.fill(text)
                        
                        # 验证填写结果
                        filled_value = await element.input_value()
                        if filled_value == text:
                            logger.info(f"识别结果填写成功: {text}")
                            return True
                except Exception:
                    continue
            
            logger.warning("未找到可用的输入框")
            return False
            
        except Exception as e:
            logger.error(f"填写识别结果失败: {e}")
            return False


# 全局实例
global_universal_recognizer = UniversalImageRecognizer()


def get_universal_image_recognizer() -> UniversalImageRecognizer:
    """获取通用图片识别器实例"""
    return global_universal_recognizer


# 便捷函数
async def recognize_image_universal(page, config: ImageRecognitionConfig) -> ImageRecognitionResult:
    """便捷的通用图片识别函数"""
    recognizer = get_universal_image_recognizer()
    return await recognizer.recognize_image(page, config)
