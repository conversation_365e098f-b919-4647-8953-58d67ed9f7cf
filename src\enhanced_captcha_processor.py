"""
增强验证码处理器

实现完整的验证码识别流程：OCR识别 → AI识别 → 用户处理
"""
import asyncio
import logging
import time
import base64
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

from ai_llm_manager import get_llm_manager, LLMProvider
from opencv_ocr_processor import OpenCVOCREngine, OCRResult
from ai_captcha_handler import AICaptchaDetector, CaptchaImage, CaptchaInput

logger = logging.getLogger(__name__)


@dataclass
class CaptchaRecognitionResult:
    """验证码识别结果"""
    success: bool
    recognized_text: str
    confidence: float
    method: str  # ocr, ai, manual
    processing_time: float
    image_path: str
    error_message: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "recognized_text": self.recognized_text,
            "confidence": self.confidence,
            "method": self.method,
            "processing_time": self.processing_time,
            "image_path": self.image_path,
            "error_message": self.error_message
        }


class AIVisionCaptchaRecognizer:
    """AI视觉验证码识别器"""
    
    def __init__(self, llm_provider: str = "gemini"):
        self.llm_manager = get_llm_manager()
        if llm_provider.lower() == "gemini":
            self.llm_provider = LLMProvider.GEMINI
        elif llm_provider.lower() == "openai":
            self.llm_provider = LLMProvider.OPENAI
        elif llm_provider.lower() == "qwen":
            self.llm_provider = LLMProvider.QWEN
        else:
            self.llm_provider = LLMProvider.GEMINI
    
    async def recognize_captcha_with_ai(self, image_path: str) -> Tuple[str, float]:
        """使用AI视觉识别验证码"""
        try:
            logger.info(f"开始AI视觉识别验证码: {image_path}")
            
            # 读取图像并转换为base64
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 构建AI识别提示
            prompt = f"""
请识别这张验证码图片中的文字内容。

图片是一张验证码图片，请仔细观察并识别其中的字符。

要求：
1. 只返回识别出的字符，不要其他解释
2. 验证码通常是3-8位的字母数字组合
3. 如果不确定某个字符，请给出最可能的选择
4. 返回格式：直接返回识别的字符，如：ABC123

请识别验证码：
"""
            
            # 如果是Gemini，支持图像输入
            if self.llm_provider == LLMProvider.GEMINI:
                response = await self._gemini_vision_recognize(prompt, image_base64)
            else:
                # 其他模型使用文本描述
                response = await self._text_based_recognize(prompt, image_path)
            
            # 解析识别结果
            recognized_text = self._parse_ai_response(response.content)
            
            # 计算置信度（基于响应质量）
            confidence = self._calculate_ai_confidence(recognized_text, response.content)
            
            logger.info(f"AI识别结果: {recognized_text} (置信度: {confidence:.2f})")
            
            return recognized_text, confidence
            
        except Exception as e:
            logger.error(f"AI视觉识别失败: {e}")
            return "", 0.0
    
    async def _gemini_vision_recognize(self, prompt: str, image_base64: str) -> Any:
        """使用Gemini视觉模型识别"""
        try:
            # 构建包含图像的消息
            vision_prompt = f"""
{prompt}

[图像数据: data:image/png;base64,{image_base64}]

请直接返回识别的验证码字符，不要其他内容。
"""
            
            response = await self.llm_manager.generate(vision_prompt, provider=self.llm_provider)
            return response
            
        except Exception as e:
            logger.error(f"Gemini视觉识别失败: {e}")
            raise
    
    async def _text_based_recognize(self, prompt: str, image_path: str) -> Any:
        """基于文本描述的识别（备用方案）"""
        try:
            # 分析图像特征
            import cv2
            import numpy as np
            
            image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
            if image is None:
                raise Exception("无法读取图像")
            
            # 提取图像特征描述
            height, width = image.shape
            mean_intensity = np.mean(image)
            std_intensity = np.std(image)
            
            # 检测边缘
            edges = cv2.Canny(image, 50, 150)
            edge_count = np.sum(edges > 0)
            
            # 构建描述性提示
            description_prompt = f"""
{prompt}

图像特征描述：
- 尺寸: {width}x{height}像素
- 平均亮度: {mean_intensity:.1f}
- 亮度标准差: {std_intensity:.1f}
- 边缘像素数: {edge_count}

基于这些特征，这可能是一个包含字母数字字符的验证码图像。
请根据常见验证码模式给出最可能的字符组合（3-6位字母数字）：
"""
            
            response = await self.llm_manager.generate(description_prompt, provider=self.llm_provider)
            return response
            
        except Exception as e:
            logger.error(f"文本描述识别失败: {e}")
            raise
    
    def _parse_ai_response(self, response_content: str) -> str:
        """解析AI响应内容"""
        if not response_content:
            return ""
        
        # 清理响应内容
        text = response_content.strip()
        
        # 移除常见的前缀和后缀
        prefixes = ["验证码是:", "识别结果:", "答案:", "结果:", "验证码:", "captcha:", "code:"]
        for prefix in prefixes:
            if text.lower().startswith(prefix.lower()):
                text = text[len(prefix):].strip()
        
        # 提取字母数字字符
        import re
        matches = re.findall(r'[A-Za-z0-9]+', text)
        
        if matches:
            # 选择最可能的验证码（长度在3-8之间）
            valid_matches = [m for m in matches if 3 <= len(m) <= 8]
            if valid_matches:
                return valid_matches[0]
            else:
                return matches[0] if matches else ""
        
        return ""
    
    def _calculate_ai_confidence(self, recognized_text: str, response_content: str) -> float:
        """计算AI识别置信度"""
        if not recognized_text:
            return 0.0
        
        # 基础置信度
        base_confidence = 0.7
        
        # 长度合理性
        if 3 <= len(recognized_text) <= 6:
            base_confidence += 0.1
        
        # 字符类型合理性
        if recognized_text.isalnum():
            base_confidence += 0.1
        
        # 响应确定性（简单启发式）
        if "确定" in response_content or "清楚" in response_content:
            base_confidence += 0.05
        elif "可能" in response_content or "不确定" in response_content:
            base_confidence -= 0.1
        
        return min(base_confidence, 1.0)


class EnhancedCaptchaProcessor:
    """增强验证码处理器"""
    
    def __init__(self):
        self.detector = AICaptchaDetector()
        self.ocr_engine = OpenCVOCREngine()
        self.ai_recognizer = AIVisionCaptchaRecognizer()
        self.min_ocr_confidence = 0.7  # OCR最低置信度阈值
        self.min_ai_confidence = 0.6   # AI最低置信度阈值
    
    async def process_captcha_complete(self, page, interactive: bool = True) -> CaptchaRecognitionResult:
        """完整的验证码处理流程"""
        start_time = time.time()
        
        try:
            logger.info("开始完整验证码处理流程")
            
            # 步骤1: AI检测验证码元素
            print("🔍 步骤1: AI检测验证码元素...")
            analysis = await self.detector.detect_captcha_elements(page)
            
            if not analysis.has_captcha:
                return CaptchaRecognitionResult(
                    success=False,
                    recognized_text="",
                    confidence=0.0,
                    method="detection",
                    processing_time=time.time() - start_time,
                    image_path="",
                    error_message="未检测到验证码"
                )
            
            print(f"   ✅ 检测到验证码: 图片{len(analysis.captcha_images)}个, 输入框{len(analysis.captcha_inputs)}个")
            
            # 步骤2: 获取验证码图片
            print("📷 步骤2: 获取验证码图片...")
            if not analysis.captcha_images:
                return CaptchaRecognitionResult(
                    success=False,
                    recognized_text="",
                    confidence=0.0,
                    method="capture",
                    processing_time=time.time() - start_time,
                    image_path="",
                    error_message="未找到验证码图片"
                )
            
            captcha_image = analysis.captcha_images[0]
            image_path = await self._capture_captcha_image(page, captcha_image)
            print(f"   ✅ 验证码图片已保存: {image_path}")
            
            # 步骤3: OCR识别验证码
            print("🔤 步骤3: OCR识别验证码...")
            ocr_result = await self.ocr_engine.recognize_text(image_path)
            print(f"   📊 OCR结果: '{ocr_result.text}' (置信度: {ocr_result.confidence:.2f})")
            
            # 步骤4: 判断OCR结果是否可靠
            if ocr_result.text and ocr_result.confidence >= self.min_ocr_confidence:
                print("   ✅ OCR识别成功，置信度足够")
                recognized_text = ocr_result.text
                confidence = ocr_result.confidence
                method = "ocr"
            else:
                print("   ⚠️ OCR识别失败或置信度不足，尝试AI识别...")
                
                # 步骤5: AI识别验证码
                print("🤖 步骤5: AI视觉识别验证码...")
                ai_text, ai_confidence = await self.ai_recognizer.recognize_captcha_with_ai(image_path)
                print(f"   📊 AI结果: '{ai_text}' (置信度: {ai_confidence:.2f})")
                
                if ai_text and ai_confidence >= self.min_ai_confidence:
                    print("   ✅ AI识别成功")
                    recognized_text = ai_text
                    confidence = ai_confidence
                    method = "ai"
                else:
                    print("   ❌ AI识别也失败")
                    
                    # 步骤6: 用户手动处理
                    if interactive:
                        print("👤 步骤6: 请用户手动输入验证码...")
                        recognized_text, confidence = await self._manual_captcha_input(image_path)
                        method = "manual"
                    else:
                        return CaptchaRecognitionResult(
                            success=False,
                            recognized_text="",
                            confidence=0.0,
                            method="failed",
                            processing_time=time.time() - start_time,
                            image_path=image_path,
                            error_message="OCR和AI识别都失败，需要手动处理"
                        )
            
            # 步骤7: 填写验证码
            if recognized_text:
                print(f"✍️ 步骤7: 填写验证码 '{recognized_text}'...")
                success = await self._fill_captcha(page, analysis.captcha_inputs[0], recognized_text)
                
                if success:
                    print("   ✅ 验证码填写成功")
                    
                    processing_time = time.time() - start_time
                    return CaptchaRecognitionResult(
                        success=True,
                        recognized_text=recognized_text,
                        confidence=confidence,
                        method=method,
                        processing_time=processing_time,
                        image_path=image_path
                    )
                else:
                    return CaptchaRecognitionResult(
                        success=False,
                        recognized_text=recognized_text,
                        confidence=confidence,
                        method=method,
                        processing_time=time.time() - start_time,
                        image_path=image_path,
                        error_message="验证码填写失败"
                    )
            else:
                return CaptchaRecognitionResult(
                    success=False,
                    recognized_text="",
                    confidence=0.0,
                    method="failed",
                    processing_time=time.time() - start_time,
                    image_path=image_path,
                    error_message="验证码识别失败"
                )
                
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"验证码处理异常: {e}"
            logger.error(error_msg)
            
            return CaptchaRecognitionResult(
                success=False,
                recognized_text="",
                confidence=0.0,
                method="error",
                processing_time=processing_time,
                image_path="",
                error_message=error_msg
            )
    
    async def _capture_captcha_image(self, page, captcha_image: CaptchaImage) -> str:
        """截取验证码图片"""
        try:
            # 查找验证码图片元素
            img_element = await page.wait_for_selector(captcha_image.selector, timeout=10000)
            if not img_element:
                raise Exception(f"未找到验证码图片: {captcha_image.selector}")
            
            # 截取验证码图片
            timestamp = int(time.time())
            image_path = f"captcha_{timestamp}.png"
            await img_element.screenshot(path=image_path)
            
            # 更新图片路径
            captcha_image.image_path = image_path
            
            return image_path
            
        except Exception as e:
            logger.error(f"截取验证码图片失败: {e}")
            raise
    
    async def _manual_captcha_input(self, image_path: str) -> Tuple[str, float]:
        """用户手动输入验证码"""
        try:
            print(f"\n📷 验证码图片已保存到: {image_path}")
            print("🔍 请查看验证码图片，然后手动输入验证码内容")
            
            # 尝试打开图片（如果可能）
            try:
                import os
                import platform
                
                if platform.system() == "Windows":
                    os.startfile(image_path)
                elif platform.system() == "Darwin":  # macOS
                    os.system(f"open {image_path}")
                else:  # Linux
                    os.system(f"xdg-open {image_path}")
                
                print("📖 验证码图片已在默认程序中打开")
            except Exception:
                print("⚠️ 无法自动打开图片，请手动查看图片文件")
            
            # 获取用户输入
            while True:
                user_input = input("\n请输入验证码内容 (输入'skip'跳过): ").strip()
                
                if user_input.lower() == 'skip':
                    return "", 0.0
                
                if user_input and len(user_input) >= 3:
                    # 清理用户输入
                    clean_input = ''.join(c for c in user_input if c.isalnum())
                    if clean_input:
                        print(f"✅ 用户输入验证码: {clean_input}")
                        return clean_input, 1.0  # 用户输入给予最高置信度
                
                print("❌ 输入无效，请输入至少3位的字母数字组合")
                
        except KeyboardInterrupt:
            print("\n❌ 用户取消输入")
            return "", 0.0
        except Exception as e:
            logger.error(f"用户手动输入失败: {e}")
            return "", 0.0
    
    async def _fill_captcha(self, page, captcha_input: CaptchaInput, text: str) -> bool:
        """填写验证码"""
        try:
            # 查找验证码输入框
            input_element = await page.wait_for_selector(captcha_input.selector, timeout=10000)
            if not input_element:
                raise Exception(f"未找到验证码输入框: {captcha_input.selector}")
            
            # 清空并填写验证码
            await input_element.fill("")  # 先清空
            await input_element.fill(text)
            
            # 验证填写结果
            filled_value = await input_element.input_value()
            if filled_value == text:
                logger.info(f"验证码填写成功: {text}")
                return True
            else:
                logger.warning(f"验证码填写验证失败: 期望{text}, 实际{filled_value}")
                return False
                
        except Exception as e:
            logger.error(f"填写验证码失败: {e}")
            return False
    
    async def refresh_captcha(self, page, analysis) -> bool:
        """刷新验证码"""
        try:
            if not analysis.refresh_buttons:
                logger.warning("未找到验证码刷新按钮")
                return False
            
            # 点击第一个刷新按钮
            refresh_selector = analysis.refresh_buttons[0]
            refresh_element = await page.query_selector(refresh_selector)
            
            if refresh_element and await refresh_element.is_visible():
                await refresh_element.click()
                # 等待验证码刷新
                await page.wait_for_timeout(2000)
                logger.info("验证码刷新成功")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"刷新验证码失败: {e}")
            return False


# 全局实例
global_enhanced_captcha_processor = EnhancedCaptchaProcessor()


def get_enhanced_captcha_processor() -> EnhancedCaptchaProcessor:
    """获取增强验证码处理器实例"""
    return global_enhanced_captcha_processor


# 便捷函数
async def process_captcha_complete(page, interactive: bool = True) -> CaptchaRecognitionResult:
    """便捷的完整验证码处理函数"""
    processor = get_enhanced_captcha_processor()
    return await processor.process_captcha_complete(page, interactive)
