import React, { useState, useCallback, useRef, useEffect } from 'react';
import ReactFlow, {
  addEdge,
  Background,
  Controls,
  MiniMap,
  ReactFlowProvider,
  useNodesState,
  useEdgesState,
  useReactFlow,
  Position,
  MarkerType
} from 'react-flow-renderer';
import 'react-flow-renderer/dist/style.css';
import ExecutionMonitor from './components/ExecutionMonitor';
import { ExecutionStatus } from './constants';
import useWorkflowExecution from './hooks/useWorkflowExecution';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  Button,
  Container,
  Paper,
  Drawer,
  IconButton,
  Tooltip,
  useMediaQuery,
  useTheme,
  CssBaseline,
  Divider,
  Tabs,
  Tab
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SettingsIcon from '@mui/icons-material/Settings';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import StopIcon from '@mui/icons-material/Stop';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import ChatIcon from '@mui/icons-material/Chat';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import ListIcon from '@mui/icons-material/List';
import NodePropertiesPanel from './components/NodePropertiesPanel';
import WorkflowToolbar from './components/WorkflowToolbar';
import AIRPAChatPanel from './components/AIRPAChatPanel';
import WorkflowListPanel from './components/WorkflowListPanel';
import { v4 as uuidv4 } from 'uuid';
import { exportWorkflow, importWorkflow } from './utils/workflowSerializer';
import { useWorkflowExecution, ExecutionStatus } from './hooks/useWorkflowExecution';

// 自定义节点组件
const CustomNode = ({ data, id, type, selected }) => {
  // 根据节点类型设置不同的样式
  const getNodeStyle = (nodeType) => {
    const baseStyle = {
      padding: '12px 16px',
      minWidth: 180,
      borderRadius: 4,
      border: '2px solid',
      backgroundColor: '#fff',
      transition: 'all 0.2s ease',
      '&:hover': {
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
      }
    };

    switch (nodeType) {
      case 'start':
        return {
          ...baseStyle,
          borderColor: '#10b981',
          backgroundColor: '#ecfdf5',
        };
      case 'end':
        return {
          ...baseStyle,
          borderColor: '#ef4444',
          backgroundColor: '#fef2f2',
        };
      case 'condition':
        return {
          ...baseStyle,
          borderColor: '#3b82f6',
          backgroundColor: '#eff6ff',
          borderRadius: '50%',
          width: 120,
          height: 120,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transform: 'rotate(45deg)'
        };
      case 'loop':
        return {
          ...baseStyle,
          borderColor: '#f59e0b',
          backgroundColor: '#fffbeb',
          borderStyle: 'dashed',
        };
      default:
        return {
          ...baseStyle,
          borderColor: '#94a3b8',
        };
    }
  };


  // 条件节点的特殊渲染
  if (type === 'condition') {
    return (
      <Paper 
        elevation={selected ? 6 : 3}
        sx={{
          ...getNodeStyle(type),
          transform: 'rotate(45deg)',
          '& > div': {
            transform: 'rotate(-45deg)',
            width: '100%',
            textAlign: 'center'
          }
        }}
      >
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{data.label}</div>
          {data.description && (
            <div style={{ fontSize: 11, color: '#666' }}>{data.description}</div>
          )}
        </div>
      </Paper>
    );
  }


  // 开始/结束节点的特殊渲染
  if (type === 'start' || type === 'end') {
    return (
      <Paper 
        elevation={selected ? 6 : 3}
        sx={{
          ...getNodeStyle(type),
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: 80,
          height: 80,
          borderRadius: '50%',
          '& > div': {
            textAlign: 'center'
          }
        }}
      >
        <div>
          <div style={{ fontWeight: 'bold' }}>{data.label}</div>
        </div>
      </Paper>
    );
  }

  // 默认节点渲染
  return (
    <Paper 
      elevation={selected ? 6 : 3}
      sx={{
        ...getNodeStyle(type),
        borderLeft: `4px solid ${
          type === 'condition' ? '#3b82f6' : 
          type === 'loop' ? '#f59e0b' : 
          type === 'start' ? '#10b981' : 
          type === 'end' ? '#ef4444' : 
          '#94a3b8'
        }`,
      }}
    >
      <div style={{ textAlign: 'left' }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center',
          marginBottom: 8,
          fontWeight: 'bold',
          color: type === 'condition' ? '#2563eb' : 
                type === 'loop' ? '#d97706' : '#1e293b'
        }}>
          {type === 'condition' && '⏳'}
          {type === 'loop' && '🔄'}
          <span style={{ marginLeft: 4 }}>{data.label}</span>
        </div>
        {data.description && (
          <div style={{ 
            fontSize: 12, 
            color: '#64748b',
            backgroundColor: '#f8fafc',
            padding: '4px 8px',
            borderRadius: 4,
            marginTop: 8
          }}>
            {data.description}
          </div>
        )}
      </div>
    </Paper>
  );
};

const nodeTypes = {
  default: CustomNode,
  input: CustomNode,
  output: CustomNode,
};

// 默认边样式
const defaultEdgeOptions = {
  type: 'smoothstep',
  style: { stroke: '#b1b1b7', strokeWidth: 2 },
  markerEnd: {
    type: MarkerType.ArrowClosed,
    color: '#b1b1b7',
  },
  animated: false,
};

import 'reactflow/dist/style.css';
import './App.css';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

const initialNodes = [
  {
    id: 'start',
    type: 'start',
    data: { 
      label: '开始',
      description: '工作流开始节点'
    },
    position: { x: 250, y: 100 },
    sourcePosition: 'right',
    style: { 
      border: '2px solid #10b981',
      borderRadius: '50%',
      width: 60,
      height: 60,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  },
  {
    id: 'end',
    type: 'end',
    data: { 
      label: '结束',
      description: '工作流结束节点'
    },
    position: { x: 250, y: 400 },
    targetPosition: 'left',
    style: { 
      border: '2px solid #ef4444',
      borderRadius: '50%',
      width: 60,
      height: 60,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  }
];

const initialEdges = [
  // 初始时没有连接
];

function App() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState(null);
  const [workflowName, setWorkflowName] = useState('未命名工作流');
  const [isModified, setIsModified] = useState(false);
  const reactFlowWrapper = useRef(null);
  const [reactFlowInstance, setReactFlowInstance] = useState(null);
  const [isMobile, setIsMobile] = useState(false);
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  
  // 执行监控相关状态
  const [monitorOpen, setMonitorOpen] = useState(false);
  const toggleMonitor = () => setMonitorOpen(!monitorOpen);
  
  // 使用自定义 hook 管理工作流执行
  const {
    status: executionStatus,
    logs: executionLogs,
    stats: executionStats,
    start: startExecution,
    pause: pauseExecution,
    resume: resumeExecution,
    stop: stopExecution,
    clearLogs: clearExecutionLogs,
    updateNodeStatus
  } = useWorkflowExecution({
    nodes,
    edges,
    onNodeStatusChange: (nodeId, status) => {
      setNodes(nds => nds.map(node => {
        if (node.id === nodeId) {
          return { ...node, data: { ...node.data, status } };
        }
        return node;
      }));
    }
  });
  
  // 处理开始执行
  const handleStartExecution = useCallback(() => {
    startExecution();
    setMonitorOpen(true);
  }, [startExecution]);
  
  const prevNodesRef = useRef(nodes);
  const prevEdgesRef = useRef(edges);

  // 检测工作流变化
  useEffect(() => {
    const nodesChanged = JSON.stringify(nodes) !== JSON.stringify(prevNodesRef.current);
    const edgesChanged = JSON.stringify(edges) !== JSON.stringify(prevEdgesRef.current);
    
    if (nodesChanged || edgesChanged) {
      setIsModified(true);
    }
    
    prevNodesRef.current = nodes;
    prevEdgesRef.current = edges;
  }, [nodes, edges]);

  // 处理节点连接
  const onConnect = useCallback(
    (params) => {
      // 检查是否已存在相同的连接
      const isConnectionExists = edges.some(
        (edge) =>
          edge.source === params.source &&
          edge.target === params.target
      );

      if (!isConnectionExists) {
        setEdges((eds) =>
          addEdge(
            {
              ...params,
              id: `e${params.source}-${params.target}-${Math.random().toString(36).substr(2, 9)}`,
              animated: true,
              style: { stroke: '#555' },
            },
            eds
          )
        );
        setIsModified(true);
      }
    },
    [edges]
  );

  // 添加新节点
  const onAddNode = (type = 'default', label = '新节点') => {
    const position = reactFlowInstance.screenToFlowPosition({
      x: window.innerWidth / 2,
      y: window.innerHeight / 2,
    });

    const newNode = {
      id: `node-${Date.now()}`,
      type,
      data: { 
        label,
        description: type === 'condition' ? '条件分支' : ''
      },
      position,
      sourcePosition: Position.Right,
      targetPosition: Position.Left,
    };

    setNodes((nds) => [...nds, newNode]);
  };

  // 处理节点选择
  const onNodeClick = useCallback((event, node) => {
    setSelectedNode(node);
    if (isMobile) {
      setPropertiesPanelOpen(true);
    }
  }, [isMobile]);

  // 处理节点更新
  const onUpdateNode = useCallback((nodeId, newNode) => {
    setNodes((nds) =>
      nds.map((node) => (node.id === nodeId ? newNode : node))
    );
    setIsModified(true);
  }, []);

  // 处理节点删除
  const onDeleteNode = useCallback((nodeId) => {
    // 删除节点
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    // 删除相关连接
    setEdges((eds) =>
      eds.filter(
        (edge) => edge.source !== nodeId && edge.target !== nodeId
      )
    );
    setSelectedNode(null);
    setIsModified(true);
  }, []);

  // 关闭属性面板
  const handleClosePropertiesPanel = useCallback(() => {
    setPropertiesPanelOpen(false);
    setSelectedNode(null);
  }, []);

  // 处理画布点击（取消选择节点）
  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
    if (isMobile) {
      setPropertiesPanelOpen(false);
    }
  }, [isMobile]);

  // 处理拖放
  const onDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // 创建新工作流
  const handleNewWorkflow = useCallback(() => {
    if (isModified && !window.confirm('您有未保存的更改，确定要创建新工作流吗？')) {
      return;
    }
    
    // 创建默认的开始和结束节点
    setSelectedNode(null);
    setWorkflowName(name || '未命名工作流');
    setIsModified(false);
  }, [isModified, executionStatus, setNodes, setEdges]);

  // 打开工作流
  const handleOpenWorkflow = useCallback((loadedNodes, loadedEdges, workflowName) => {
    if (executionStatus === ExecutionStatus.RUNNING) {
      alert('请先停止工作流执行');
      return;
    }
    setNodes(loadedNodes || []);
    setEdges(loadedEdges || []);
    setSelectedNode(null);
    setWorkflowName(workflowName || '未命名工作流');
    setIsModified(false);
  }, [executionStatus, setNodes, setEdges]);

  // 保存工作流
  const handleSaveWorkflow = useCallback(() => {
    if (executionStatus === ExecutionStatus.RUNNING) {
      alert('请先停止工作流执行');
      return;
    }
    
    // 在实际应用中，这里可以调用API保存到服务器
    const workflowData = {
      name: workflowName,
      nodes: nodes.map(node => ({
        id: node.id,
        type: node.type,
        position: node.position,
        data: node.data,
      })),
      edges: edges.map(edge => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
      })),
    };
    
    console.log('保存工作流:', workflowData);
    setIsModified(false);
    alert('工作流保存成功！');
  }, [executionStatus, workflowName, nodes, edges]);

  // 处理键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event) => {
      // 保存快捷键: Ctrl+S
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        handleSaveWorkflow();
      }
      // 新建工作流: Ctrl+N
      else if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
        event.preventDefault();
        handleNewWorkflow();
      }
      // 打开工作流: Ctrl+O
      else if ((event.ctrlKey || event.metaKey) && event.key === 'o') {
        event.preventDefault();
        document.getElementById('workflow-file-input')?.click();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleSaveWorkflow, handleNewWorkflow]);

  // 处理放置节点
  const onDrop = useCallback(
    (event) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');
      const label = event.dataTransfer.getData('label') || '新节点';

      // 检查是否在画布内
      const position = reactFlowInstance.screenToFlowPosition({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode = {
        id: `node-${Date.now()}`,
        type,
        position,
        data: { 
          label,
          description: type === 'condition' ? '条件分支' : ''
        },
        sourcePosition: Position.Right,
        targetPosition: Position.Left,
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance]
  );

  // 开始拖动节点
  const onDragStart = (event, nodeType, nodeLabel) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.setData('label', nodeLabel);
    event.dataTransfer.effectAllowed = 'move';
  };

  return (
    <ThemeProvider theme={createTheme()}>
      <CssBaseline />
      
      {/* 工作流工具栏 */}
      <WorkflowToolbar 
        nodes={nodes}
        edges={edges}
        workflowName={workflowName}
        isModified={isModified}
        onNewWorkflow={handleNewWorkflow}
        onOpenWorkflow={handleOpenWorkflow}
        onSaveWorkflow={handleSaveWorkflow}
        onSaveAsWorkflow={(newName) => {
          setWorkflowName(newName);
          setIsModified(false);
        }}
      />
      
      {/* 主内容区域 */}
      <Box sx={{ display: 'flex', flex: 1, overflow: 'hidden' }}>
        {/* 左侧节点面板 */}
        <Box 
          sx={{ 
            width: 200, 
            p: 2, 
            borderRight: '1px solid #e0e0e0',
            overflowY: 'auto',
            display: { xs: 'none', sm: 'block' }
          }}
        >
          <Typography variant="subtitle2" gutterBottom>节点面板</Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {Object.keys(nodeTypes).map((node) => (
              <Button
                key={node}
                variant="outlined"
                startIcon={node === 'start' ? '🚀' : node === 'end' ? '🏁' : node === 'condition' ? '⚖️' : node === 'loop' ? '🔄' : '📋'}
                onDragStart={(event) => onDragStart(event, node)}
                draggable
                sx={{ 
                  justifyContent: 'flex-start',
                  textTransform: 'none',
                  textAlign: 'left',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}
              >
                {node === 'start' ? '开始' : node === 'end' ? '结束' : node === 'condition' ? '条件分支' : node === 'loop' ? '循环节点' : '操作节点'}
              </Button>
            ))}
          </Box>
        </Box>
        
        {/* 主画布 */}
        <Box sx={{ flex: 1, position: 'relative' }} ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onInit={setReactFlowInstance}
            onDrop={onDrop}
            onDragOver={onDragOver}
            onNodeClick={onNodeClick}
            onPaneClick={onPaneClick}
            nodeTypes={nodeTypes}
            fitView
            attributionPosition="bottom-left"
            snapToGrid
            snapGrid={[15, 15]}
          >
            <Background />
            <Controls />
            <MiniMap />
            
            {/* 执行控制按钮 */}
            <Box sx={{ position: 'absolute', bottom: 16, right: 16, display: 'flex', gap: 1, zIndex: 10 }}>
              <Tooltip title={monitorOpen ? '隐藏执行监控' : '显示执行监控'}>
                <Button 
                  variant="contained" 
                  color={monitorOpen ? 'primary' : 'inherit'}
                  onClick={toggleMonitor}
                  startIcon={monitorOpen ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  size="small"
                >
                  监控
                </Button>
              </Tooltip>
              
              {executionStatus === ExecutionStatus.RUNNING || executionStatus === ExecutionStatus.PAUSED ? (
                <>
                  {executionStatus === ExecutionStatus.RUNNING ? (
                    <Tooltip title="暂停">
                      <Button 
                        variant="contained" 
                        color="warning"
                        onClick={pauseExecution}
                        startIcon={<PauseIcon />}
                        size="small"
                      >
                        暂停
                      </Button>
                    </Tooltip>
                  ) : (
                    <Tooltip title="继续">
                      <Button 
                        variant="contained" 
                        color="success"
                        onClick={resumeExecution}
                        startIcon={<PlayArrowIcon />}
                        size="small"
                      >
                        继续
                      </Button>
                    </Tooltip>
                  )}
                  <Tooltip title="停止">
                    <Button 
                      variant="contained" 
                      color="error"
                      onClick={stopExecution}
                      startIcon={<StopIcon />}
                      size="small"
                    >
                      停止
                    </Button>
                  </Tooltip>
                </>
              ) : (
                <Tooltip title="开始执行">
                  <Button 
                    variant="contained" 
                    color="primary"
                    onClick={handleStartExecution}
                    startIcon={<PlayArrowIcon />}
                    size="small"
                  >
                    执行
                  </Button>
                </Tooltip>
              )}
            </Box>
            
            {/* 执行监控面板 */}
            <Drawer
              anchor="bottom"
              open={monitorOpen}
              onClose={toggleMonitor}
              sx={{
                '& .MuiDrawer-paper': {
                  height: '40%',
                  borderTopLeftRadius: 12,
                  borderTopRightRadius: 12,
                  overflow: 'hidden',
                },
              }}
              ModalProps={{
                keepMounted: true,
              }}
            >
              <ExecutionMonitor
                status={executionStatus}
                logs={executionLogs}
                stats={executionStats}
                onStart={handleStartExecution}
                onPause={pauseExecution}
                onResume={resumeExecution}
                onStop={stopExecution}
                onClear={clearExecutionLogs}
              />
            </Drawer>
          </ReactFlow>
          {/* 画布工具栏 */}
          <Box sx={{ 
            position: 'absolute', 
            bottom: 16, 
            right: 16, 
            display: 'flex', 
            gap: 1,
            zIndex: 10
          }}>
            <Tooltip title="适应视图">
              <Button 
                variant="contained" 
                size="small"
                onClick={() => reactFlowInstance?.fitView({ padding: 0.2 })}
                sx={{ minWidth: 'auto' }}
              >
                🔍
              </Button>
            </Tooltip>
            
            <Tooltip title="清空画布">
              <Button 
                variant="contained" 
                color="error"
                size="small"
                onClick={() => {
                  if (window.confirm('确定要清空画布吗？这将删除所有节点和连接。')) {
                    setNodes([]);
                    setEdges([]);
                    setSelectedNode(null);
                    setIsModified(true);
                  }
                }}
                sx={{ minWidth: 'auto' }}
              >
                🗑️
              </Button>
            </Tooltip>
            
            <Tooltip title={propertiesPanelOpen ? '隐藏属性面板' : '显示属性面板'}>
              <Button 
                variant="contained" 
                color={propertiesPanelOpen ? 'primary' : 'inherit'}
                size="small"
                onClick={() => setPropertiesPanelOpen(!propertiesPanelOpen)}
                sx={{ 
                  minWidth: 'auto',
                  display: { xs: 'none', sm: 'flex' }
                }}
              >
                ⚙️
              </Button>
            </Tooltip>
          </Box>
        <Box sx={{ 
          p: 1, 
          bgcolor: 'background.default', 
          borderTop: '1px solid', 
          borderColor: 'divider',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          fontSize: '0.75rem',
          color: 'text.secondary'
        }}>
          <Box>
            节点: {nodes.length} | 连接: {edges.length} | {selectedNode ? `已选择: ${selectedNode.data?.label || selectedNode.id}` : '未选择节点'}
          </Box>
          <Box>
            {isModified && <span style={{ color: '#f57c00' }}>未保存的更改</span>}
            {!isModified && <span>已保存</span>}
          </Box>
        </Box>
        
        {/* 节点属性面板 - 桌面版 */}
        <Drawer
          variant="persistent"
          anchor="right"
          open={propertiesPanelOpen && !isMobile}
          sx={{
            width: 320,
            flexShrink: 0,
            '& .MuiDrawer-paper': {
              width: 320,
              boxSizing: 'border-box',
              borderLeft: '1px solid #e0e0e0',
              boxShadow: 'none'
            },
          }}
        >
          <NodePropertiesPanel
            selectedNode={selectedNode}
            onUpdateNode={onUpdateNode}
            onDeleteNode={onDeleteNode}
            onClosePanel={handleClosePropertiesPanel}
          />
        </Drawer>
        
        {/* 节点属性面板 - 移动版 */}
        <Drawer
          anchor="bottom"
          open={propertiesPanelOpen && isMobile}
          onClose={handleClosePropertiesPanel}
          sx={{
            '& .MuiDrawer-paper': {
              height: '60vh',
              borderTopLeftRadius: 12,
              borderTopRightRadius: 12,
            },
          }}
          ModalProps={{
            keepMounted: true,
          }}
        >
          <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">节点属性</Typography>
            <IconButton onClick={handleClosePropertiesPanel}>
              <CloseIcon />
            </IconButton>
          </Box>
          <Divider />
          <Box sx={{ height: 'calc(100% - 56px)', overflow: 'auto' }}>
            <NodePropertiesPanel
              selectedNode={selectedNode}
              onUpdateNode={onUpdateNode}
              onDeleteNode={onDeleteNode}
              onClosePanel={handleClosePropertiesPanel}
            />
          </Box>
        </Drawer>
      </Box>
    </ThemeProvider>
  );
}

export default App;
