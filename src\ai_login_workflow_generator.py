"""
AI登录工作流生成器

使用AI+browser-use分析登录页面，生成可复用的登录工作流
"""
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path

from ai_llm_manager import get_llm_manager, LLMProvider
from workflow.models import Step, Workflow
from enhanced_ocr_integration import get_enhanced_ocr_analyzer

logger = logging.getLogger(__name__)


@dataclass
class LoginElement:
    """登录页面元素"""
    element_type: str  # company, username, password, captcha, submit
    selector: str
    description: str
    placeholder: str = ""
    required: bool = True
    confidence: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "element_type": self.element_type,
            "selector": self.selector,
            "description": self.description,
            "placeholder": self.placeholder,
            "required": self.required,
            "confidence": self.confidence
        }


@dataclass
class LoginPageAnalysis:
    """登录页面分析结果"""
    url: str
    title: str
    elements: List[LoginElement]
    has_captcha: bool
    analysis_time: datetime
    confidence: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "url": self.url,
            "title": self.title,
            "elements": [elem.to_dict() for elem in self.elements],
            "has_captcha": self.has_captcha,
            "analysis_time": self.analysis_time.isoformat(),
            "confidence": self.confidence
        }


@dataclass
class LoginWorkflowTemplate:
    """登录工作流模板"""
    name: str
    url: str
    description: str
    workflow: Workflow
    test_results: List[Dict[str, Any]] = field(default_factory=list)
    success_rate: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "url": self.url,
            "description": self.description,
            "workflow": self.workflow.to_dict(),
            "test_results": self.test_results,
            "success_rate": self.success_rate,
            "created_at": self.created_at.isoformat()
        }


class AILoginPageAnalyzer:
    """AI登录页面分析器"""
    
    def __init__(self, llm_provider: str = "gemini"):
        self.llm_manager = get_llm_manager()
        if llm_provider.lower() == "gemini":
            self.llm_provider = LLMProvider.GEMINI
        elif llm_provider.lower() == "openai":
            self.llm_provider = LLMProvider.OPENAI
        elif llm_provider.lower() == "qwen":
            self.llm_provider = LLMProvider.QWEN
        else:
            self.llm_provider = LLMProvider.GEMINI
    
    async def analyze_login_page(self, page) -> LoginPageAnalysis:
        """分析登录页面"""
        try:
            # 获取页面基本信息
            url = page.url
            title = await page.title()
            
            logger.info(f"开始分析登录页面: {url}")
            
            # 获取页面HTML结构
            page_content = await page.content()
            
            # 使用AI分析页面结构
            elements = await self._ai_analyze_page_elements(page, page_content)
            
            # 检测验证码
            has_captcha = await self._detect_captcha(page)
            
            # 计算整体置信度
            confidence = self._calculate_confidence(elements)
            
            analysis = LoginPageAnalysis(
                url=url,
                title=title,
                elements=elements,
                has_captcha=has_captcha,
                analysis_time=datetime.now(),
                confidence=confidence
            )
            
            logger.info(f"页面分析完成，发现 {len(elements)} 个登录元素")
            
            return analysis
            
        except Exception as e:
            logger.error(f"登录页面分析失败: {e}")
            raise
    
    async def _ai_analyze_page_elements(self, page, page_content: str) -> List[LoginElement]:
        """使用AI分析页面元素"""
        try:
            # 获取所有可能的输入元素
            input_elements = await page.query_selector_all("input, button, a")
            
            elements_info = []
            for element in input_elements:
                try:
                    tag_name = await element.evaluate("el => el.tagName.toLowerCase()")
                    element_type = await element.get_attribute("type") or ""
                    placeholder = await element.get_attribute("placeholder") or ""
                    name = await element.get_attribute("name") or ""
                    id_attr = await element.get_attribute("id") or ""
                    class_attr = await element.get_attribute("class") or ""
                    text_content = await element.text_content() or ""
                    
                    # 生成选择器
                    selector = await self._generate_selector(element)
                    
                    elements_info.append({
                        "tag": tag_name,
                        "type": element_type,
                        "placeholder": placeholder,
                        "name": name,
                        "id": id_attr,
                        "class": class_attr,
                        "text": text_content.strip()[:50],
                        "selector": selector
                    })
                except Exception:
                    continue
            
            # 使用AI分析元素类型
            prompt = f"""
分析以下登录页面的表单元素，识别每个元素的用途：

页面标题: {await page.title()}
页面URL: {page.url}

表单元素信息:
{json.dumps(elements_info, ensure_ascii=False, indent=2)}

请分析每个元素的用途，返回JSON格式：
{{
    "elements": [
        {{
            "selector": "元素选择器",
            "element_type": "company|username|password|captcha|submit",
            "description": "元素描述",
            "placeholder": "占位符文本",
            "required": true/false,
            "confidence": 0.95
        }}
    ]
}}

分析规则：
1. company: 企业名称、公司名称输入框
2. username: 用户名、账号、邮箱输入框
3. password: 密码输入框
4. captcha: 验证码输入框
5. submit: 登录、提交按钮

只返回与登录相关的元素，置信度要基于元素属性的匹配程度。
"""
            
            response = await self.llm_manager.generate(prompt, provider=self.llm_provider)
            
            # 解析AI响应
            content = response.content.strip()
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            ai_result = json.loads(content)
            
            # 转换为LoginElement对象
            login_elements = []
            for elem_data in ai_result.get("elements", []):
                element = LoginElement(
                    element_type=elem_data.get("element_type", "unknown"),
                    selector=elem_data.get("selector", ""),
                    description=elem_data.get("description", ""),
                    placeholder=elem_data.get("placeholder", ""),
                    required=elem_data.get("required", True),
                    confidence=elem_data.get("confidence", 0.5)
                )
                login_elements.append(element)
            
            return login_elements
            
        except Exception as e:
            logger.error(f"AI分析页面元素失败: {e}")
            # 返回基础的元素检测结果
            return await self._fallback_element_detection(page)
    
    async def _generate_selector(self, element) -> str:
        """生成元素选择器"""
        try:
            # 尝试多种选择器策略
            selectors = []
            
            # ID选择器
            id_attr = await element.get_attribute("id")
            if id_attr:
                selectors.append(f"#{id_attr}")
            
            # Name选择器
            name_attr = await element.get_attribute("name")
            if name_attr:
                selectors.append(f"[name='{name_attr}']")
            
            # Placeholder选择器
            placeholder = await element.get_attribute("placeholder")
            if placeholder:
                selectors.append(f"[placeholder*='{placeholder}']")
            
            # 类型选择器
            type_attr = await element.get_attribute("type")
            if type_attr:
                selectors.append(f"input[type='{type_attr}']")
            
            # 返回最具体的选择器
            return selectors[0] if selectors else "unknown"
            
        except Exception:
            return "unknown"
    
    async def _detect_captcha(self, page) -> bool:
        """检测验证码"""
        try:
            captcha_selectors = [
                "img[alt*='验证码']",
                "img[src*='captcha']",
                "img[src*='verify']",
                ".captcha",
                "#captcha"
            ]
            
            for selector in captcha_selectors:
                element = await page.query_selector(selector)
                if element and await element.is_visible():
                    return True
            
            return False
            
        except Exception:
            return False
    
    async def _fallback_element_detection(self, page) -> List[LoginElement]:
        """备用元素检测"""
        elements = []
        
        try:
            # 检测企业名称输入框
            company_selectors = ["input[placeholder*='企业']", "input[name*='company']"]
            for selector in company_selectors:
                element = await page.query_selector(selector)
                if element:
                    elements.append(LoginElement(
                        element_type="company",
                        selector=selector,
                        description="企业名称输入框",
                        confidence=0.8
                    ))
                    break
            
            # 检测用户名输入框
            username_selectors = ["input[placeholder*='用户名']", "input[name*='username']"]
            for selector in username_selectors:
                element = await page.query_selector(selector)
                if element:
                    elements.append(LoginElement(
                        element_type="username",
                        selector=selector,
                        description="用户名输入框",
                        confidence=0.8
                    ))
                    break
            
            # 检测密码输入框
            password_element = await page.query_selector("input[type='password']")
            if password_element:
                elements.append(LoginElement(
                    element_type="password",
                    selector="input[type='password']",
                    description="密码输入框",
                    confidence=0.9
                ))
            
            # 检测提交按钮
            submit_selectors = ["button[type='submit']", "button:has-text('登录')"]
            for selector in submit_selectors:
                element = await page.query_selector(selector)
                if element:
                    elements.append(LoginElement(
                        element_type="submit",
                        selector=selector,
                        description="登录按钮",
                        confidence=0.8
                    ))
                    break
            
        except Exception as e:
            logger.error(f"备用元素检测失败: {e}")
        
        return elements
    
    def _calculate_confidence(self, elements: List[LoginElement]) -> float:
        """计算整体置信度"""
        if not elements:
            return 0.0
        
        # 检查必要元素是否存在
        element_types = {elem.element_type for elem in elements}
        required_types = {"username", "password", "submit"}
        
        # 基础分数
        base_score = len(element_types.intersection(required_types)) / len(required_types)
        
        # 元素置信度平均分
        avg_confidence = sum(elem.confidence for elem in elements) / len(elements)
        
        # 综合置信度
        return (base_score * 0.6 + avg_confidence * 0.4)


class LoginWorkflowGenerator:
    """登录工作流生成器"""
    
    def __init__(self):
        self.analyzer = AILoginPageAnalyzer()
        self.ocr_analyzer = get_enhanced_ocr_analyzer()
    
    async def generate_login_workflow(self, url: str, page_analysis: LoginPageAnalysis) -> Workflow:
        """根据页面分析生成登录工作流"""
        try:
            steps = []
            step_id = 1
            
            # 步骤1: 导航到登录页面
            nav_step = Step(
                id=f"step_{step_id}",
                type="navigate",
                description=f"导航到登录页面",
                metadata={"url": url}
            )
            steps.append(nav_step)
            step_id += 1
            
            # 根据分析结果生成填写步骤
            for element in page_analysis.elements:
                if element.element_type == "company":
                    step = Step(
                        id=f"step_{step_id}",
                        type="fill",
                        description=f"填写企业名称",
                        metadata={
                            "selector": element.selector,
                            "value": "{{company_name}}",
                            "description": element.description
                        }
                    )
                    steps.append(step)
                    step_id += 1
                
                elif element.element_type == "username":
                    step = Step(
                        id=f"step_{step_id}",
                        type="fill",
                        description=f"填写用户名",
                        metadata={
                            "selector": element.selector,
                            "value": "{{username}}",
                            "description": element.description
                        }
                    )
                    steps.append(step)
                    step_id += 1
                
                elif element.element_type == "password":
                    step = Step(
                        id=f"step_{step_id}",
                        type="fill",
                        description=f"填写密码",
                        metadata={
                            "selector": element.selector,
                            "value": "{{password}}",
                            "description": element.description,
                            "sensitive": True
                        }
                    )
                    steps.append(step)
                    step_id += 1
                
                elif element.element_type == "captcha":
                    # 使用通用图片识别节点处理验证码
                    from workflow_image_recognition_node import WorkflowImageRecognitionGenerator
                    from universal_image_recognition import RecognitionMethod

                    # 查找验证码输入框
                    captcha_input_selectors = []
                    for inp_elem in page_analysis.elements:
                        if inp_elem.element_type == "captcha" and inp_elem.selector != element.selector:
                            captcha_input_selectors.append(inp_elem.selector)

                    # 如果没有找到专门的验证码输入框，使用通用选择器
                    if not captcha_input_selectors:
                        captcha_input_selectors = [
                            "input[name='captcha']",
                            "input[placeholder*='验证码']",
                            "input[id*='captcha']"
                        ]

                    step = WorkflowImageRecognitionGenerator.create_captcha_recognition_step(
                        step_id=f"step_{step_id}",
                        target_selectors=[element.selector],
                        input_selectors=captcha_input_selectors,
                        recognition_method=RecognitionMethod.OCR_THEN_AI,
                        ocr_confidence_threshold=0.7,
                        ai_confidence_threshold=0.6,
                        max_retry_count=3,
                        validation_rules={
                            "min_length": 3,
                            "max_length": 8,
                            "alphanumeric_only": True
                        }
                    )
                    steps.append(step)
                    step_id += 1
            
            # 最后添加提交步骤
            submit_element = next((elem for elem in page_analysis.elements if elem.element_type == "submit"), None)
            if submit_element:
                submit_step = Step(
                    id=f"step_{step_id}",
                    type="click",
                    description="点击登录按钮",
                    metadata={
                        "selector": submit_element.selector,
                        "description": submit_element.description
                    }
                )
                steps.append(submit_step)
                step_id += 1
            
            # 添加结果验证步骤
            verify_step = Step(
                id=f"step_{step_id}",
                type="verify",
                description="验证登录结果",
                metadata={
                    "success_indicators": [
                        "url_change",
                        "text=欢迎",
                        "text=首页",
                        ".user-info"
                    ],
                    "error_indicators": [
                        "text=用户名或密码错误",
                        "text=验证码错误",
                        ".error-message"
                    ]
                }
            )
            steps.append(verify_step)
            
            # 创建工作流
            workflow = Workflow(
                name=f"登录工作流_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                description=f"AI生成的{page_analysis.title}登录工作流",
                version="1.0.0",
                steps=steps,
                variables={
                    "company_name": "",
                    "username": "",
                    "password": ""
                }
            )
            
            return workflow
            
        except Exception as e:
            logger.error(f"生成登录工作流失败: {e}")
            raise


class AILoginWorkflowSystem:
    """AI登录工作流系统"""
    
    def __init__(self):
        self.analyzer = AILoginPageAnalyzer()
        self.generator = LoginWorkflowGenerator()
        self.templates_dir = Path("login_workflows")
        self.templates_dir.mkdir(exist_ok=True)
    
    async def learn_login_workflow(self, url: str) -> Tuple[LoginPageAnalysis, Workflow]:
        """学习生成登录工作流"""
        try:
            from playwright.async_api import async_playwright
            
            logger.info(f"开始学习登录工作流: {url}")
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=False)
                page = await browser.new_page()
                
                try:
                    # 打开登录页面
                    await page.goto(url)
                    await page.wait_for_load_state('networkidle')
                    
                    # AI分析页面
                    analysis = await self.analyzer.analyze_login_page(page)
                    
                    # 生成工作流
                    workflow = await self.generator.generate_login_workflow(url, analysis)
                    
                    return analysis, workflow
                    
                finally:
                    await browser.close()
                    
        except Exception as e:
            logger.error(f"学习登录工作流失败: {e}")
            raise
    
    def save_login_workflow(self, template: LoginWorkflowTemplate) -> str:
        """保存登录工作流模板"""
        try:
            # 生成文件名
            safe_name = "".join(c for c in template.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = self.templates_dir / filename
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(template.to_dict(), f, indent=2, ensure_ascii=False)
            
            logger.info(f"登录工作流已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"保存登录工作流失败: {e}")
            raise
    
    def load_login_workflow(self, filepath: str) -> LoginWorkflowTemplate:
        """加载登录工作流模板"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 重建工作流对象
            workflow_data = data["workflow"]
            steps = []
            for step_data in workflow_data["steps"]:
                step = Step(
                    id=step_data["id"],
                    type=step_data["type"],
                    description=step_data["description"],
                    metadata=step_data.get("metadata", {})
                )
                steps.append(step)
            
            workflow = Workflow(
                name=workflow_data["name"],
                description=workflow_data["description"],
                version=workflow_data.get("version", "1.0.0"),
                steps=steps,
                variables=workflow_data.get("variables", {})
            )
            
            template = LoginWorkflowTemplate(
                name=data["name"],
                url=data["url"],
                description=data["description"],
                workflow=workflow,
                test_results=data.get("test_results", []),
                success_rate=data.get("success_rate", 0.0),
                created_at=datetime.fromisoformat(data["created_at"])
            )
            
            return template
            
        except Exception as e:
            logger.error(f"加载登录工作流失败: {e}")
            raise
    
    def list_login_workflows(self) -> List[Dict[str, Any]]:
        """列出所有登录工作流"""
        workflows = []
        
        for filepath in self.templates_dir.glob("*.json"):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                workflows.append({
                    "filepath": str(filepath),
                    "name": data.get("name", ""),
                    "url": data.get("url", ""),
                    "description": data.get("description", ""),
                    "success_rate": data.get("success_rate", 0.0),
                    "created_at": data.get("created_at", "")
                })
                
            except Exception as e:
                logger.warning(f"读取工作流文件失败: {filepath} - {e}")
                continue
        
        return sorted(workflows, key=lambda x: x["created_at"], reverse=True)


# 全局实例
global_ai_login_system = AILoginWorkflowSystem()


class LoginWorkflowExecutor:
    """登录工作流执行器"""

    def __init__(self):
        self.ocr_analyzer = get_enhanced_ocr_analyzer()

    async def execute_login_workflow(self, template: LoginWorkflowTemplate, credentials: Dict[str, str], headless: bool = False) -> Dict[str, Any]:
        """执行登录工作流"""
        try:
            from playwright.async_api import async_playwright

            start_time = datetime.now()

            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=headless)
                page = await browser.new_page()

                try:
                    execution_log = []

                    for step in template.workflow.steps:
                        step_start = datetime.now()

                        try:
                            if step.type == "navigate":
                                await page.goto(step.metadata["url"])
                                await page.wait_for_load_state('networkidle')
                                execution_log.append({
                                    "step_id": step.id,
                                    "type": step.type,
                                    "status": "success",
                                    "message": f"导航到 {step.metadata['url']}"
                                })

                            elif step.type == "fill":
                                selector = step.metadata["selector"]
                                value_template = step.metadata["value"]

                                # 替换变量
                                if value_template.startswith("{{") and value_template.endswith("}}"):
                                    var_name = value_template[2:-2]
                                    value = credentials.get(var_name, "")
                                else:
                                    value = value_template

                                # 填写字段
                                element = await page.wait_for_selector(selector, timeout=10000)
                                await element.fill(value)

                                execution_log.append({
                                    "step_id": step.id,
                                    "type": step.type,
                                    "status": "success",
                                    "message": f"填写 {step.description}"
                                })

                            elif step.type == "captcha":
                                # 处理验证码（兼容旧格式）
                                captcha_result = await self._handle_captcha_step(page, step)
                                execution_log.append({
                                    "step_id": step.id,
                                    "type": step.type,
                                    "status": "success" if captcha_result["success"] else "warning",
                                    "message": captcha_result["message"]
                                })

                            elif step.type == "image_recognition":
                                # 处理通用图片识别
                                recognition_result = await self._handle_image_recognition_step(page, step)
                                execution_log.append({
                                    "step_id": step.id,
                                    "type": step.type,
                                    "status": "success" if recognition_result["success"] else "warning",
                                    "message": recognition_result["message"],
                                    "recognized_text": recognition_result.get("recognized_text", ""),
                                    "confidence": recognition_result.get("confidence", 0.0),
                                    "method": recognition_result.get("method_used", ""),
                                    "processing_time": recognition_result.get("processing_time", 0.0)
                                })

                            elif step.type == "click":
                                selector = step.metadata["selector"]
                                element = await page.wait_for_selector(selector, timeout=10000)
                                await element.click()

                                execution_log.append({
                                    "step_id": step.id,
                                    "type": step.type,
                                    "status": "success",
                                    "message": f"点击 {step.description}"
                                })

                            elif step.type == "verify":
                                # 验证登录结果
                                verify_result = await self._verify_login_result(page, step)
                                execution_log.append({
                                    "step_id": step.id,
                                    "type": step.type,
                                    "status": "success" if verify_result["success"] else "error",
                                    "message": verify_result["message"]
                                })

                                if verify_result["success"]:
                                    # 登录成功，自动保存登录状态（在浏览器关闭前）
                                    try:
                                        from login_state_manager import get_login_state_manager

                                        state_manager = get_login_state_manager()
                                        session_name = f"{template.name}_{credentials.get('username', 'user')}"
                                        session_description = f"自动保存的登录状态 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

                                        # 确保页面仍然可用
                                        try:
                                            current_url = page.url
                                            logger.info(f"准备保存登录状态，当前URL: {current_url}")
                                        except Exception as page_error:
                                            logger.warning(f"页面可能已关闭，跳过状态保存: {page_error}")
                                            raise Exception("页面不可用，无法保存登录状态")

                                        session_id = await state_manager.save_login_session(
                                            page,
                                            session_name,
                                            session_description,
                                            user_info=credentials
                                        )

                                        execution_log.append({
                                            "step_id": "auto_save_state",
                                            "type": "save_login_state",
                                            "status": "success",
                                            "message": f"登录状态已自动保存: {session_id}",
                                            "session_id": session_id,
                                            "session_name": session_name
                                        })

                                        logger.info(f"登录状态自动保存成功: {session_id}")

                                    except Exception as e:
                                        logger.warning(f"自动保存登录状态失败: {e}")
                                        execution_log.append({
                                            "step_id": "auto_save_state",
                                            "type": "save_login_state",
                                            "status": "warning",
                                            "message": f"保存登录状态失败: {e}"
                                        })
                                else:
                                    break

                        except Exception as e:
                            execution_log.append({
                                "step_id": step.id,
                                "type": step.type,
                                "status": "error",
                                "message": f"步骤执行失败: {str(e)}"
                            })
                            break

                    # 计算执行结果
                    total_duration = (datetime.now() - start_time).total_seconds()
                    success_steps = sum(1 for log in execution_log if log["status"] == "success")
                    total_steps = len(execution_log)

                    final_success = (
                        total_steps > 0 and
                        execution_log[-1]["type"] == "verify" and
                        execution_log[-1]["status"] == "success"
                    )

                    # 如果登录成功但还没有保存状态，在关闭浏览器前尝试保存
                    if final_success and not any(log.get("type") == "save_login_state" and log.get("status") == "success" for log in execution_log):
                        try:
                            logger.info("登录成功但未保存状态，尝试在关闭浏览器前保存")
                            from login_state_manager import get_login_state_manager

                            state_manager = get_login_state_manager()
                            session_name = f"{template.name}_{credentials.get('username', 'user')}_final"
                            session_description = f"最终保存的登录状态 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

                            session_id = await state_manager.save_login_session(
                                page,
                                session_name,
                                session_description,
                                user_info=credentials
                            )

                            execution_log.append({
                                "step_id": "final_save_state",
                                "type": "save_login_state",
                                "status": "success",
                                "message": f"最终登录状态已保存: {session_id}",
                                "session_id": session_id,
                                "session_name": session_name
                            })

                            logger.info(f"最终登录状态保存成功: {session_id}")

                        except Exception as e:
                            logger.warning(f"最终保存登录状态失败: {e}")
                            execution_log.append({
                                "step_id": "final_save_state",
                                "type": "save_login_state",
                                "status": "warning",
                                "message": f"最终保存登录状态失败: {e}"
                            })

                    return {
                        "success": final_success,
                        "execution_time": total_duration,
                        "steps_executed": total_steps,
                        "steps_succeeded": success_steps,
                        "execution_log": execution_log,
                        "final_url": page.url
                    }

                finally:
                    await browser.close()

        except Exception as e:
            logger.error(f"执行登录工作流失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": 0,
                "steps_executed": 0,
                "steps_succeeded": 0,
                "execution_log": []
            }

    async def _handle_captcha_step(self, page, step) -> Dict[str, Any]:
        """处理验证码步骤 - 使用增强验证码处理器"""
        try:
            from enhanced_captcha_processor import get_enhanced_captcha_processor

            # 使用增强验证码处理器（OCR → AI → 用户处理）
            processor = get_enhanced_captcha_processor()
            result = await processor.process_captcha_complete(page, interactive=True)

            if result.success:
                return {
                    "success": True,
                    "message": f"验证码处理成功: {result.recognized_text} (方法: {result.method}, 置信度: {result.confidence:.2f})",
                    "recognized_text": result.recognized_text,
                    "confidence": result.confidence,
                    "method": result.method,
                    "processing_time": result.processing_time
                }
            else:
                return {
                    "success": False,
                    "message": f"验证码处理失败: {result.error_message}",
                    "method": result.method,
                    "error_details": result.error_message
                }

        except Exception as e:
            return {"success": False, "message": f"验证码处理异常: {str(e)}"}

    async def _handle_image_recognition_step(self, page, step) -> Dict[str, Any]:
        """处理通用图片识别步骤"""
        try:
            from workflow_image_recognition_node import get_image_recognition_workflow_node

            # 使用通用图片识别节点
            node = get_image_recognition_workflow_node()
            result = await node.execute(page, step)

            return result

        except Exception as e:
            return {"success": False, "message": f"图片识别处理异常: {str(e)}"}

    async def _verify_login_result(self, page, step) -> Dict[str, Any]:
        """验证登录结果"""
        try:
            await page.wait_for_timeout(3000)  # 等待页面响应

            current_url = page.url
            success_indicators = step.metadata.get("success_indicators", [])
            error_indicators = step.metadata.get("error_indicators", [])

            # 检查成功指标
            for indicator in success_indicators:
                if indicator == "url_change":
                    # URL变化检查
                    if "login" not in current_url.lower():
                        return {"success": True, "message": "登录成功，页面已跳转"}
                else:
                    # 元素检查
                    try:
                        element = await page.query_selector(indicator)
                        if element and await element.is_visible():
                            return {"success": True, "message": f"登录成功，发现成功指标: {indicator}"}
                    except Exception:
                        continue

            # 检查错误指标
            for indicator in error_indicators:
                try:
                    element = await page.query_selector(indicator)
                    if element and await element.is_visible():
                        error_text = await element.text_content()
                        return {"success": False, "message": f"登录失败: {error_text}"}
                except Exception:
                    continue

            # 默认判断
            return {"success": True, "message": "登录状态不明确，假设成功"}

        except Exception as e:
            return {"success": False, "message": f"验证登录结果失败: {str(e)}"}


class LoginStateWorkflowExecutor:
    """支持登录状态的工作流执行器"""

    def __init__(self):
        from login_state_manager import get_login_state_manager
        self.state_manager = get_login_state_manager()
        self.base_executor = LoginWorkflowExecutor()

    async def execute_with_cached_login(
        self,
        session_id: str,
        target_url: str = None,
        headless: bool = True
    ) -> Dict[str, Any]:
        """使用缓存的登录状态执行操作"""
        try:
            logger.info(f"使用缓存登录状态执行: {session_id}")

            # 加载登录会话
            session = await self.state_manager.load_login_session(session_id)
            if not session:
                return {
                    "success": False,
                    "error": f"登录会话不存在: {session_id}",
                    "execution_time": 0
                }

            if not session.is_valid:
                return {
                    "success": False,
                    "error": f"登录会话已过期: {session_id}",
                    "execution_time": 0
                }

            start_time = datetime.now()

            from playwright.async_api import async_playwright

            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=headless)
                context = await browser.new_context()
                page = await context.new_page()

                try:
                    # 恢复登录状态
                    restore_success = await self.state_manager.restore_login_state(page, session)

                    if not restore_success:
                        return {
                            "success": False,
                            "error": "恢复登录状态失败",
                            "execution_time": (datetime.now() - start_time).total_seconds()
                        }

                    # 验证登录状态
                    login_valid = await self.state_manager.verify_login_state(page, session)

                    if not login_valid:
                        return {
                            "success": False,
                            "error": "登录状态验证失败，可能已过期",
                            "execution_time": (datetime.now() - start_time).total_seconds()
                        }

                    # 如果指定了目标URL，导航到目标页面
                    if target_url:
                        await page.goto(target_url)
                        await page.wait_for_load_state('networkidle')

                    execution_time = (datetime.now() - start_time).total_seconds()

                    return {
                        "success": True,
                        "message": "使用缓存登录状态成功",
                        "session_id": session_id,
                        "session_name": session.name,
                        "current_url": page.url,
                        "execution_time": execution_time,
                        "page": page,  # 返回页面对象供后续操作
                        "browser": browser,  # 返回浏览器对象
                        "context": context  # 返回上下文对象
                    }

                except Exception as e:
                    await browser.close()
                    raise e

        except Exception as e:
            logger.error(f"使用缓存登录状态执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": (datetime.now() - start_time).total_seconds() if 'start_time' in locals() else 0
            }

    async def list_available_sessions(self) -> List[Dict[str, Any]]:
        """列出可用的登录会话"""
        return self.state_manager.list_login_sessions()

    async def delete_session(self, session_id: str) -> bool:
        """删除登录会话"""
        return await self.state_manager.delete_login_session(session_id)


def get_ai_login_system() -> AILoginWorkflowSystem:
    """获取AI登录工作流系统实例"""
    return global_ai_login_system


def get_login_state_executor() -> LoginStateWorkflowExecutor:
    """获取支持登录状态的工作流执行器"""
    return LoginStateWorkflowExecutor()
