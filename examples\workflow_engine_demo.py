"""
工作流引擎演示

展示新开发的工作流引擎功能：
1. 条件分支执行
2. 循环控制
3. 并行执行
4. 脚本执行
5. 变量管理
"""
import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.workflow.engine import WorkflowEngine
from src.workflow.models import WorkflowDefinition, StepDefinition
from src.workflow.dsl import WorkflowDSL


def create_condition_workflow():
    """创建条件分支演示工作流"""
    return {
        "name": "条件分支演示",
        "description": "演示条件分支执行功能",
        "variables": {
            "user_role": "admin",
            "debug_mode": True
        },
        "steps": [
            {
                "id": "check_role",
                "type": "condition",
                "name": "检查用户角色",
                "expression": "user_role == 'admin'",
                "then_steps": [
                    {
                        "id": "admin_action",
                        "type": "script",
                        "name": "管理员操作",
                        "language": "python",
                        "code": "print('执行管理员操作')\ncontext['admin_executed'] = True"
                    }
                ],
                "else_steps": [
                    {
                        "id": "user_action",
                        "type": "script",
                        "name": "普通用户操作",
                        "language": "python",
                        "code": "print('执行普通用户操作')\ncontext['user_executed'] = True"
                    }
                ]
            }
        ]
    }


def create_loop_workflow():
    """创建循环演示工作流"""
    return {
        "name": "循环演示",
        "description": "演示不同类型的循环功能",
        "variables": {
            "items": ["apple", "banana", "orange"],
            "counter": 0
        },
        "steps": [
            {
                "id": "foreach_loop",
                "type": "loop",
                "name": "遍历水果列表",
                "loop_type": "foreach",
                "items": ["apple", "banana", "orange"],
                "variable": "fruit",
                "steps": [
                    {
                        "id": "process_fruit",
                        "type": "script",
                        "name": "处理水果",
                        "language": "python",
                        "code": "print(f'处理水果: {context[\"fruit\"]}')\ncontext['processed_count'] = context.get('processed_count', 0) + 1"
                    }
                ]
            },
            {
                "id": "for_loop",
                "type": "loop",
                "name": "计数循环",
                "loop_type": "for",
                "count": 3,
                "steps": [
                    {
                        "id": "count_action",
                        "type": "script",
                        "name": "计数操作",
                        "language": "python",
                        "code": "print(f'计数: {context[\"loop\"][\"index\"] + 1}')"
                    }
                ]
            }
        ]
    }


def create_parallel_workflow():
    """创建并行执行演示工作流"""
    return {
        "name": "并行执行演示",
        "description": "演示并行执行功能",
        "steps": [
            {
                "id": "parallel_tasks",
                "type": "parallel",
                "name": "并行任务",
                "max_workers": 3,
                "wait_for_all": True,
                "steps": [
                    {
                        "id": "task1",
                        "type": "script",
                        "name": "任务1",
                        "language": "python",
                        "code": "import time\ntime.sleep(1)\nprint('任务1完成')\ncontext['task1_result'] = 'success'"
                    },
                    {
                        "id": "task2",
                        "type": "script",
                        "name": "任务2",
                        "language": "python",
                        "code": "import time\ntime.sleep(0.5)\nprint('任务2完成')\ncontext['task2_result'] = 'success'"
                    },
                    {
                        "id": "task3",
                        "type": "wait",
                        "name": "等待任务",
                        "duration": 800
                    }
                ]
            }
        ]
    }


def create_complex_workflow():
    """创建复杂工作流演示"""
    return {
        "name": "复杂工作流演示",
        "description": "演示多种功能组合使用",
        "variables": {
            "environment": "test",
            "max_retries": 3,
            "test_data": [
                {"name": "test1", "enabled": True},
                {"name": "test2", "enabled": False},
                {"name": "test3", "enabled": True}
            ]
        },
        "steps": [
            # 1. 环境检查
            {
                "id": "env_check",
                "type": "condition",
                "name": "环境检查",
                "expression": "environment == 'test'",
                "then_steps": [
                    {
                        "id": "test_setup",
                        "type": "script",
                        "name": "测试环境设置",
                        "language": "python",
                        "code": "print('设置测试环境')\ncontext['env_ready'] = True"
                    }
                ],
                "else_steps": [
                    {
                        "id": "prod_setup",
                        "type": "script",
                        "name": "生产环境设置",
                        "language": "python",
                        "code": "print('设置生产环境')\ncontext['env_ready'] = True"
                    }
                ]
            },
            
            # 2. 数据处理循环
            {
                "id": "data_processing",
                "type": "loop",
                "name": "数据处理",
                "loop_type": "foreach",
                "items": [
                    {"name": "test1", "enabled": True},
                    {"name": "test2", "enabled": False},
                    {"name": "test3", "enabled": True}
                ],
                "variable": "test_item",
                "steps": [
                    {
                        "id": "check_enabled",
                        "type": "condition",
                        "name": "检查是否启用",
                        "expression": "test_item['enabled'] == True",
                        "then_steps": [
                            {
                                "id": "process_test",
                                "type": "script",
                                "name": "处理测试项",
                                "language": "python",
                                "code": "print(f'处理测试项: {context[\"test_item\"][\"name\"]}')\ncontext['processed_items'] = context.get('processed_items', []) + [context['test_item']['name']]"
                            }
                        ]
                    }
                ]
            },
            
            # 3. 并行验证
            {
                "id": "parallel_validation",
                "type": "parallel",
                "name": "并行验证",
                "max_workers": 2,
                "steps": [
                    {
                        "id": "validate_results",
                        "type": "script",
                        "name": "验证结果",
                        "language": "python",
                        "code": "processed = context.get('processed_items', [])\nprint(f'验证处理结果: {len(processed)} 个项目')\ncontext['validation_passed'] = len(processed) > 0"
                    },
                    {
                        "id": "generate_report",
                        "type": "script",
                        "name": "生成报告",
                        "language": "python",
                        "code": "import json\nreport = {'processed_count': len(context.get('processed_items', [])), 'env': context.get('environment', 'unknown')}\nprint(f'生成报告: {json.dumps(report)}')\ncontext['report'] = report"
                    }
                ]
            },
            
            # 4. 文件保存
            {
                "id": "save_results",
                "type": "file",
                "name": "保存结果",
                "operation": "write",
                "target": "workflow_results.json",
                "content": '{"status": "completed", "processed_items": {{ processed_items }}, "report": {{ report }}}'
            }
        ]
    }


async def run_workflow_demo(workflow_data, title):
    """运行工作流演示"""
    print(f"\n{'='*60}")
    print(f"🚀 {title}")
    print(f"{'='*60}")
    
    try:
        # 解析工作流
        dsl = WorkflowDSL()
        workflow = dsl.parse_dict(workflow_data)
        
        print(f"📋 工作流名称: {workflow.name}")
        print(f"📝 描述: {workflow.description}")
        print(f"🔧 步骤数量: {len(workflow.steps)}")
        
        # 创建工作流引擎
        engine = WorkflowEngine(workflow)
        
        # 执行工作流
        print(f"\n⏳ 开始执行工作流...")
        result = await engine.execute(workflow_data.get('variables', {}))
        
        # 输出结果
        print(f"\n📊 执行结果:")
        print(f"   状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
        
        if result['success']:
            print(f"   工作流: {result['workflow_name']}")
            
            # 显示变量状态
            variables = result.get('variables', {})
            if variables:
                print(f"\n📋 最终变量状态:")
                for key, value in variables.items():
                    if not key.startswith('_') and key not in ['workflow', 'execution', 'system']:
                        print(f"   {key}: {value}")
            
            # 显示步骤结果
            step_results = result.get('step_results', {})
            if step_results:
                print(f"\n🔍 步骤执行详情:")
                for step_id, step_result in step_results.items():
                    print(f"   {step_id}: {step_result}")
        else:
            print(f"   错误: {result.get('error', '未知错误')}")
        
        return result
        
    except Exception as e:
        print(f"\n❌ 工作流执行失败: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


async def main():
    """主函数"""
    print("🎭 工作流引擎功能演示")
    print("展示条件分支、循环、并行执行等高级功能")
    
    # 演示列表
    demos = [
        (create_condition_workflow(), "条件分支执行演示"),
        (create_loop_workflow(), "循环控制演示"),
        (create_parallel_workflow(), "并行执行演示"),
        (create_complex_workflow(), "复杂工作流演示")
    ]
    
    results = []
    
    # 运行所有演示
    for workflow_data, title in demos:
        result = await run_workflow_demo(workflow_data, title)
        results.append((title, result))
        
        # 等待一下再运行下一个演示
        await asyncio.sleep(1)
    
    # 总结
    print(f"\n{'='*60}")
    print("📈 演示总结")
    print(f"{'='*60}")
    
    successful = sum(1 for _, result in results if result.get('success', False))
    total = len(results)
    
    print(f"总演示数量: {total}")
    print(f"成功演示: {successful}")
    print(f"失败演示: {total - successful}")
    print(f"成功率: {successful/total*100:.1f}%")
    
    print(f"\n详细结果:")
    for title, result in results:
        status = "✅" if result.get('success', False) else "❌"
        print(f"  {status} {title}")
        if not result.get('success', False):
            print(f"     错误: {result.get('error', '未知错误')}")
    
    print(f"\n🎉 工作流引擎演示完成！")
    
    # 保存演示结果
    output_file = Path(__file__).parent / "workflow_engine_demo_results.json"
    import json
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            "demo_time": "2025-12-19",
            "total_demos": total,
            "successful_demos": successful,
            "success_rate": successful/total*100,
            "results": [{"title": title, "success": result.get('success', False), "error": result.get('error')} for title, result in results]
        }, f, indent=2, ensure_ascii=False)
    
    print(f"📄 演示结果已保存到: {output_file}")


if __name__ == "__main__":
    asyncio.run(main())
