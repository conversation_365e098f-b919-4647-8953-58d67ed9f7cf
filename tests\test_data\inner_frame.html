<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inner Frame</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 15px;
            background-color: #fffaf0;
        }
        .header {
            background-color: #ff9800;
            color: white;
            padding: 8px;
            margin: -15px -15px 15px -15px;
            text-align: center;
        }
        .search-container {
            display: flex;
            margin-bottom: 15px;
        }
        #search {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px 0 0 4px;
            font-size: 14px;
        }
        #search-button {
            padding: 8px 16px;
            background-color: #ff9800;
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
        }
        #search-button:hover {
            background-color: #e68a00;
        }
        .search-results {
            display: none;
            margin-top: 15px;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        .result-item {
            padding: 8px;
            margin: 5px 0;
            background-color: #fff8e1;
            border-left: 3px solid #ffc107;
        }
        .result-title {
            font-weight: bold;
            color: #ff6f00;
        }
        .result-snippet {
            font-size: 13px;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>Inner Frame</h3>
    </div>
    
    <p>This is the inner frame with a search form.</p>
    
    <div class="search-container">
        <input type="text" id="search" placeholder="Search in inner frame...">
        <button id="search-button">Search</button>
    </div>
    
    <div id="search-results" class="search-results">
        <h4>Search Results</h4>
        <div id="results-container"></div>
    </div>

    <script>
        // 搜索功能
        document.getElementById('search-button').addEventListener('click', performSearch);
        document.getElementById('search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // 模拟搜索结果
        const sampleResults = [
            {
                title: 'First Search Result',
                url: 'https://example.com/result1',
                snippet: 'This is the first sample search result from the inner frame.'
            },
            {
                title: 'Second Result',
                url: 'https://example.com/result2',
                snippet: 'Another sample result showing the search functionality in the inner iframe.'
            },
            {
                title: 'Third Item Found',
                url: 'https://example.com/result3',
                snippet: 'The third and final sample search result in our demonstration.'
            }
        ];

        function performSearch() {
            const searchTerm = document.getElementById('search').value.trim();
            if (!searchTerm) return;
            
            // 显示加载状态
            const resultsContainer = document.getElementById('results-container');
            resultsContainer.innerHTML = '<p>Searching for: ' + searchTerm + '...</p>';
            
            // 模拟搜索延迟
            setTimeout(() => {
                // 清空结果
                resultsContainer.innerHTML = '';
                
                // 添加结果
                sampleResults.forEach((result, index) => {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'result-item';
                    resultDiv.innerHTML = `
                        <div class="result-title">${result.title}</div>
                        <div style="color:#006621;font-size:13px;">${result.url}</div>
                        <div class="result-snippet">${result.snippet}</div>
                    `;
                    resultsContainer.appendChild(resultDiv);
                });
                
                // 显示结果区域
                document.getElementById('search-results').style.display = 'block';
                
                // 通知父框架
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'searchPerformed',
                        term: searchTerm,
                        resultCount: sampleResults.length
                    }, '*');
                }
                
                console.log('Search performed in inner frame:', searchTerm);
            }, 800);
        }
    </script>
</body>
</html>
