"""
登录状态管理器

实现登录状态的录制、保存、加载和恢复功能
"""
import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib

logger = logging.getLogger(__name__)


@dataclass
class LoginStateData:
    """登录状态数据"""
    url: str
    domain: str
    cookies: List[Dict[str, Any]]
    local_storage: Dict[str, str]
    session_storage: Dict[str, str]
    user_agent: str
    viewport: Dict[str, int]
    timestamp: str
    expires_at: str
    user_info: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LoginStateData':
        return cls(**data)


@dataclass
class LoginSession:
    """登录会话"""
    session_id: str
    name: str
    description: str
    login_url: str
    target_domain: str
    state_data: LoginStateData
    created_at: datetime
    last_used: datetime
    use_count: int = 0
    is_valid: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "session_id": self.session_id,
            "name": self.name,
            "description": self.description,
            "login_url": self.login_url,
            "target_domain": self.target_domain,
            "state_data": self.state_data.to_dict(),
            "created_at": self.created_at.isoformat(),
            "last_used": self.last_used.isoformat(),
            "use_count": self.use_count,
            "is_valid": self.is_valid
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LoginSession':
        return cls(
            session_id=data["session_id"],
            name=data["name"],
            description=data["description"],
            login_url=data["login_url"],
            target_domain=data["target_domain"],
            state_data=LoginStateData.from_dict(data["state_data"]),
            created_at=datetime.fromisoformat(data["created_at"]),
            last_used=datetime.fromisoformat(data["last_used"]),
            use_count=data.get("use_count", 0),
            is_valid=data.get("is_valid", True)
        )


class LoginStateRecorder:
    """登录状态录制器"""
    
    def __init__(self):
        self.recording = False
        self.recorded_states = []
    
    async def start_recording(self, page):
        """开始录制登录状态"""
        try:
            self.recording = True
            self.recorded_states = []
            
            logger.info("开始录制登录状态")
            
            # 记录初始状态
            initial_state = await self._capture_page_state(page)
            self.recorded_states.append({
                "type": "initial",
                "timestamp": datetime.now().isoformat(),
                "state": initial_state
            })
            
            return True
            
        except Exception as e:
            logger.error(f"开始录制失败: {e}")
            return False
    
    async def record_login_success(self, page, user_info: Dict[str, Any] = None) -> LoginStateData:
        """录制登录成功后的状态"""
        try:
            logger.info("录制登录成功状态")

            # 检查页面是否仍然可用
            try:
                # 尝试获取页面URL来检查页面状态
                current_url = page.url
                logger.info(f"当前页面URL: {current_url}")
            except Exception as e:
                logger.error(f"页面已关闭或不可用: {e}")
                raise Exception("页面已关闭，无法录制登录状态")

            # 等待页面稳定（减少等待时间并添加错误处理）
            try:
                await page.wait_for_timeout(1000)
            except Exception as e:
                logger.warning(f"等待页面稳定时出错: {e}")
                # 继续执行，不中断流程

            # 捕获完整的登录状态
            state_data = await self._capture_complete_login_state(page, user_info)

            # 记录登录成功状态
            self.recorded_states.append({
                "type": "login_success",
                "timestamp": datetime.now().isoformat(),
                "state": state_data.to_dict()
            })

            logger.info(f"登录状态录制完成: {state_data.domain}")

            return state_data

        except Exception as e:
            logger.error(f"录制登录成功状态失败: {e}")
            raise
    
    async def stop_recording(self):
        """停止录制"""
        self.recording = False
        logger.info("停止录制登录状态")
    
    async def _capture_page_state(self, page) -> Dict[str, Any]:
        """捕获页面状态"""
        try:
            return {
                "url": page.url,
                "title": await page.title(),
                "cookies": await page.context.cookies(),
                "local_storage": await page.evaluate("() => Object.assign({}, localStorage)"),
                "session_storage": await page.evaluate("() => Object.assign({}, sessionStorage)")
            }
        except Exception as e:
            logger.error(f"捕获页面状态失败: {e}")
            return {}
    
    async def _capture_complete_login_state(self, page, user_info: Dict[str, Any] = None) -> LoginStateData:
        """捕获完整的登录状态"""
        try:
            # 获取当前页面信息
            current_url = page.url
            domain = self._extract_domain(current_url)
            logger.info(f"捕获登录状态 - URL: {current_url}, 域名: {domain}")

            # 获取cookies（添加错误处理）
            try:
                cookies = await page.context.cookies()
                logger.info(f"获取到 {len(cookies)} 个cookies")
            except Exception as e:
                logger.warning(f"获取cookies失败: {e}")
                cookies = []

            # 获取localStorage（添加错误处理）
            try:
                local_storage = await page.evaluate("() => Object.assign({}, localStorage)")
                logger.info(f"获取到 {len(local_storage)} 个localStorage项")
            except Exception as e:
                logger.warning(f"获取localStorage失败: {e}")
                local_storage = {}

            # 获取sessionStorage（添加错误处理）
            try:
                session_storage = await page.evaluate("() => Object.assign({}, sessionStorage)")
                logger.info(f"获取到 {len(session_storage)} 个sessionStorage项")
            except Exception as e:
                logger.warning(f"获取sessionStorage失败: {e}")
                session_storage = {}

            # 获取用户代理（添加错误处理）
            try:
                user_agent = await page.evaluate("() => navigator.userAgent")
            except Exception as e:
                logger.warning(f"获取用户代理失败: {e}")
                user_agent = "Unknown"

            # 获取视口大小（添加错误处理）
            try:
                viewport = page.viewport_size
                if not viewport:
                    viewport = {"width": 1920, "height": 1080}
            except Exception as e:
                logger.warning(f"获取视口大小失败: {e}")
                viewport = {"width": 1920, "height": 1080}

            # 计算过期时间（默认7天）
            expires_at = (datetime.now() + timedelta(days=7)).isoformat()

            state_data = LoginStateData(
                url=current_url,
                domain=domain,
                cookies=cookies,
                local_storage=local_storage,
                session_storage=session_storage,
                user_agent=user_agent,
                viewport=viewport,
                timestamp=datetime.now().isoformat(),
                expires_at=expires_at,
                user_info=user_info or {}
            )

            logger.info(f"登录状态捕获完成: {domain}")
            return state_data

        except Exception as e:
            logger.error(f"捕获完整登录状态失败: {e}")
            raise
    
    def _extract_domain(self, url: str) -> str:
        """提取域名"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return f"{parsed.scheme}://{parsed.netloc}"
        except Exception:
            return url


class LoginStateManager:
    """登录状态管理器"""
    
    def __init__(self, storage_dir: str = "login_sessions"):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        self.recorder = LoginStateRecorder()
    
    async def save_login_session(
        self, 
        page, 
        session_name: str, 
        description: str = "",
        user_info: Dict[str, Any] = None
    ) -> str:
        """保存登录会话"""
        try:
            logger.info(f"保存登录会话: {session_name}")
            
            # 录制登录状态
            state_data = await self.recorder.record_login_success(page, user_info)
            
            # 生成会话ID
            session_id = self._generate_session_id(session_name, state_data.domain)
            
            # 创建登录会话
            session = LoginSession(
                session_id=session_id,
                name=session_name,
                description=description,
                login_url=state_data.url,
                target_domain=state_data.domain,
                state_data=state_data,
                created_at=datetime.now(),
                last_used=datetime.now()
            )
            
            # 保存到文件
            session_file = self.storage_dir / f"{session_id}.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session.to_dict(), f, indent=2, ensure_ascii=False)
            
            logger.info(f"登录会话已保存: {session_file}")
            
            return session_id
            
        except Exception as e:
            logger.error(f"保存登录会话失败: {e}")
            raise
    
    async def load_login_session(self, session_id: str) -> Optional[LoginSession]:
        """加载登录会话"""
        try:
            session_file = self.storage_dir / f"{session_id}.json"
            
            if not session_file.exists():
                logger.warning(f"登录会话文件不存在: {session_file}")
                return None
            
            with open(session_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            session = LoginSession.from_dict(data)
            
            # 检查会话是否过期
            if not self._is_session_valid(session):
                logger.warning(f"登录会话已过期: {session_id}")
                session.is_valid = False
            
            return session
            
        except Exception as e:
            logger.error(f"加载登录会话失败: {e}")
            return None
    
    async def restore_login_state(self, page, session: LoginSession) -> bool:
        """恢复登录状态"""
        try:
            logger.info(f"恢复登录状态: {session.name}")
            
            state_data = session.state_data
            
            # 设置用户代理
            await page.set_extra_http_headers({
                'User-Agent': state_data.user_agent
            })
            
            # 设置视口大小
            if state_data.viewport:
                try:
                    await page.set_viewport_size({
                        'width': state_data.viewport.get('width', 1920),
                        'height': state_data.viewport.get('height', 1080)
                    })
                except Exception as e:
                    logger.warning(f"设置视口大小失败: {e}")
                    # 跳过视口设置，不影响主要功能
            
            # 添加cookies
            if state_data.cookies:
                await page.context.add_cookies(state_data.cookies)
            
            # 导航到目标页面
            await page.goto(state_data.url)
            await page.wait_for_load_state('networkidle')
            
            # 恢复localStorage
            if state_data.local_storage:
                for key, value in state_data.local_storage.items():
                    try:
                        await page.evaluate(f"localStorage.setItem('{key}', '{value}')")
                    except Exception:
                        continue
            
            # 恢复sessionStorage
            if state_data.session_storage:
                for key, value in state_data.session_storage.items():
                    try:
                        await page.evaluate(f"sessionStorage.setItem('{key}', '{value}')")
                    except Exception:
                        continue
            
            # 刷新页面以应用存储状态
            await page.reload()
            await page.wait_for_load_state('networkidle')
            
            # 更新会话使用信息
            session.last_used = datetime.now()
            session.use_count += 1
            await self._update_session_file(session)
            
            logger.info(f"登录状态恢复成功: {session.name}")
            
            return True
            
        except Exception as e:
            logger.error(f"恢复登录状态失败: {e}")
            return False
    
    async def verify_login_state(self, page, session: LoginSession) -> bool:
        """验证登录状态是否有效"""
        try:
            # 检查当前URL是否在目标域名下
            current_url = page.url
            if session.state_data.domain not in current_url:
                return False
            
            # 检查是否有登录指示器
            login_indicators = [
                "text=欢迎",
                "text=首页",
                "text=用户中心",
                ".user-info",
                ".user-name",
                ".logout",
                ".profile"
            ]
            
            for indicator in login_indicators:
                try:
                    element = await page.query_selector(indicator)
                    if element and await element.is_visible():
                        return True
                except Exception:
                    continue
            
            # 检查是否有登录页面指示器（如果有则说明未登录）
            login_page_indicators = [
                "text=登录",
                "text=用户名",
                "text=密码",
                "input[type='password']",
                ".login-form"
            ]
            
            for indicator in login_page_indicators:
                try:
                    element = await page.query_selector(indicator)
                    if element and await element.is_visible():
                        return False
                except Exception:
                    continue
            
            # 默认认为登录状态有效
            return True
            
        except Exception as e:
            logger.error(f"验证登录状态失败: {e}")
            return False
    
    def list_login_sessions(self) -> List[Dict[str, Any]]:
        """列出所有登录会话"""
        sessions = []
        
        for session_file in self.storage_dir.glob("*.json"):
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                session = LoginSession.from_dict(data)
                
                sessions.append({
                    "session_id": session.session_id,
                    "name": session.name,
                    "description": session.description,
                    "domain": session.target_domain,
                    "created_at": session.created_at.isoformat(),
                    "last_used": session.last_used.isoformat(),
                    "use_count": session.use_count,
                    "is_valid": self._is_session_valid(session),
                    "file_path": str(session_file)
                })
                
            except Exception as e:
                logger.warning(f"读取会话文件失败: {session_file} - {e}")
                continue
        
        # 按最后使用时间排序
        sessions.sort(key=lambda x: x["last_used"], reverse=True)
        
        return sessions
    
    async def delete_login_session(self, session_id: str) -> bool:
        """删除登录会话"""
        try:
            session_file = self.storage_dir / f"{session_id}.json"
            
            if session_file.exists():
                session_file.unlink()
                logger.info(f"登录会话已删除: {session_id}")
                return True
            else:
                logger.warning(f"登录会话文件不存在: {session_id}")
                return False
                
        except Exception as e:
            logger.error(f"删除登录会话失败: {e}")
            return False
    
    def _generate_session_id(self, session_name: str, domain: str) -> str:
        """生成会话ID"""
        content = f"{session_name}_{domain}_{int(time.time())}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    def _is_session_valid(self, session: LoginSession) -> bool:
        """检查会话是否有效"""
        try:
            expires_at = datetime.fromisoformat(session.state_data.expires_at)
            return datetime.now() < expires_at and session.is_valid
        except Exception:
            return False
    
    async def _update_session_file(self, session: LoginSession):
        """更新会话文件"""
        try:
            session_file = self.storage_dir / f"{session.session_id}.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session.to_dict(), f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"更新会话文件失败: {e}")


# 全局实例
global_login_state_manager = LoginStateManager()


def get_login_state_manager() -> LoginStateManager:
    """获取登录状态管理器实例"""
    return global_login_state_manager


# 便捷函数
async def save_current_login_state(page, session_name: str, description: str = "") -> str:
    """便捷保存当前登录状态"""
    manager = get_login_state_manager()
    return await manager.save_login_session(page, session_name, description)


async def restore_login_state_by_id(page, session_id: str) -> bool:
    """便捷恢复登录状态"""
    manager = get_login_state_manager()
    session = await manager.load_login_session(session_id)
    
    if session:
        return await manager.restore_login_state(page, session)
    else:
        return False
