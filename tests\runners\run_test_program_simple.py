"""
使用 unittest.TestProgram 运行测试
"""
import sys
import os
import unittest

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath('.'))

if __name__ == "__main__":
    # 设置测试模块
    test_module = "tests.test_operations"
    
    # 创建测试程序
    test_program = unittest.TestProgram(
        module=None,
        argv=[sys.argv[0], test_module, "-v"],
        testLoader=unittest.TestLoader(),
        testRunner=unittest.TextTestRunner(verbosity=2),
        exit=False
    )
    
    # 输出测试结果
    print("\n测试结果:")
    print(f"运行测试数: {test_program.result.testsRun}")
    print(f"失败测试数: {len(test_program.result.failures) if hasattr(test_program.result, 'failures') else 0}")
    print(f"错误测试数: {len(test_program.result.errors) if hasattr(test_program.result, 'errors') else 0}")
    
    # 返回适当的退出码
    sys.exit(not test_program.result.wasSuccessful())
