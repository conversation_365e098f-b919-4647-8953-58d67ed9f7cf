# 开发指南

**最后更新**: 2025年12月19日  
**适用版本**: v1.0+

## 🚀 快速开始

### 环境要求
- **操作系统**: Windows 11 (主要开发环境)
- **Python版本**: 3.8+
- **Node.js版本**: 16+ (用于browser-tools-mcp)
- **浏览器**: Chrome/Chromium (用于Playwright)

### 开发环境设置

#### 1. 克隆项目
```bash
git clone <repository-url>
cd playwright
```

#### 2. 创建虚拟环境
```bash
python -m venv .venv
.\.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac
```

#### 3. 安装依赖
```bash
# 基础依赖
pip install -r requirements.txt

# 开发依赖
pip install -r requirements-dev.txt

# Playwright浏览器
playwright install
```

#### 4. 安装外部集成 (可选)
```bash
# browser-use (需要OpenAI API密钥)
pip install browser-use
pip install "browser-use[memory]"

# browser-tools-mcp
npx @agentdeskai/browser-tools-mcp@latest
npx @agentdeskai/browser-tools-server@latest
```

#### 5. 环境变量配置
创建 `.env` 文件:
```env
# OpenAI API (用于browser-use)
OPENAI_API_KEY=your_openai_api_key_here

# Google Vision API (用于OCR)
GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json

# Azure Computer Vision (备选OCR)
AZURE_COMPUTER_VISION_KEY=your_azure_key
AZURE_COMPUTER_VISION_ENDPOINT=your_azure_endpoint
```

## 📁 项目结构

```
playwright/
├── src/                           # 源代码
│   ├── workflow/                  # 工作流核心
│   │   ├── operations/           # 操作系统
│   │   ├── models.py            # 数据模型
│   │   ├── dsl.py               # DSL解析器
│   │   ├── variables.py         # 变量系统
│   │   ├── engine.py            # 工作流引擎
│   │   └── runner.py            # 工作流运行器
│   ├── browser_use_integration.py    # browser-use集成
│   ├── ai_ocr_integration.py         # AI OCR集成
│   ├── ai_intelligent_interaction.py # AI智能交互
│   └── enhanced_workflow_player.py   # 增强播放器
├── examples/                      # 示例程序
│   ├── ai_integration_demo.py     # AI集成演示
│   ├── browser_use_integration_poc.py # browser-use PoC
│   └── workflow_engine_demo.py    # 工作流引擎演示
├── tests/                         # 测试文件
├── docs/                          # 项目文档
├── requirements.txt               # 依赖列表
└── README.md                      # 项目说明
```

## 🔧 开发工作流

### 代码开发流程
1. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **编写代码**
   - 遵循代码规范
   - 编写单元测试
   - 更新文档

3. **运行测试**
   ```bash
   # 运行所有测试
   python -m pytest tests/
   
   # 运行特定测试
   python -m pytest tests/test_specific.py
   
   # 运行覆盖率测试
   python -m pytest --cov=src tests/
   ```

4. **代码检查**
   ```bash
   # 代码格式化
   black src/ tests/
   
   # 代码检查
   flake8 src/ tests/
   
   # 类型检查
   mypy src/
   ```

5. **提交代码**
   ```bash
   git add .
   git commit -m "feat: add new feature"
   git push origin feature/your-feature-name
   ```

### 测试策略

#### 单元测试
- **位置**: `tests/unit/`
- **覆盖率要求**: >80%
- **命名规范**: `test_*.py`

```python
# 示例单元测试
import pytest
from src.workflow.models import Operation

def test_operation_creation():
    op = Operation(type="click", selector="#button")
    assert op.type == "click"
    assert op.selector == "#button"
```

#### 集成测试
- **位置**: `tests/integration/`
- **覆盖率要求**: >70%
- **测试场景**: 模块间交互

```python
# 示例集成测试
import pytest
from src.enhanced_workflow_player import EnhancedWorkflowPlayer

@pytest.mark.asyncio
async def test_workflow_execution():
    player = EnhancedWorkflowPlayer()
    workflow = {"steps": [{"type": "navigate", "url": "https://example.com"}]}
    result = await player.play(workflow)
    assert result.status == "passed"
```

#### 端到端测试
- **位置**: `tests/e2e/`
- **覆盖率要求**: >60%
- **测试场景**: 完整用户流程

## 📝 代码规范

### Python代码规范
- **格式化工具**: Black
- **代码检查**: Flake8
- **类型注解**: 使用Type Hints
- **文档字符串**: Google风格

```python
def process_workflow(workflow: Dict[str, Any]) -> WorkflowResult:
    """
    处理工作流执行
    
    Args:
        workflow: 工作流定义字典
        
    Returns:
        WorkflowResult: 执行结果
        
    Raises:
        WorkflowError: 工作流执行错误
    """
    pass
```

### 命名规范
- **文件名**: 小写字母+下划线 (`workflow_engine.py`)
- **类名**: 大驼峰 (`WorkflowEngine`)
- **函数名**: 小写字母+下划线 (`execute_workflow`)
- **常量**: 大写字母+下划线 (`MAX_RETRY_COUNT`)

### 注释规范
```python
# 单行注释：解释代码意图
"""
多行注释：
- 模块级别的说明
- 复杂逻辑的解释
"""

# TODO: 待完成的功能
# FIXME: 需要修复的问题
# NOTE: 重要说明
```

## 🧪 调试和测试

### 本地调试
```bash
# 运行AI集成演示
python examples/ai_integration_demo.py

# 运行browser-use集成PoC
python examples/browser_use_integration_poc.py

# 运行工作流引擎演示
python examples/workflow_engine_demo.py
```

### 日志配置
```python
import logging

# 配置日志级别
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 使用日志
logger = logging.getLogger(__name__)
logger.info("执行工作流步骤")
logger.error("执行失败", exc_info=True)
```

### 性能分析
```python
import cProfile
import pstats

# 性能分析
profiler = cProfile.Profile()
profiler.enable()

# 执行代码
your_function()

profiler.disable()
stats = pstats.Stats(profiler)
stats.sort_stats('cumulative')
stats.print_stats(10)
```

## 🔌 扩展开发

### 添加新的操作类型
1. **定义操作模型**
```python
@dataclass
class CustomOperation(Operation):
    type: str = "custom"
    custom_param: str = ""
```

2. **实现操作处理器**
```python
class CustomOperationHandler(OperationHandler):
    async def execute(self, operation: CustomOperation, context: ExecutionContext):
        # 实现自定义逻辑
        pass
```

3. **注册操作类型**
```python
OPERATION_HANDLERS["custom"] = CustomOperationHandler
```

### 添加新的等待条件
```python
class CustomWaitCondition(BaseWaitCondition):
    def __init__(self, custom_param: str):
        self.custom_param = custom_param
    
    async def check(self, page) -> bool:
        # 实现自定义等待逻辑
        return True
```

### 添加新的AI分析器
```python
class CustomAIAnalyzer:
    def analyze_requirement(self, user_input: str) -> AnalysisResult:
        # 实现自定义AI分析逻辑
        pass
```

## 📚 开发资源

### 重要文档
- [项目概述](../01-project-overview/README.md)
- [系统架构](../02-architecture/README.md)
- [API文档](../06-api/README.md)
- [集成指南](../07-integration/README.md)

### 外部依赖文档
- [Playwright文档](https://playwright.dev/python/)
- [browser-use文档](https://github.com/browser-use/browser-use)
- [browser-tools-mcp文档](https://github.com/AgentDeskAI/browser-tools-mcp)

### 开发工具
- **IDE**: VS Code (推荐)
- **插件**: Python, Pylance, Black Formatter
- **调试**: VS Code Debugger
- **版本控制**: Git

## 🚨 常见问题

### Q: 如何解决Playwright安装问题？
```bash
# 重新安装Playwright
pip uninstall playwright
pip install playwright
playwright install
```

### Q: 如何解决browser-use导入错误？
```bash
# 检查是否安装
pip list | grep browser-use

# 重新安装
pip install browser-use --upgrade
```

### Q: 如何解决JavaScript执行错误？
检查脚本语法，确保使用正确的JavaScript语法：
```javascript
// 正确的语法
return document.title;

// 错误的语法 (不要在顶层使用return)
// return { title: document.title };
```

### Q: 如何配置OpenAI API密钥？
```bash
# 方法1: 环境变量
export OPENAI_API_KEY=your_key_here

# 方法2: .env文件
echo "OPENAI_API_KEY=your_key_here" > .env
```

## 📞 获取帮助

- **技术问题**: 查看项目文档或提交Issue
- **功能建议**: 提交Feature Request
- **Bug报告**: 提交Bug Report
- **社区讨论**: 参与项目讨论

---

> 🚀 **开始开发**: 按照上述步骤设置环境后，可以从运行示例程序开始熟悉项目
