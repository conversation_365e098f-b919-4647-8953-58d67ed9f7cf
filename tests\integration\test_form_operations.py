"""
表单操作测试
"""
import os
import sys
from pathlib import Path
import pytest
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page, expect

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入测试辅助函数
from tests.test_utils import get_test_data_path, save_debug_info, get_form_data

# 测试页面路径
TEST_PAGE = "form_test_page.html"

class TestFormOperations:
    """测试表单操作"""
    
    @pytest.fixture(scope="class")
    def browser(self):
        """启动Playwright浏览器"""
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)  # 设置为非无头模式以便观察
            yield browser
            browser.close()

    @pytest.fixture(scope="class")
    def context(self, browser):
        """创建浏览器上下文"""
        context = browser.new_context()
        yield context
        context.close()

    @pytest.fixture(scope="class")
    def page(self, context):
        """创建测试页面"""
        print("\n=== 设置测试页面 ===")
        
        page = context.new_page()
        
        # 设置超时
        page.set_default_timeout(10000)  # 10秒超时
        
        # 启用控制台日志
        page.on("console", lambda msg: print(f"浏览器控制台: {msg.text}"))
        
        # 页面错误处理
        page.on("pageerror", lambda err: print(f"页面错误: {err}"))
        
        # 导航到测试页面
        test_url = get_test_data_path(TEST_PAGE)
        print(f"正在导航到: {test_url}")
        
        try:
            # 先访问一个空白页面
            page.goto("about:blank")
            
            # 然后导航到测试页面
            response = page.goto(test_url, wait_until="domcontentloaded")
            if not response.ok:
                print(f"页面加载失败: {response.status} {response.status_text}")
            
            # 等待页面加载完成
            print("等待页面加载完成...")
            page.wait_for_load_state("networkidle")
            
            # 等待表单元素可见
            print("等待表单元素...")
            page.wait_for_selector("#test-form", state="visible", timeout=5000)
            
            print("=== 测试页面设置完成 ===")
            
            yield page
            
        except Exception as e:
            print(f"页面设置失败: {str(e)}")
            save_debug_info(page, "form_test_setup_error")
            raise

    def test_form_submission(self, page: Page):
        """测试表单提交"""
        print("\n=== 开始测试表单提交 ===")
        
        # 1. 验证页面标题
        title = page.title()
        print(f"页面标题: {title}")
        assert "Form Test Page" in title, f"页面标题不匹配: {title}"
        
        # 2. 填写表单
        print("填写表单...")
        page.fill("#username", "testuser")
        page.fill("#email", "<EMAIL>")
        page.fill("#password", "password123")
        page.select_option("#country", "CN")
        page.check("#male")
        page.check("#sports")
        
        # 3. 提交表单
        print("提交表单...")
        with page.expect_navigation():
            page.click("#submit-btn")
        
        # 4. 验证提交结果
        print("验证提交结果...")
        success_message = page.locator("#success-message")
        expect(success_message).to_be_visible()
        expect(success_message).to_contain_text("提交成功")
        
        # 5. 验证表单数据
        form_data = get_form_data(page)
        print(f"表单数据: {form_data}")
        assert form_data.get("username") == "testuser"
        assert form_data.get("email") == "<EMAIL>"
        assert form_data.get("country") == "CN"
        assert form_data.get("gender") == "male"
        assert "sports" in form_data.get("hobbies", [])
        
        print("=== 表单提交测试通过 ===")
    
    def test_form_validation(self, page: Page):
        """测试表单验证"""
        print("\n=== 开始测试表单验证 ===")
        
        # 1. 重置表单
        print("重置表单...")
        page.click("#reset-btn")
        
        # 2. 尝试提交空表单
        print("尝试提交空表单...")
        page.click("#submit-btn")
        
        # 3. 验证错误消息
        print("验证错误消息...")
        error_message = page.locator("#error-message")
        expect(error_message).to_be_visible()
        expect(error_message).to_contain_text("请填写必填字段")
        
        print("=== 表单验证测试通过 ===")
