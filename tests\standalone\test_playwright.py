"""
测试 Playwright 基本功能
"""
import asyncio
from playwright.async_api import async_playwright

async def main():
    """主函数"""
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        
        # 创建新页面
        page = await browser.new_page()
        
        # 导航到示例网站
        await page.goto('https://example.com')
        
        # 获取页面标题
        title = await page.title()
        print(f"页面标题: {title}")
        
        # 截图
        await page.screenshot(path='example.png')
        print("已保存截图: example.png")
        
        # 关闭浏览器
        await browser.close()

if __name__ == "__main__":
    asyncio.run(main())
