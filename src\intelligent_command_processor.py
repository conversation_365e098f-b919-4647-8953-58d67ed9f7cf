"""
智能命令处理器

识别用户命令，无法识别时通过AI协助处理
"""
import asyncio
import logging
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ai_llm_manager import get_llm_manager, LLMProvider

logger = logging.getLogger(__name__)


class CommandType(Enum):
    """命令类型"""
    NAVIGATION = "navigation"        # 导航命令
    ANALYSIS = "analysis"           # 分析命令
    OPERATION = "operation"         # 操作命令
    QUERY = "query"                # 查询命令
    SYSTEM = "system"              # 系统命令
    HELP = "help"                  # 帮助命令
    UNKNOWN = "unknown"            # 未知命令


@dataclass
class CommandResult:
    """命令结果"""
    success: bool
    command_type: CommandType
    action: str
    parameters: Dict[str, Any]
    response: str
    confidence: float
    processing_method: str  # rule_based, ai_assisted, manual
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "command_type": self.command_type.value,
            "action": self.action,
            "parameters": self.parameters,
            "response": self.response,
            "confidence": self.confidence,
            "processing_method": self.processing_method
        }


class IntelligentCommandProcessor:
    """智能命令处理器"""
    
    def __init__(self):
        self.llm_manager = get_llm_manager()
        self.command_patterns = self._init_command_patterns()
        self.command_history = []
    
    def _init_command_patterns(self) -> Dict[str, Dict]:
        """初始化命令模式"""
        return {
            # 导航命令
            "navigation": {
                "patterns": [
                    r"(导航|跳转|进入|打开|访问)\s*(到|至)?\s*(.+)",
                    r"我要(去|到|进入|查看|访问)\s*(.+)",
                    r"(找|查找|寻找)\s*(.+)",
                    r"(显示|展示|打开)\s*(.+)",
                ],
                "examples": ["导航到用户管理", "我要查看订单列表", "找到设置页面"]
            },
            
            # 分析命令
            "analysis": {
                "patterns": [
                    r"(分析|解析|检查|扫描)\s*(当前|这个|页面|网页)?\s*(页面|网页|界面)?",
                    r"(获取|提取|收集)\s*(页面|网页)?\s*(信息|数据|链接|操作)",
                    r"(页面|网页|界面)\s*(分析|解析|信息)",
                ],
                "examples": ["分析当前页面", "获取页面信息", "页面分析"]
            },
            
            # 操作命令
            "operation": {
                "patterns": [
                    r"(点击|单击|双击)\s*(.+)",
                    r"(填写|输入|录入)\s*(.+)",
                    r"(提交|保存|确认)\s*(.+)?",
                    r"(删除|移除|清除)\s*(.+)",
                    r"(添加|新增|创建)\s*(.+)",
                    r"(编辑|修改|更新)\s*(.+)",
                ],
                "examples": ["点击保存按钮", "填写用户信息", "添加新用户"]
            },
            
            # 查询命令
            "query": {
                "patterns": [
                    r"(查询|搜索|检索)\s*(.+)",
                    r"(显示|列出|展示)\s*(所有|全部)?\s*(.+)",
                    r"(统计|计算|汇总)\s*(.+)",
                    r"(状态|信息|详情)\s*(是什么|如何)",
                ],
                "examples": ["查询用户信息", "显示所有订单", "统计数据"]
            },
            
            # 系统命令
            "system": {
                "patterns": [
                    r"(系统|状态|版本|配置)\s*(信息|状态|详情)?",
                    r"(记录|保存|导出)\s*(操作|日志|记录)",
                    r"(清空|清除|重置)\s*(记录|缓存|数据)",
                    r"(退出|关闭|结束)",
                ],
                "examples": ["系统状态", "记录操作", "清空记录", "退出"]
            },
            
            # 帮助命令
            "help": {
                "patterns": [
                    r"(帮助|help|使用说明|说明|指南)",
                    r"(如何|怎么|怎样)\s*(.+)",
                    r"(什么是|介绍)\s*(.+)",
                    r"(命令|功能)\s*(列表|清单|说明)",
                ],
                "examples": ["帮助", "如何使用", "命令列表"]
            }
        }
    
    async def process_command(self, user_input: str) -> CommandResult:
        """处理用户命令"""
        try:
            logger.info(f"处理用户命令: {user_input}")
            
            # 预处理输入
            cleaned_input = self._preprocess_input(user_input)
            
            # 1. 尝试基于规则的识别
            rule_result = self._rule_based_recognition(cleaned_input)
            
            if rule_result.confidence >= 0.8:
                logger.info(f"规则识别成功: {rule_result.command_type.value}")
                rule_result.processing_method = "rule_based"
                self._add_to_history(user_input, rule_result)
                return rule_result
            
            # 2. 尝试AI协助识别
            ai_result = await self._ai_assisted_recognition(cleaned_input)
            
            if ai_result.confidence >= 0.6:
                logger.info(f"AI识别成功: {ai_result.command_type.value}")
                ai_result.processing_method = "ai_assisted"
                self._add_to_history(user_input, ai_result)
                return ai_result
            
            # 3. 返回未知命令，提供建议
            suggestion = await self._generate_suggestion(cleaned_input)
            
            unknown_result = CommandResult(
                success=False,
                command_type=CommandType.UNKNOWN,
                action="unknown",
                parameters={"original_input": user_input},
                response=f"抱歉，我无法理解您的命令。{suggestion}",
                confidence=0.0,
                processing_method="manual"
            )
            
            self._add_to_history(user_input, unknown_result)
            return unknown_result
            
        except Exception as e:
            logger.error(f"处理命令失败: {e}")
            return CommandResult(
                success=False,
                command_type=CommandType.UNKNOWN,
                action="error",
                parameters={"error": str(e)},
                response=f"处理命令时发生错误: {e}",
                confidence=0.0,
                processing_method="error"
            )
    
    def _preprocess_input(self, user_input: str) -> str:
        """预处理用户输入"""
        # 去除多余空格
        cleaned = re.sub(r'\s+', ' ', user_input.strip())
        
        # 转换常见的同义词
        synonyms = {
            "页面": ["网页", "界面", "页"],
            "查看": ["看", "浏览", "访问"],
            "查找": ["找", "搜索", "寻找"],
            "点击": ["单击", "按", "选择"],
        }
        
        for standard, variants in synonyms.items():
            for variant in variants:
                cleaned = cleaned.replace(variant, standard)
        
        return cleaned
    
    def _rule_based_recognition(self, user_input: str) -> CommandResult:
        """基于规则的命令识别"""
        best_match = None
        best_confidence = 0.0
        best_command_type = CommandType.UNKNOWN
        
        for command_type, config in self.command_patterns.items():
            for pattern in config["patterns"]:
                match = re.search(pattern, user_input, re.IGNORECASE)
                if match:
                    # 计算匹配置信度
                    confidence = self._calculate_pattern_confidence(match, user_input)
                    
                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_match = match
                        best_command_type = CommandType(command_type)
        
        if best_match and best_confidence > 0.5:
            # 提取参数
            action, parameters = self._extract_parameters(best_match, best_command_type)
            
            # 生成响应
            response = self._generate_rule_response(best_command_type, action, parameters)
            
            return CommandResult(
                success=True,
                command_type=best_command_type,
                action=action,
                parameters=parameters,
                response=response,
                confidence=best_confidence,
                processing_method="rule_based"
            )
        
        return CommandResult(
            success=False,
            command_type=CommandType.UNKNOWN,
            action="unknown",
            parameters={},
            response="",
            confidence=0.0,
            processing_method="rule_based"
        )
    
    async def _ai_assisted_recognition(self, user_input: str) -> CommandResult:
        """AI协助命令识别"""
        try:
            # 构建AI提示
            prompt = f"""
请分析用户的命令意图：

用户输入: {user_input}

请识别命令类型和参数：

命令类型选项：
- navigation: 导航/跳转命令
- analysis: 分析/检查命令  
- operation: 操作/执行命令
- query: 查询/搜索命令
- system: 系统/管理命令
- help: 帮助/说明命令

返回格式：
命令类型|动作|参数JSON|置信度|响应

例如：
navigation|navigate_to|{{"target": "用户管理"}}|0.9|正在导航到用户管理页面

请分析：
"""
            
            response = await self.llm_manager.generate(prompt, provider=LLMProvider.GEMINI)
            
            # 解析AI响应
            return self._parse_ai_response(response.content, user_input)
            
        except Exception as e:
            logger.error(f"AI协助识别失败: {e}")
            return CommandResult(
                success=False,
                command_type=CommandType.UNKNOWN,
                action="ai_error",
                parameters={"error": str(e)},
                response="AI分析失败",
                confidence=0.0,
                processing_method="ai_assisted"
            )
    
    def _parse_ai_response(self, ai_response: str, original_input: str) -> CommandResult:
        """解析AI响应"""
        try:
            parts = ai_response.strip().split('|')
            
            if len(parts) >= 5:
                command_type_str = parts[0].strip()
                action = parts[1].strip()
                parameters_str = parts[2].strip()
                confidence_str = parts[3].strip()
                response = parts[4].strip()
                
                # 解析命令类型
                try:
                    command_type = CommandType(command_type_str)
                except ValueError:
                    command_type = CommandType.UNKNOWN
                
                # 解析参数
                try:
                    import json
                    parameters = json.loads(parameters_str)
                except:
                    parameters = {"target": original_input}
                
                # 解析置信度
                try:
                    confidence = float(confidence_str)
                    confidence = max(0.0, min(1.0, confidence))
                except:
                    confidence = 0.5
                
                return CommandResult(
                    success=True,
                    command_type=command_type,
                    action=action,
                    parameters=parameters,
                    response=response,
                    confidence=confidence,
                    processing_method="ai_assisted"
                )
            
        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
        
        # 返回默认结果
        return CommandResult(
            success=False,
            command_type=CommandType.UNKNOWN,
            action="parse_error",
            parameters={"original_input": original_input},
            response="AI响应解析失败",
            confidence=0.0,
            processing_method="ai_assisted"
        )
    
    async def _generate_suggestion(self, user_input: str) -> str:
        """生成建议"""
        try:
            # 基于历史命令生成建议
            if self.command_history:
                recent_commands = [cmd["input"] for cmd in self.command_history[-5:]]
                suggestion = f"\n\n您可以尝试：\n"
                
                # 添加常用命令建议
                common_commands = [
                    "分析当前页面",
                    "我要查看用户管理",
                    "导航到设置页面",
                    "帮助"
                ]
                
                for cmd in common_commands:
                    suggestion += f"• {cmd}\n"
                
                return suggestion
            
            return "\n\n请尝试使用更明确的命令，或输入'帮助'查看使用说明。"
            
        except Exception as e:
            logger.error(f"生成建议失败: {e}")
            return "\n\n请输入'帮助'查看使用说明。"
    
    def _calculate_pattern_confidence(self, match, user_input: str) -> float:
        """计算模式匹配置信度"""
        # 基础置信度
        base_confidence = 0.7
        
        # 匹配长度占比
        match_length = len(match.group(0))
        input_length = len(user_input)
        length_ratio = match_length / input_length
        
        # 调整置信度
        confidence = base_confidence + (length_ratio * 0.3)
        
        return min(confidence, 1.0)
    
    def _extract_parameters(self, match, command_type: CommandType) -> Tuple[str, Dict]:
        """提取命令参数"""
        groups = match.groups()
        
        if command_type == CommandType.NAVIGATION:
            target = groups[-1] if groups else ""
            return "navigate_to", {"target": target.strip()}
        
        elif command_type == CommandType.ANALYSIS:
            return "analyze_page", {"scope": "current_page"}
        
        elif command_type == CommandType.OPERATION:
            operation = groups[0] if groups else ""
            target = groups[1] if len(groups) > 1 else ""
            return "perform_operation", {"operation": operation, "target": target}
        
        elif command_type == CommandType.QUERY:
            query = groups[-1] if groups else ""
            return "query_data", {"query": query.strip()}
        
        elif command_type == CommandType.SYSTEM:
            action = groups[0] if groups else ""
            return "system_action", {"action": action.strip()}
        
        elif command_type == CommandType.HELP:
            return "show_help", {"topic": "general"}
        
        return "unknown", {}
    
    def _generate_rule_response(self, command_type: CommandType, action: str, parameters: Dict) -> str:
        """生成规则响应"""
        if command_type == CommandType.NAVIGATION:
            target = parameters.get("target", "")
            return f"🎯 正在导航到: {target}"
        
        elif command_type == CommandType.ANALYSIS:
            return "🔍 正在分析当前页面..."
        
        elif command_type == CommandType.OPERATION:
            operation = parameters.get("operation", "")
            target = parameters.get("target", "")
            return f"⚡ 正在执行操作: {operation} {target}"
        
        elif command_type == CommandType.QUERY:
            query = parameters.get("query", "")
            return f"🔎 正在查询: {query}"
        
        elif command_type == CommandType.SYSTEM:
            action = parameters.get("action", "")
            return f"⚙️ 正在执行系统操作: {action}"
        
        elif command_type == CommandType.HELP:
            return "📖 正在显示帮助信息..."
        
        return "✅ 命令已识别，正在处理..."
    
    def _add_to_history(self, user_input: str, result: CommandResult):
        """添加到历史记录"""
        self.command_history.append({
            "timestamp": datetime.now().isoformat(),
            "input": user_input,
            "result": result.to_dict()
        })
        
        # 保持历史记录在合理范围内
        if len(self.command_history) > 100:
            self.command_history = self.command_history[-50:]
    
    def get_command_history(self) -> List[Dict]:
        """获取命令历史"""
        return self.command_history.copy()
    
    def get_supported_commands(self) -> Dict[str, List[str]]:
        """获取支持的命令"""
        commands = {}
        for command_type, config in self.command_patterns.items():
            commands[command_type] = config["examples"]
        return commands


# 全局实例
global_command_processor = IntelligentCommandProcessor()


def get_intelligent_command_processor() -> IntelligentCommandProcessor:
    """获取智能命令处理器实例"""
    return global_command_processor
