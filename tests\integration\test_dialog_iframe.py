"""
测试对话框和iframe操作
"""
import os
import sys
import logging
import time
from pathlib import Path
from typing import Generator, Optional

import pytest
from playwright.sync_api import Page, expect, Dialog, BrowserContext

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入测试辅助函数
from tests.test_utils import get_test_data_path, save_debug_info

class TestDialogAndIframe:
    """测试对话框和iframe操作"""
    
    def setup_dialog_page(self, page: Page) -> None:
        """设置对话框测试页面"""
        try:
            # 获取测试页面路径
            test_file = "dialog_test_page.html"
            test_url = get_test_data_path(test_file)
            
            # 检查测试文件是否存在
            if not os.path.exists(test_url.replace('file://', '')):
                raise FileNotFoundError(f"测试文件 {test_file} 不存在于 {test_url}")
            
            logger.info(f"正在导航到对话框测试页面: {test_url}")
            logger.debug(f"当前工作目录: {os.getcwd()}")
            logger.debug(f"测试页面是否存在: {os.path.exists(test_url.replace('file://', ''))}")
            
            # 设置页面加载超时
            page.set_default_timeout(30000)  # 30秒超时
            
            # 导航到页面并等待加载完成
            logger.debug("开始导航到测试页面...")
            response = page.goto(f"file://{test_url}", wait_until="domcontentloaded")
            logger.debug(f"页面导航完成，状态码: {response.status if response else 'N/A'}")
            
            # 等待页面完全加载
            page.wait_for_load_state("networkidle")
            
            # 检查页面标题
            title = page.title()
            logger.debug(f"页面标题: {title}")
            
            # 等待主要元素加载完成
            logger.debug("等待主要元素加载...")
            try:
                page.wait_for_selector("#alertBtn", state="visible", timeout=10000)
                logger.debug("alert按钮已加载")
                
                # 检查按钮是否可点击
                alert_btn = page.locator("#alertBtn")
                is_visible = alert_btn.is_visible()
                is_enabled = alert_btn.is_enabled()
                logger.debug(f"Alert按钮状态 - 可见: {is_visible}, 可用: {is_enabled}")
                
                if not (is_visible and is_enabled):
                    raise Exception("Alert按钮不可用")
                
            except Exception as e:
                # 保存页面截图和HTML用于调试
                save_debug_info(page, "dialog_page_element_load_failed")
                raise Exception(f"页面元素加载失败: {str(e)}")
            
            logger.info("对话框测试页面加载完成")
            return True
            
        except Exception as e:
            error_msg = f"对话框测试页面加载失败: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息:")
            
            # 保存调试信息
            try:
                save_debug_info(page, "dialog_page_load_failed")
                logger.debug(f"当前URL: {page.url}")
                logger.debug(f"页面标题: {page.title()}")
                logger.debug(f"页面内容: {page.content()[:500]}...")
            except Exception as debug_e:
                logger.error(f"保存调试信息时出错: {str(debug_e)}")
                
            raise Exception(error_msg)
    
    def test_alert_dialog(self, page: Page):
        """
        测试alert对话框功能
        
        测试步骤：
        1. 加载对话框测试页面
        2. 清空结果区域
        3. 注册对话框处理函数
        4. 点击触发alert的按钮
        5. 验证对话框内容和结果
        """
        logger.info("开始测试alert对话框")
        
        try:
            # 1. 加载对话框测试页面
            if not self.setup_dialog_page(page):
                raise Exception("页面加载失败")
                
            # 2. 清空结果区域
            logger.debug("清空结果区域...")
            page.evaluate("""() => {
                const resultEl = document.getElementById('result');
                if (resultEl) {
                    resultEl.innerHTML = '';
                    console.log('结果区域已清空');
                } else {
                    console.error('未找到结果区域元素');
                }
            }""")
            
            # 3. 准备对话框处理
            dialog_messages = []
            
            def handle_dialog(dialog: Dialog):
                try:
                    msg = f"捕获到对话框: type={dialog.type}, message={dialog.message}"
                    logger.info(msg)
                    dialog_messages.append({
                        'type': dialog.type,
                        'message': dialog.message,
                        'timestamp': time.time()
                    })
                    logger.info("接受对话框...")
                    dialog.accept()
                except Exception as e:
                    logger.error(f"处理对话框时出错: {str(e)}")
                    raise
            
            # 注册对话框处理函数
            logger.debug("注册对话框处理函数...")
            page.once("dialog", handle_dialog)
            
            # 4. 点击触发alert的按钮
            logger.debug("点击Alert按钮...")
            alert_btn = page.locator("#alertBtn")
            
            # 确保按钮可见且可点击
            alert_btn.scroll_into_view_if_needed()
            page.wait_for_timeout(500)  # 等待滚动完成
            
            # 点击按钮并等待对话框处理
            with page.expect_event("dialog") as dialog_info:
                alert_btn.click(delay=100)  # 添加延迟模拟真实点击
            
            # 处理对话框
            dialog = dialog_info.value
            dialog.accept()
            
            # 5. 等待结果更新
            logger.debug("等待结果更新...")
            result_locator = page.locator("#result")
            result_locator.wait_for(state="visible", timeout=10000)
            
            # 获取结果文本
            result_text = result_locator.text_content() or ""
            logger.info(f"测试结果: {result_text}")
            
            # 验证结果
            if not result_text:
                # 如果结果为空，尝试获取控制台日志
                console_msgs = page.evaluate("window.consoleMessages || []")
                logger.warning(f"结果为空，控制台日志: {console_msgs}")
            
            # 检查断言
            assert "Alert was shown and closed" in result_text, \
                f"结果区域未显示预期文本，实际内容: {result_text}"
            
            # 验证对话框消息
            assert len(dialog_messages) > 0, "未捕获到对话框"
            assert any("This is an alert message!" in msg.get('message', '') 
                     for msg in dialog_messages), \
                f"未找到预期的对话框消息，实际消息: {dialog_messages}"
            
            logger.info("alert对话框测试通过")
            return True
            
        except Exception as e:
            error_msg = f"alert对话框测试失败: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息:")
            
            # 保存调试信息
            try:
                save_debug_info(page, "test_alert_dialog_failed")
                
                # 获取更多调试信息
                logger.debug(f"当前URL: {page.url}")
                logger.debug(f"页面标题: {page.title()}")
                
                # 检查按钮状态
                try:
                    alert_btn = page.locator("#alertBtn")
                    logger.debug(f"Alert按钮状态: 存在={alert_btn.count()>0}, "
                                 f"可见={alert_btn.is_visible() if alert_btn.count()>0 else False}, "
                                 f"可用={alert_btn.is_enabled() if alert_btn.count()>0 else False}")
                except Exception as btn_err:
                    logger.error(f"获取按钮状态失败: {str(btn_err)}")
                
                # 获取结果区域内容
                try:
                    result_content = page.locator("#result").text_content() or ""
                    logger.debug(f"结果区域内容: {result_content}")
                except Exception as res_err:
                    logger.error(f"获取结果区域内容失败: {str(res_err)}")
                
                # 获取控制台日志
                try:
                    console_msgs = page.evaluate("window.consoleMessages || []")
                    logger.debug(f"控制台日志: {console_msgs}")
                except Exception as console_err:
                    logger.error(f"获取控制台日志失败: {str(console_err)}")
                
            except Exception as debug_err:
                logger.error(f"保存调试信息时出错: {str(debug_err)}")
            
            # 重新抛出异常，使测试失败
            raise Exception(error_msg) from e
    
    def test_confirm_dialog(self, page: Page):
        """
        测试确认对话框功能
        
        测试步骤：
        1. 加载对话框测试页面
        2. 清空结果区域
        3. 注册对话框处理函数
        4. 点击触发确认对话框的按钮
        5. 验证对话框内容和结果
        """
        logger.info("开始测试确认对话框")
        
        try:
            # 1. 加载对话框测试页面
            if not self.setup_dialog_page(page):
                raise Exception("页面加载失败")
                
            # 2. 清空结果区域
            logger.debug("清空结果区域...")
            page.evaluate("""() => {
                const resultEl = document.getElementById('result');
                if (resultEl) {
                    resultEl.innerHTML = '';
                    console.log('结果区域已清空');
                } else {
                    console.error('未找到结果区域元素');
                }
            }""")
            
            # 3. 准备对话框处理
            dialog_messages = []
            
            def handle_dialog(dialog: Dialog):
                try:
                    msg = f"捕获到对话框: type={dialog.type}, message={dialog.message}"
                    logger.info(msg)
                    dialog_messages.append({
                        'type': dialog.type,
                        'message': dialog.message,
                        'timestamp': time.time()
                    })
                    logger.info("接受确认对话框...")
                    dialog.accept()  # 点击确认
                except Exception as e:
                    logger.error(f"处理确认对话框时出错: {str(e)}")
                    raise
            
            # 注册对话框处理函数
            logger.debug("注册确认对话框处理函数...")
            page.once("dialog", handle_dialog)
            
            # 4. 点击触发确认对话框的按钮
            logger.debug("点击Confirm按钮...")
            confirm_btn = page.locator("#confirmBtn")
            
            # 确保按钮可见且可点击
            confirm_btn.scroll_into_view_if_needed()
            page.wait_for_timeout(500)  # 等待滚动完成
            
            # 点击按钮并等待对话框处理
            with page.expect_event("dialog") as dialog_info:
                confirm_btn.click(delay=100)  # 添加延迟模拟真实点击
            
            # 处理对话框
            dialog = dialog_info.value
            dialog.accept()  # 确认
            
            # 5. 等待结果更新
            logger.debug("等待结果更新...")
            result_locator = page.locator("#result")
            result_locator.wait_for(state="visible", timeout=10000)
            
            # 获取结果文本
            result_text = result_locator.text_content() or ""
            logger.info(f"测试结果: {result_text}")
            
            # 验证结果
            if not result_text:
                # 如果结果为空，尝试获取控制台日志
                console_msgs = page.evaluate("window.consoleMessages || []")
                logger.warning(f"结果为空，控制台日志: {console_msgs}")
            
            # 检查断言
            assert "Confirm dialog result: OK" in result_text, \
                f"确认对话框结果不正确，实际内容: {result_text}"
            
            # 验证对话框消息
            assert len(dialog_messages) > 0, "未捕获到确认对话框"
            assert any("Are you sure you want to proceed?" in msg.get('message', '') 
                     for msg in dialog_messages), \
                f"未找到预期的确认对话框消息，实际消息: {dialog_messages}"
            
            logger.info("确认对话框测试通过")
            return True
            
        except Exception as e:
            error_msg = f"确认对话框测试失败: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息:")
            
            # 保存调试信息
            try:
                save_debug_info(page, "test_confirm_dialog_failed")
                
                # 获取更多调试信息
                logger.debug(f"当前URL: {page.url}")
                logger.debug(f"页面标题: {page.title()}")
                
                # 检查按钮状态
                try:
                    confirm_btn = page.locator("#confirmBtn")
                    logger.debug(f"Confirm按钮状态: 存在={confirm_btn.count()>0}, "
                                 f"可见={confirm_btn.is_visible() if confirm_btn.count()>0 else False}, "
                                 f"可用={confirm_btn.is_enabled() if confirm_btn.count()>0 else False}")
                except Exception as btn_err:
                    logger.error(f"获取按钮状态失败: {str(btn_err)}")
                
                # 获取结果区域内容
                try:
                    result_content = page.locator("#result").text_content() or ""
                    logger.debug(f"结果区域内容: {result_content}")
                except Exception as res_err:
                    logger.error(f"获取结果区域内容失败: {str(res_err)}")
                
                # 获取控制台日志
                try:
                    console_msgs = page.evaluate("window.consoleMessages || []")
                    logger.debug(f"控制台日志: {console_msgs}")
                except Exception as console_err:
                    logger.error(f"获取控制台日志失败: {str(console_err)}")
                
            except Exception as debug_err:
                logger.error(f"保存调试信息时出错: {str(debug_err)}")
            
            # 重新抛出异常，使测试失败
            raise Exception(error_msg) from e
    
    def test_custom_dialog(self, page: Page):
        """测试自定义对话框"""
        logger.info("开始测试自定义对话框")
        
        try:
            self.setup_dialog_page(page)
            
            # 清空结果区域
            page.evaluate("""() => {
                document.getElementById('result').innerHTML = '';
            }""")
            
            # 监听对话框
            dialog_messages = []
            
            def handle_dialog(dialog: Dialog):
                logger.info(f"捕获到确认对话框: {dialog.message}")
                dialog_messages.append(dialog.message)
                dialog.accept()  # 点击确定
            
            page.once("dialog", handle_dialog)
            
            # 点击触发confirm的按钮
            page.click("#confirmBtn")
            
            # 等待结果更新
            page.wait_for_selector("#result div", state="visible")
            
            # 验证结果
            result_text = page.text_content("#result")
            logger.info(f"测试结果: {result_text}")
            
            assert "Confirm dialog result: OK" in result_text, "结果区域未显示预期文本"
            assert len(dialog_messages) > 0, "未捕获到确认对话框"
            assert "Are you sure you want to proceed?" in dialog_messages[0], "确认对话框消息不匹配"
            
            logger.info("确认对话框测试通过")
            return True
            
        except Exception as e:
            logger.error(f"确认对话框测试失败: {str(e)}")
            save_debug_info(page, "test_confirm_dialog_failed")
            raise
    
    def test_prompt_dialog(self, page: Page):
        """
        测试提示对话框功能
        
        测试步骤：
        1. 加载对话框测试页面
        2. 清空结果区域
        3. 准备测试输入数据
        4. 注册对话框处理函数
        5. 点击触发提示对话框的按钮
        6. 验证对话框内容和结果
        """
        logger.info("开始测试提示对话框")
        
        try:
            # 1. 加载对话框测试页面
            if not self.setup_dialog_page(page):
                raise Exception("页面加载失败")
                
            # 2. 清空结果区域
            logger.debug("清空结果区域...")
            page.evaluate("""() => {
                const resultEl = document.getElementById('result');
                if (resultEl) {
                    resultEl.innerHTML = '';
                    console.log('结果区域已清空');
                } else {
                    console.error('未找到结果区域元素');
                }
            }""")
            
            # 3. 准备测试输入数据
            test_input = "Test User"
            logger.debug(f"测试输入: {test_input}")
            
            # 4. 准备对话框处理
            dialog_messages = []
            
            def handle_dialog(dialog: Dialog):
                try:
                    msg = f"捕获到对话框: type={dialog.type}, message={dialog.message}"
                    logger.info(msg)
                    dialog_messages.append({
                        'type': dialog.type,
                        'message': dialog.message,
                        'timestamp': time.time()
                    })
                    logger.info(f"在提示对话框中输入: {test_input}")
                    dialog.accept(test_input)  # 输入文本并确认
                except Exception as e:
                    logger.error(f"处理提示对话框时出错: {str(e)}")
                    raise
            
            # 注册对话框处理函数
            logger.debug("注册提示对话框处理函数...")
            
            # 5. 点击触发提示对话框的按钮
            logger.debug("点击Prompt按钮...")
            prompt_btn = page.locator("#promptBtn")
            
            # 确保按钮可见且可点击
            prompt_btn.scroll_into_view_if_needed()
            page.wait_for_timeout(500)  # 等待滚动完成
            
            # 点击按钮并等待对话框处理
            with page.expect_event("dialog") as dialog_info:
                prompt_btn.click(delay=100)  # 添加延迟模拟真实点击
            
            # 处理对话框
            dialog = dialog_info.value
            dialog.accept(test_input)  # 输入文本并确认
            
            # 6. 等待结果更新
            logger.debug("等待结果更新...")
            result_locator = page.locator("#result")
            result_locator.wait_for(state="visible", timeout=10000)
            
            # 获取结果文本
            result_text = result_locator.text_content() or ""
            logger.info(f"测试结果: {result_text}")
            
            # 验证结果
            if not result_text:
                # 如果结果为空，尝试获取控制台日志
                console_msgs = page.evaluate("window.consoleMessages || []")
                logger.warning(f"结果为空，控制台日志: {console_msgs}")
            
            # 检查断言
            expected_text = f"You entered: {test_input}"
            assert expected_text in result_text, \
                f"结果区域未显示预期文本，期望包含: '{expected_text}'，实际内容: '{result_text}'"
            
            # 验证对话框消息
            assert len(dialog_messages) > 0, "未捕获到提示对话框"
            assert any("Please enter your name:" in msg.get('message', '') 
                     for msg in dialog_messages), \
                f"未找到预期的提示对话框消息，实际消息: {dialog_messages}"
            
            logger.info("提示对话框测试通过")
            return True
            
        except Exception as e:
            error_msg = f"提示对话框测试失败: {str(e)}"
            logger.error(error_msg)
            logger.exception("详细错误信息:")
            
            # 保存调试信息
            try:
                save_debug_info(page, "test_prompt_dialog_failed")
                
                # 获取更多调试信息
                logger.debug(f"当前URL: {page.url}")
                logger.debug(f"页面标题: {page.title()}")
                
                # 检查按钮状态
                try:
                    prompt_btn = page.locator("#promptBtn")
                    logger.debug(f"Prompt按钮状态: 存在={prompt_btn.count()>0}, "
                                 f"可见={prompt_btn.is_visible() if prompt_btn.count()>0 else False}, "
                                 f"可用={prompt_btn.is_enabled() if prompt_btn.count()>0 else False}")
                except Exception as btn_err:
                    logger.error(f"获取按钮状态失败: {str(btn_err)}")
                
                # 获取结果区域内容
                try:
                    result_content = page.locator("#result").text_content() or ""
                    logger.debug(f"结果区域内容: {result_content}")
                except Exception as res_err:
                    logger.error(f"获取结果区域内容失败: {str(res_err)}")
                
                # 获取控制台日志
                try:
                    console_msgs = page.evaluate("window.consoleMessages || []")
                    logger.debug(f"控制台日志: {console_msgs}")
                except Exception as console_err:
                    logger.error(f"获取控制台日志失败: {str(console_err)}")
                
            except Exception as debug_err:
                logger.error(f"保存调试信息时出错: {str(debug_err)}")
            
            # 重新抛出异常，使测试失败
            raise Exception(error_msg) from e
    
    def test_iframe_interaction(self, page: Page):
        """测试iframe交互"""
        logger.info("开始测试iframe交互")
        
        try:
            # 导航到包含iframe的测试页面
            test_url = get_test_data_path("iframe_test_page.html")
            logger.info(f"正在导航到: {test_url}")
            
            # 设置页面加载超时
            page.set_default_timeout(10000)  # 10秒超时
            
            # 导航到页面并等待加载完成
            page.goto(test_url, wait_until="domcontentloaded")
            
            # 等待iframe加载
            page.wait_for_selector("iframe", state="attached")
            page.wait_for_timeout(1000)  # 等待iframe内容加载
            
            # 定位iframe
            frame = page.frame_locator("iframe").first
            
            # 在iframe中操作元素
            frame.locator("#username").fill("testuser")
            frame.locator("#password").fill("password123")
            frame.locator("button[type='submit']").click()
            
            # 验证iframe中的操作结果
            success_msg = frame.locator(".success-message")
            expect(success_msg).to_be_visible()
            
            logger.info("iframe交互测试通过")
            return True
            
        except Exception as e:
            logger.error(f"iframe交互测试失败: {str(e)}")
            save_debug_info(page, "test_iframe_interaction_failed")
            raise
    
    def test_nested_iframes(self, page: Page):
        """测试嵌套iframe"""
        logger.info("开始测试嵌套iframe")
        
        try:
            # 导航到包含嵌套iframe的测试页面
            test_url = get_test_data_path("nested_iframes_test_page.html")
            logger.info(f"正在导航到: {test_url}")
            
            # 设置页面加载超时
            page.set_default_timeout(15000)  # 15秒超时（嵌套iframe需要更长时间）
            
            # 导航到页面并等待加载完成
            page.goto(test_url, wait_until="domcontentloaded")
            
            # 等待外层iframe加载
            page.wait_for_selector("#outer-iframe", state="attached")
            page.wait_for_timeout(1000)  # 等待iframe内容加载n            
            # 定位外层iframe
            outer_frame = page.frame_locator("#outer-iframe")
            
            # 定位内层iframe
            inner_frame = outer_frame.frame_locator("#inner-iframe")
            
            # 在内层iframe中操作元素
            inner_frame.locator("#search").fill("test search")
            inner_frame.locator("#search-button").click()
            
            # 验证内层iframe中的操作结果
            results = inner_frame.locator(".search-results")
            expect(results).to_be_visible()
            
            logger.info("嵌套iframe测试通过")
            return True
            
        except Exception as e:
            logger.error(f"嵌套iframe测试失败: {str(e)}")
            save_debug_info(page, "test_nested_iframes_failed")
            raise
