"""
多平台LLM使用演示

展示如何在实际场景中使用多平台AI大模型
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def setup_demo_environment():
    """设置演示环境"""
    print("🔧 设置演示环境")
    print("-" * 20)
    
    # 从.env文件加载配置
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"✅ 加载环境变量: {key}")
    else:
        print("❌ .env文件不存在")
    
    # 检查配置
    gemini_key = os.getenv('GEMINI_API_KEY')
    qwen_key = os.getenv('QWEN_API_KEY') or os.getenv('DASHSCOPE_API_KEY')
    openai_key = os.getenv('OPENAI_API_KEY')
    
    print(f"\n📊 配置状态:")
    print(f"   Gemini: {'✅ 已配置' if gemini_key else '❌ 未配置'}")
    print(f"   通义千问: {'✅ 已配置' if qwen_key else '❌ 未配置'}")
    print(f"   OpenAI: {'✅ 已配置' if openai_key else '❌ 未配置'}")
    
    return bool(gemini_key or qwen_key or openai_key)


async def demo_basic_llm_usage():
    """演示基础LLM使用"""
    print("\n🎭 演示1: 基础LLM使用")
    print("=" * 40)
    
    try:
        from ai_llm_manager import get_llm_manager
        
        manager = get_llm_manager()
        available_providers = manager.get_available_providers()
        
        print(f"可用的AI平台: {[p.value for p in available_providers]}")
        
        if not available_providers:
            print("❌ 没有可用的AI平台")
            return False
        
        # 演示自动选择最佳LLM
        print(f"\n🔍 自动选择最佳AI平台:")
        response = await manager.generate("请用一句话介绍人工智能")
        print(f"   使用平台: {response.provider.value}")
        print(f"   使用模型: {response.model}")
        print(f"   AI响应: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础LLM演示失败: {e}")
        return False


async def demo_specific_provider_usage():
    """演示指定提供商使用"""
    print("\n🎭 演示2: 指定AI平台使用")
    print("=" * 40)
    
    try:
        from ai_llm_manager import get_llm_manager, LLMProvider
        
        manager = get_llm_manager()
        available_providers = manager.get_available_providers()
        
        # 为每个可用的提供商演示使用
        for provider in available_providers:
            print(f"\n🔍 使用 {provider.value} 平台:")
            try:
                response = await manager.generate(
                    f"请说'Hello from {provider.value}'", 
                    provider=provider
                )
                print(f"   ✅ 成功")
                print(f"   📝 响应: {response.content}")
                print(f"   📊 模型: {response.model}")
            except Exception as e:
                print(f"   ❌ 失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 指定提供商演示失败: {e}")
        return False


async def demo_browser_use_integration():
    """演示browser-use集成"""
    print("\n🎭 演示3: browser-use集成")
    print("=" * 40)
    
    try:
        from real_browser_use_integration import get_real_browser_use_agent, create_multi_llm_agent
        
        # 演示自动选择LLM的代理
        print("🔍 创建自动选择LLM的代理:")
        agent = get_real_browser_use_agent()
        
        if agent._check_prerequisites():
            print("   ✅ 代理创建成功")
            
            # 演示需求分析
            print("\n🔍 演示需求分析:")
            test_request = "请帮我打开百度网站并搜索人工智能"
            
            session = agent.interaction_manager.start_requirement_analysis_session(test_request)
            print(f"   📝 用户需求: {test_request}")
            print(f"   🧠 AI理解: {session.user_requirement.parsed_intent}")
            print(f"   🏢 业务领域: {session.user_requirement.business_domain}")
            print(f"   📊 置信度: {session.user_requirement.confidence:.2f}")
            
            if session.user_requirement.requires_parameters:
                print(f"   📋 需要参数: {session.user_requirement.required_parameters}")
            
            if session.workflow_matches:
                print(f"   🔗 匹配工作流: {len(session.workflow_matches)} 个")
        else:
            print("   ❌ 代理前提条件检查失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ browser-use集成演示失败: {e}")
        return False


async def demo_ai_comparison():
    """演示AI平台对比"""
    print("\n🎭 演示4: AI平台对比")
    print("=" * 40)
    
    try:
        from ai_llm_manager import get_llm_manager
        import time
        
        manager = get_llm_manager()
        available_providers = manager.get_available_providers()
        
        if len(available_providers) < 2:
            print("❌ 需要至少2个AI平台才能进行对比")
            return False
        
        # 对比测试
        test_prompt = "请用中文解释什么是机器学习，限制在100字以内"
        print(f"📝 测试问题: {test_prompt}")
        
        results = {}
        
        for provider in available_providers:
            print(f"\n🔍 测试 {provider.value}:")
            try:
                start_time = time.time()
                response = await manager.generate(test_prompt, provider=provider)
                end_time = time.time()
                
                response_time = end_time - start_time
                
                results[provider.value] = {
                    "success": True,
                    "response": response.content,
                    "model": response.model,
                    "response_time": response_time,
                    "response_length": len(response.content)
                }
                
                print(f"   ✅ 响应时间: {response_time:.2f}秒")
                print(f"   📝 响应长度: {len(response.content)}字符")
                print(f"   📊 模型: {response.model}")
                print(f"   💬 内容: {response.content[:80]}...")
                
            except Exception as e:
                print(f"   ❌ 失败: {e}")
                results[provider.value] = {"success": False, "error": str(e)}
        
        # 生成对比报告
        print(f"\n📊 AI平台对比报告:")
        print("-" * 30)
        
        successful_results = {k: v for k, v in results.items() if v.get("success")}
        
        if successful_results:
            # 找出最快的
            fastest = min(successful_results.items(), key=lambda x: x[1]["response_time"])
            print(f"🏃 最快响应: {fastest[0]} ({fastest[1]['response_time']:.2f}秒)")
            
            # 找出最详细的
            most_detailed = max(successful_results.items(), key=lambda x: x[1]["response_length"])
            print(f"📝 最详细回答: {most_detailed[0]} ({most_detailed[1]['response_length']}字符)")
        
        return True
        
    except Exception as e:
        print(f"❌ AI平台对比演示失败: {e}")
        return False


async def demo_practical_scenario():
    """演示实际应用场景"""
    print("\n🎭 演示5: 实际应用场景")
    print("=" * 40)
    
    try:
        from real_browser_use_integration import get_real_browser_use_agent
        
        # 模拟实际的用户请求
        practical_requests = [
            "请帮我打开淘宝网站",
            "请帮我搜索iPhone 15的价格信息",
            "请帮我创建一个新的邮箱账户",
            "请帮我查看今天的天气预报"
        ]
        
        agent = get_real_browser_use_agent()
        
        if not agent._check_prerequisites():
            print("❌ 代理不可用")
            return False
        
        print("🔍 分析实际用户请求:")
        
        for i, request in enumerate(practical_requests, 1):
            print(f"\n   请求 {i}: {request}")
            try:
                session = agent.interaction_manager.start_requirement_analysis_session(request)
                print(f"   🧠 AI理解: {session.user_requirement.parsed_intent}")
                print(f"   🏢 业务领域: {session.user_requirement.business_domain}")
                print(f"   📊 置信度: {session.user_requirement.confidence:.2f}")
                
                if session.user_requirement.requires_parameters:
                    print(f"   📋 需要参数: {', '.join(session.user_requirement.required_parameters)}")
                else:
                    print(f"   ✅ 无需额外参数")
                    
            except Exception as e:
                print(f"   ❌ 分析失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 实际场景演示失败: {e}")
        return False


async def main():
    """主函数"""
    print("🎭 多平台LLM使用演示")
    print("展示AI+RPA系统的多平台AI能力")
    print("=" * 60)
    
    # 设置环境
    if not setup_demo_environment():
        print("\n❌ 环境配置不完整，请配置至少一个AI平台的API密钥")
        print("💡 支持的平台: OpenAI, Google Gemini, 阿里云通义千问")
        return
    
    # 运行演示
    demos = [
        ("基础LLM使用", demo_basic_llm_usage),
        ("指定AI平台使用", demo_specific_provider_usage),
        ("browser-use集成", demo_browser_use_integration),
        ("AI平台对比", demo_ai_comparison),
        ("实际应用场景", demo_practical_scenario)
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        try:
            results[demo_name] = await demo_func()
        except Exception as e:
            print(f"❌ {demo_name} 演示失败: {e}")
            results[demo_name] = False
    
    # 生成演示报告
    print("\n" + "=" * 60)
    print("📊 多平台LLM演示报告")
    print("=" * 60)
    
    total_demos = len(results)
    successful_demos = sum(1 for success in results.values() if success)
    
    print(f"\n📈 演示统计:")
    print(f"   总演示数: {total_demos}")
    print(f"   成功数: {successful_demos}")
    print(f"   失败数: {total_demos - successful_demos}")
    print(f"   成功率: {successful_demos/total_demos*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for demo_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {demo_name}")
    
    if successful_demos == total_demos:
        print(f"\n🎉 所有演示成功！多平台AI系统运行完美！")
        print(f"\n🚀 您现在可以:")
        print(f"   1. 使用任何配置的AI平台执行browser-use任务")
        print(f"   2. 让系统自动选择最佳的AI平台")
        print(f"   3. 根据任务特点手动指定AI平台")
        print(f"   4. 享受智能降级和错误恢复功能")
        
    elif successful_demos > 0:
        print(f"\n🔄 部分演示成功")
        print(f"💡 系统基本可用，建议检查失败的演示项目")
        
    else:
        print(f"\n❌ 所有演示失败")
        print(f"💡 请检查API密钥配置和网络连接")
    
    print(f"\n💡 使用提示:")
    print(f"   # 自动选择最佳AI")
    print(f"   agent = get_real_browser_use_agent()")
    print(f"   ")
    print(f"   # 指定使用Gemini")
    print(f"   agent = get_real_browser_use_agent(llm_provider='gemini')")
    print(f"   ")
    print(f"   # 执行任务")
    print(f"   result = await agent.execute_user_request('您的任务')")


if __name__ == "__main__":
    asyncio.run(main())
