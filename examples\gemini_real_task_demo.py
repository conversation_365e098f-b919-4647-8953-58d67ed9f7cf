"""
Gemini真实任务演示

使用Gemini执行实际的browser-use自动化任务
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value


async def demo_gemini_requirement_analysis():
    """演示Gemini需求分析能力"""
    print("🧪 演示Gemini需求分析能力")
    print("=" * 50)
    
    try:
        from real_browser_use_integration import get_real_browser_use_agent
        
        # 创建使用Gemini的代理
        agent = get_real_browser_use_agent(llm_provider="gemini")
        
        if not agent._check_prerequisites():
            print("❌ Gemini代理前提条件检查失败")
            return False
        
        print("✅ Gemini代理创建成功")
        
        # 测试各种类型的用户需求
        test_requests = [
            "请帮我打开百度网站",
            "请帮我搜索人工智能的最新发展",
            "请帮我打开淘宝并搜索iPhone 15",
            "请帮我查看今天的天气预报",
            "请帮我打开GitHub并搜索Python项目",
            "请帮我创建一个新的邮箱账户",
            "请帮我在京东上比较不同品牌的笔记本电脑价格",
            "请帮我查看股票市场的最新行情"
        ]
        
        print(f"\n🔍 使用Gemini分析 {len(test_requests)} 个用户需求:")
        
        successful_analyses = 0
        
        for i, request in enumerate(test_requests, 1):
            print(f"\n📋 需求 {i}: {request}")
            
            try:
                session = agent.interaction_manager.start_requirement_analysis_session(request)
                
                print(f"   ✅ 分析成功")
                print(f"   🧠 AI理解: {session.user_requirement.parsed_intent}")
                print(f"   🏢 业务领域: {session.user_requirement.business_domain or '通用'}")
                print(f"   📊 置信度: {session.user_requirement.confidence:.2f}")
                
                if session.user_requirement.requires_parameters:
                    print(f"   📋 需要参数: {', '.join(session.user_requirement.required_parameters)}")
                else:
                    print(f"   ✅ 无需额外参数")
                
                if session.workflow_matches:
                    print(f"   🔗 匹配工作流: {len(session.workflow_matches)} 个")
                
                successful_analyses += 1
                
            except Exception as e:
                print(f"   ❌ 分析失败: {e}")
        
        success_rate = successful_analyses / len(test_requests) * 100
        print(f"\n📊 需求分析统计:")
        print(f"   总需求数: {len(test_requests)}")
        print(f"   成功分析: {successful_analyses}")
        print(f"   成功率: {success_rate:.1f}%")
        
        return success_rate >= 80  # 80%以上认为成功
        
    except Exception as e:
        print(f"❌ Gemini需求分析演示失败: {e}")
        return False


async def demo_gemini_ai_conversation():
    """演示Gemini AI对话能力"""
    print("\n🧪 演示Gemini AI对话能力")
    print("=" * 50)
    
    try:
        from ai_llm_manager import get_llm_manager, LLMProvider
        
        manager = get_llm_manager()
        
        # 模拟与用户的对话场景
        conversation_scenarios = [
            {
                "scenario": "任务确认",
                "prompt": "用户说：'请帮我打开百度搜索人工智能'。请确认你理解的任务内容。"
            },
            {
                "scenario": "参数收集",
                "prompt": "用户想要搜索商品，但没有说具体搜索什么。请询问需要搜索的商品名称。"
            },
            {
                "scenario": "错误处理",
                "prompt": "在执行网页操作时遇到了'页面加载超时'的错误，请给用户一个友好的解释和建议。"
            },
            {
                "scenario": "结果总结",
                "prompt": "已经成功帮用户在淘宝上搜索了'iPhone 15'，找到了50个商品结果。请总结执行结果。"
            },
            {
                "scenario": "多步骤规划",
                "prompt": "用户要求：'帮我比较京东和淘宝上iPhone 15的价格'。请制定执行计划。"
            }
        ]
        
        print(f"🔍 测试 {len(conversation_scenarios)} 个对话场景:")
        
        successful_conversations = 0
        
        for i, scenario in enumerate(conversation_scenarios, 1):
            print(f"\n💬 场景 {i}: {scenario['scenario']}")
            print(f"   📝 提示: {scenario['prompt']}")
            
            try:
                response = await manager.generate(scenario['prompt'], provider=LLMProvider.GEMINI)
                
                print(f"   ✅ 响应成功")
                print(f"   🤖 Gemini回复: {response.content[:200]}...")
                
                successful_conversations += 1
                
            except Exception as e:
                print(f"   ❌ 对话失败: {e}")
        
        success_rate = successful_conversations / len(conversation_scenarios) * 100
        print(f"\n📊 对话能力统计:")
        print(f"   总场景数: {len(conversation_scenarios)}")
        print(f"   成功对话: {successful_conversations}")
        print(f"   成功率: {success_rate:.1f}%")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ Gemini对话能力演示失败: {e}")
        return False


async def demo_gemini_performance():
    """演示Gemini性能表现"""
    print("\n🧪 演示Gemini性能表现")
    print("=" * 50)
    
    try:
        from ai_llm_manager import get_llm_manager, LLMProvider
        import time
        
        manager = get_llm_manager()
        
        # 不同复杂度的任务
        performance_tests = [
            {
                "name": "简单问答",
                "prompt": "请说'Hello'"
            },
            {
                "name": "中文理解",
                "prompt": "请用一句话解释什么是人工智能"
            },
            {
                "name": "任务分析",
                "prompt": "分析这个任务：'请帮我在电商网站上搜索并比较不同品牌手机的价格'"
            },
            {
                "name": "步骤规划",
                "prompt": "制定详细步骤：如何在网上购买一台笔记本电脑"
            },
            {
                "name": "错误处理",
                "prompt": "如果在自动化过程中遇到'网页元素未找到'的错误，应该如何处理？"
            }
        ]
        
        print(f"🔍 测试 {len(performance_tests)} 个性能场景:")
        
        total_time = 0
        successful_tests = 0
        response_times = []
        
        for i, test in enumerate(performance_tests, 1):
            print(f"\n⚡ 测试 {i}: {test['name']}")
            
            try:
                start_time = time.time()
                response = await manager.generate(test['prompt'], provider=LLMProvider.GEMINI)
                end_time = time.time()
                
                response_time = end_time - start_time
                response_times.append(response_time)
                total_time += response_time
                
                print(f"   ✅ 成功 (耗时: {response_time:.2f}秒)")
                print(f"   📝 响应长度: {len(response.content)}字符")
                print(f"   💬 响应预览: {response.content[:100]}...")
                
                successful_tests += 1
                
            except Exception as e:
                print(f"   ❌ 失败: {e}")
        
        if response_times:
            avg_time = total_time / len(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            
            print(f"\n📊 性能统计:")
            print(f"   成功测试: {successful_tests}/{len(performance_tests)}")
            print(f"   平均响应时间: {avg_time:.2f}秒")
            print(f"   最快响应: {min_time:.2f}秒")
            print(f"   最慢响应: {max_time:.2f}秒")
            print(f"   总耗时: {total_time:.2f}秒")
        
        return successful_tests == len(performance_tests)
        
    except Exception as e:
        print(f"❌ Gemini性能演示失败: {e}")
        return False


async def main():
    """主函数"""
    print("🎭 Gemini真实任务演示")
    print("验证Gemini在实际AI+RPA场景中的表现")
    print("=" * 60)
    
    # 加载环境变量
    load_env()
    
    # 检查Gemini配置
    gemini_key = os.getenv('GEMINI_API_KEY')
    gemini_url = os.getenv('GEMINI_BASE_URL')
    
    if not gemini_key or not gemini_url:
        print("❌ Gemini配置不完整")
        print("💡 请确保设置了GEMINI_API_KEY和GEMINI_BASE_URL")
        return
    
    print(f"✅ Gemini配置检查通过")
    print(f"   API密钥: 已设置")
    print(f"   自定义URL: {gemini_url}")
    
    # 运行演示
    demos = [
        ("需求分析能力", demo_gemini_requirement_analysis),
        ("AI对话能力", demo_gemini_ai_conversation),
        ("性能表现", demo_gemini_performance)
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        try:
            results[demo_name] = await demo_func()
        except Exception as e:
            print(f"❌ {demo_name} 演示异常: {e}")
            results[demo_name] = False
    
    # 生成演示报告
    print("\n" + "=" * 60)
    print("📊 Gemini真实任务演示报告")
    print("=" * 60)
    
    total_demos = len(results)
    successful_demos = sum(1 for success in results.values() if success)
    
    print(f"\n📈 演示统计:")
    print(f"   总演示数: {total_demos}")
    print(f"   成功数: {successful_demos}")
    print(f"   失败数: {total_demos - successful_demos}")
    print(f"   成功率: {successful_demos/total_demos*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for demo_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {demo_name}")
    
    if successful_demos == total_demos:
        print(f"\n🎉 所有演示成功！Gemini已准备好执行实际任务！")
        print(f"\n🚀 您现在可以:")
        print(f"   1. 使用Gemini执行复杂的browser-use自动化任务")
        print(f"   2. 享受优秀的中文理解和对话能力")
        print(f"   3. 利用快速的响应时间提升工作效率")
        print(f"   4. 依靠稳定的自定义URL连接")
        
        print(f"\n💡 实际使用:")
        print(f"   agent = get_real_browser_use_agent(llm_provider='gemini')")
        print(f"   result = await agent.execute_user_request('请帮我在淘宝搜索商品')")
        
    elif successful_demos > 0:
        print(f"\n🔄 部分演示成功")
        print(f"💡 基础功能可用，建议检查失败的演示项目")
        
    else:
        print(f"\n❌ 所有演示失败")
        print(f"💡 请检查Gemini配置和网络连接")


if __name__ == "__main__":
    asyncio.run(main())
