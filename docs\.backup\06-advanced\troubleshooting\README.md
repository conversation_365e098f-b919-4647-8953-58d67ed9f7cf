# 故障排除指南

本指南帮助你解决在使用系统过程中可能遇到的常见问题。

## 录制问题

### 1. 浏览器启动失败

**问题描述**：
运行录制命令时，浏览器无法启动。

**可能原因**：
1. Playwright 未正确安装
2. 系统缺少必要依赖
3. 浏览器被占用
4. 权限不足

**解决方案**：

1. 重新安装 Playwright
```bash
playwright install
```

2. 安装系统依赖
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y libglib2.0-0 libnss3 libnspr4 libatk1.0-0 libatk-bridge2.0-0 libcups2 libdrm2 libdbus-1-3 libxcb1 libxkbcommon0 libx11-6 libxcomposite1 libxdamage1 libxext6 libxfixes3 libxrandr2 libgbm1 libpango-1.0-0 libcairo2 libasound2

# CentOS
sudo yum install -y pango.x86_64 libXcomposite.x86_64 libXcursor.x86_64 libXdamage.x86_64 libXext.x86_64 libXi.x86_64 libXtst.x86_64 cups-libs.x86_64 libXScrnSaver.x86_64 libXrandr.x86_64 GConf2.x86_64 alsa-lib.x86_64 atk.x86_64 gtk3.x86_64 ipa-gothic-fonts xorg-x11-fonts-100dpi xorg-x11-fonts-75dpi xorg-x11-utils xorg-x11-fonts-cyrillic xorg-x11-fonts-Type1 xorg-x11-fonts-misc
```

3. 检查进程并终止
```bash
# Windows
taskkill /F /IM chrome.exe
taskkill /F /IM msedge.exe

# Linux/macOS
pkill chrome
pkill msedge
```

4. 以管理员权限运行
```bash
# Windows PowerShell (管理员)
Start-Process pwsh -Verb RunAs

# Linux/macOS
sudo python -m src.recorder ...
```

### 2. 选择器不稳定

**问题描述**：
录制的选择器在回放时经常失效。

**可能原因**：
1. 动态生成的 ID/类名
2. 页面结构变化
3. 选择器策略不当
4. Shadow DOM 影响

**解决方案**：

1. 使用更稳定的选择器
```python
# 配置选择器策略
recorder.configure({
    "selector_strategy": {
        "priority": [
            "data-testid",
            "aria-label",
            "text",
            "role"
        ]
    }
})
```

2. 添加测试属性
```html
<!-- 在目标元素上添加 data-testid -->
<button data-testid="login-button">登录</button>
```

3. 使用多重定位策略
```python
# 组合多个选择器
selector = [
    'button[data-testid="login-button"]',
    'button:has-text("登录")',
    'form >> "登录"'
]
```

4. 处理 Shadow DOM
```python
# 配置 Shadow DOM 处理
recorder.configure({
    "shadow_dom": {
        "mode": "open",
        "include": True
    }
})
```

### 3. 录制不完整

**问题描述**：
某些操作未被录制或录制不正确。

**可能原因**：
1. 事件监听不完整
2. 操作过快
3. 网络延迟
4. 内存不足

**解决方案**：

1. 添加事件监听
```python
# 注册自定义事件处理器
class CustomEventHandler(EventHandler):
    async def handle_hover(self, event):
        # 处理悬停事件
        pass
        
    async def handle_scroll(self, event):
        # 处理滚动事件
        pass

recorder.add_event_handler(CustomEventHandler())
```

2. 调整操作间隔
```python
# 配置操作延迟
recorder.configure({
    "action_delay": 500,  # 毫秒
    "typing_delay": 100   # 毫秒
})
```

3. 网络优化
```python
# 配置网络设置
recorder.configure({
    "network": {
        "timeout": 30000,
        "retry": 3,
        "throttling": "Fast 3G"
    }
})
```

4. 内存管理
```python
# 配置内存限制
recorder.configure({
    "memory": {
        "max_memory": "2G",
        "cleanup_interval": 300
    }
})
```

## 执行问题

### 1. 执行超时

**问题描述**：
工作流执行时经常超时。

**可能原因**：
1. 网络延迟
2. 页面加载慢
3. 等待策略不当
4. 资源限制

**解决方案**：

1. 调整超时设置
```python
# 配置超时时间
executor.configure({
    "timeout": {
        "navigation": 30000,
        "action": 10000,
        "assertion": 5000
    }
})
```

2. 优化等待策略
```python
# 配置等待策略
executor.configure({
    "wait": {
        "for_navigation": True,
        "for_load_state": "networkidle",
        "for_selector": "visible"
    }
})
```

3. 添加重试机制
```python
# 配置重试策略
executor.configure({
    "retry": {
        "attempts": 3,
        "delay": 1000,
        "strategy": "exponential"
    }
})
```

4. 资源优化
```python
# 配置资源限制
executor.configure({
    "resource": {
        "block_images": True,
        "block_media": True,
        "timeout": 5000
    }
})
```

### 2. 元素未找到

**问题描述**：
执行时无法找到目标元素。

**可能原因**：
1. 选择器失效
2. 页面未加载完成
3. 元素在 iframe 中
4. 元素动态生成

**解决方案**：

1. 使用智能等待
```python
# 配置智能等待
executor.configure({
    "smart_wait": {
        "enabled": True,
        "timeout": 10000,
        "interval": 100
    }
})
```

2. 处理 iframe
```python
# 配置 iframe 处理
executor.configure({
    "iframe": {
        "auto_detect": True,
        "timeout": 5000
    }
})
```

3. 添加重试逻辑
```python
# 自定义重试逻辑
class CustomRetry(RetryStrategy):
    async def should_retry(self, error):
        return isinstance(error, ElementNotFoundError)
        
    async def wait_time(self, attempt):
        return min(100 * (2 ** attempt), 5000)

executor.set_retry_strategy(CustomRetry())
```

4. 动态元素处理
```python
# 配置动态元素处理
executor.configure({
    "dynamic": {
        "wait_for_animation": True,
        "wait_for_mutation": True,
        "mutation_timeout": 5000
    }
})
```

### 3. 执行不稳定

**问题描述**：
相同的工作流执行结果不一致。

**可能原因**：
1. 环境不一致
2. 数据依赖
3. 并发问题
4. 随机因素

**解决方案**：

1. 环境隔离
```python
# 配置环境隔离
executor.configure({
    "isolation": {
        "clean_context": True,
        "clean_storage": True,
        "clean_cache": True
    }
})
```

2. 数据准备
```python
# 配置数据准备
executor.configure({
    "data": {
        "seed": 42,
        "reset": True,
        "snapshot": "test_data.json"
    }
})
```

3. 并发控制
```python
# 配置并发控制
executor.configure({
    "concurrency": {
        "max_workers": 1,
        "queue_size": 10,
        "timeout": 300
    }
})
```

4. 错误恢复
```python
# 配置错误恢复
executor.configure({
    "recovery": {
        "enabled": True,
        "max_attempts": 3,
        "strategies": ["reload", "retry", "skip"]
    }
})
```

## 监控问题

### 1. 性能问题

**问题描述**：
监控导致系统性能下降。

**可能原因**：
1. 采集频率过高
2. 数据量过大
3. 资源占用高
4. 内存泄漏

**解决方案**：

1. 调整采集策略
```python
# 配置采集策略
monitor.configure({
    "collection": {
        "interval": 5000,
        "batch_size": 100,
        "compression": True
    }
})
```

2. 数据过滤
```python
# 配置数据过滤
monitor.configure({
    "filter": {
        "metrics": ["cpu", "memory", "network"],
        "threshold": 0.1,
        "sampling_rate": 0.5
    }
})
```

3. 资源限制
```python
# 配置资源限制
monitor.configure({
    "resource": {
        "max_memory": "1G",
        "max_cpu": 50,
        "cleanup_interval": 60
    }
})
```

4. 内存优化
```python
# 配置内存优化
monitor.configure({
    "memory": {
        "gc_interval": 300,
        "cache_size": 1000,
        "weak_references": True
    }
})
```

### 2. 数据丢失

**问题描述**：
监控数据不完整或丢失。

**可能原因**：
1. 网络问题
2. 存储失败
3. 缓冲区溢出
4. 进程崩溃

**解决方案**：

1. 数据持久化
```python
# 配置数据持久化
monitor.configure({
    "storage": {
        "type": "file",
        "path": "./monitor_data",
        "backup": True
    }
})
```

2. 缓冲策略
```python
# 配置缓冲策略
monitor.configure({
    "buffer": {
        "size": 1000,
        "flush_interval": 60,
        "overflow_strategy": "block"
    }
})
```

3. 错误处理
```python
# 配置错误处理
monitor.configure({
    "error": {
        "retry": True,
        "max_retries": 3,
        "backup_storage": "./backup"
    }
})
```

4. 进程恢复
```python
# 配置进程恢复
monitor.configure({
    "recovery": {
        "enabled": True,
        "checkpoint_interval": 300,
        "state_file": "./monitor_state.json"
    }
})
```

### 3. 告警异常

**问题描述**：
告警触发不正确或未触发。

**可能原因**：
1. 规则配置错误
2. 阈值不合理
3. 数据延迟
4. 通知失败

**解决方案**：

1. 规则优化
```python
# 配置告警规则
monitor.configure({
    "alert": {
        "rules": [
            {
                "metric": "cpu_usage",
                "operator": ">",
                "threshold": 80,
                "duration": "5m"
            }
        ]
    }
})
```

2. 阈值动态调整
```python
# 配置动态阈值
monitor.configure({
    "threshold": {
        "auto_adjust": True,
        "learning_rate": 0.1,
        "history_window": "1h"
    }
})
```

3. 延迟处理
```python
# 配置延迟处理
monitor.configure({
    "delay": {
        "max_delay": "5m",
        "buffer_size": 1000,
        "catch_up": True
    }
})
```

4. 通知重试
```python
# 配置通知重试
monitor.configure({
    "notification": {
        "channels": ["email", "slack"],
        "retry_count": 3,
        "retry_interval": 60
    }
})
```

## 下一步

- 查看[性能优化指南](../performance/README.md)了解如何优化系统性能
- 查看[最佳实践](../best-practices/README.md)了解如何避免常见问题
- 加入[社区讨论](../../discussions)获取更多帮助 