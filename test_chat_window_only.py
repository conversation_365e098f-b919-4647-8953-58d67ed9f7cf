"""
简化的聊天窗口测试

只测试图形化聊天窗口和命令识别功能
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value


async def test_chat_window_with_commands():
    """测试聊天窗口和命令处理"""
    try:
        print("🎭 AI+RPA 聊天窗口演示")
        print("=" * 50)
        
        # 加载环境变量
        load_env()
        
        # 导入组件
        from interactive_chat_window import InteractiveChatWindow
        from intelligent_command_processor import get_intelligent_command_processor
        
        # 创建命令处理器
        command_processor = get_intelligent_command_processor()
        
        # 创建聊天窗口
        chat_window = InteractiveChatWindow(
            title="AI+RPA 智能助手演示",
            width=700,
            height=800
        )
        
        print("✅ 聊天窗口创建成功")
        print("💡 即将打开图形化聊天窗口...")
        print("\n🎯 您可以在聊天窗口中测试:")
        print("   • 输入各种命令测试智能识别")
        print("   • 尝试自然语言交互")
        print("   • 使用快捷按钮")
        print("   • 查看菜单功能")
        print("   • 测试操作记录")
        
        print("\n🔧 支持的命令示例:")
        print("   • '分析当前页面' - 页面分析命令")
        print("   • '我要查看用户管理' - 导航命令")
        print("   • '查询订单信息' - 查询命令")
        print("   • '系统状态' - 系统命令")
        print("   • '帮助' - 帮助命令")
        
        # 设置消息处理器
        async def handle_message(message):
            """处理用户消息"""
            try:
                print(f"📝 处理用户消息: {message}")
                
                # 使用命令处理器分析
                result = await command_processor.process_command(message)
                
                # 生成响应
                if result.success:
                    response = f"""✅ 命令识别成功！

🏷️ 命令类型: {result.command_type.value}
⚡ 动作: {result.action}
📊 置信度: {result.confidence:.2f}
🔧 处理方法: {result.processing_method}

💬 响应: {result.response}

📋 参数: {result.parameters}"""
                else:
                    response = f"""❌ 命令识别失败

💬 {result.response}

💡 您可以尝试:
• 使用更明确的表达
• 输入'帮助'查看支持的命令
• 尝试其他表达方式"""
                
                # 发送响应到聊天窗口
                chat_window.send_response(response)
                
            except Exception as e:
                error_msg = f"❌ 处理消息时发生错误: {e}"
                chat_window.send_error_message(error_msg)
        
        # 设置消息处理器
        chat_window.set_message_handler(handle_message)
        
        # 启动聊天窗口（异步）
        chat_thread = chat_window.run_async()
        
        # 等待窗口创建
        await asyncio.sleep(2)
        
        # 发送欢迎消息
        welcome_msg = """🎉 欢迎使用 AI+RPA 智能助手演示！

这是一个简化的聊天窗口演示，专门展示:
• 🖥️ 图形化聊天界面
• 🤖 智能命令识别
• 🗣️ 自然语言交互
• 📝 实时响应反馈

请尝试输入各种命令来测试系统的智能识别能力！

💡 提示: 您可以输入任何自然语言命令，系统会智能分析并给出反馈。"""
        
        chat_window.send_response(welcome_msg)
        
        # 消息处理循环
        print("\n🚀 聊天窗口已启动！")
        print("💬 请在图形窗口中进行交互...")
        
        while chat_window.is_running:
            # 检查用户消息
            user_message = chat_window.get_user_message()
            
            if user_message:
                await handle_message(user_message)
            
            # 短暂休眠
            await asyncio.sleep(0.1)
        
        print("👋 聊天窗口已关闭")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


async def main():
    """主函数"""
    try:
        await test_chat_window_with_commands()
    except KeyboardInterrupt:
        print("\n👋 用户取消测试")
    except Exception as e:
        print(f"❌ 程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
