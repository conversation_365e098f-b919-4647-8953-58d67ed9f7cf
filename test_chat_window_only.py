"""
简化的聊天窗口测试

只测试图形化聊天窗口和命令识别功能
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value


async def test_chat_window_with_commands():
    """测试聊天窗口和命令处理"""
    try:
        print("🎭 AI+RPA 聊天窗口演示")
        print("=" * 50)
        
        # 加载环境变量
        load_env()
        
        # 导入组件
        from interactive_chat_window import InteractiveChatWindow
        from intelligent_command_processor import get_intelligent_command_processor
        
        # 创建命令处理器
        command_processor = get_intelligent_command_processor()
        
        # 创建聊天窗口
        chat_window = InteractiveChatWindow(
            title="AI+RPA 智能助手演示",
            width=700,
            height=800
        )
        
        print("✅ 聊天窗口创建成功")
        print("💡 即将打开图形化聊天窗口...")
        print("\n🎯 您可以在聊天窗口中测试:")
        print("   • 输入各种命令测试智能识别")
        print("   • 尝试自然语言交互")
        print("   • 使用快捷按钮")
        print("   • 查看菜单功能")
        print("   • 测试操作记录")
        
        print("\n🔧 支持的命令示例:")
        print("   • '分析当前页面' - 页面分析命令")
        print("   • '我要查看用户管理' - 导航命令")
        print("   • '查询订单信息' - 查询命令")
        print("   • '系统状态' - 系统命令")
        print("   • '帮助' - 帮助命令")
        
        # 设置消息处理器
        async def handle_message(message):
            """处理用户消息"""
            try:
                print(f"📝 处理用户消息: {message}")

                # 使用命令处理器分析
                result = await command_processor.process_command(message)

                # 生成响应
                if result.success:
                    # 根据命令类型执行不同的操作
                    if result.command_type.value == "navigation":
                        # 导航命令 - 模拟智能导航
                        target = result.parameters.get("target", "")
                        response = f"""🎯 智能导航执行

✅ 命令识别: {result.command_type.value}
🎯 导航目标: {target}
📊 置信度: {result.confidence:.2f}
🔧 处理方法: {result.processing_method}

🤖 AI分析结果:
正在分析"{target}"的导航需求...
• 识别关键词: {target}
• 匹配相关功能区域
• 生成导航路径

💡 模拟执行:
如果这是真实环境，系统会：
1. 分析当前页面的所有链接
2. 使用AI匹配最相关的链接
3. 自动点击并导航到目标页面
4. 验证导航结果

📋 参数: {result.parameters}"""

                    elif result.command_type.value == "analysis":
                        # 分析命令 - 模拟页面分析
                        response = f"""🔍 页面分析执行

✅ 命令识别: {result.command_type.value}
📊 置信度: {result.confidence:.2f}
🔧 处理方法: {result.processing_method}

🤖 AI分析结果:
正在分析当前页面结构...
• 提取所有可操作元素
• AI分类功能区域
• 评估操作优先级

💡 模拟执行:
如果这是真实环境，系统会：
1. 扫描页面所有链接和按钮
2. 使用AI分析每个元素的功能
3. 生成结构化的操作数据
4. 提供智能操作建议

📋 发现的操作类型: 导航、查询、设置、帮助"""

                    elif result.command_type.value == "query":
                        # 查询命令 - 模拟智能查询
                        query = result.parameters.get("query", message)
                        response = f"""🔎 智能查询执行

✅ 命令识别: {result.command_type.value}
🔍 查询内容: {query}
📊 置信度: {result.confidence:.2f}
🔧 处理方法: {result.processing_method}

🤖 AI查询结果:
正在搜索"{query}"相关信息...
• 分析查询意图
• 匹配相关数据
• 生成查询结果

💡 模拟执行:
如果这是真实环境，系统会：
1. 在当前页面搜索相关内容
2. 使用AI理解查询意图
3. 返回最匹配的结果
4. 提供相关操作建议

📋 参数: {result.parameters}"""

                    elif result.command_type.value == "system":
                        # 系统命令
                        action = result.parameters.get("action", "")
                        response = f"""⚙️ 系统命令执行

✅ 命令识别: {result.command_type.value}
🔧 系统操作: {action}
📊 置信度: {result.confidence:.2f}
🔧 处理方法: {result.processing_method}

💡 系统状态:
• 聊天窗口: 🟢 运行中
• 命令处理器: 🟢 正常
• AI模型: 🟢 可用
• 操作记录: 📝 正在记录

📋 参数: {result.parameters}"""

                    elif result.command_type.value == "help":
                        # 帮助命令
                        response = f"""📖 帮助信息

✅ 命令识别: {result.command_type.value}
📊 置信度: {result.confidence:.2f}
🔧 处理方法: {result.processing_method}

🎯 支持的功能:
• 🎯 智能导航: "我要查看用户管理"
• 🔍 页面分析: "分析当前页面"
• 🔎 智能查询: "查询订单信息"
• ⚙️ 系统命令: "系统状态"
• 📖 帮助信息: "帮助"

💡 使用技巧:
• 使用自然语言描述需求
• 系统会智能识别命令类型
• AI协助处理复杂命令
• 所有操作都会被记录

📋 当前为演示模式，展示命令识别和AI处理能力"""

                    else:
                        # 其他命令类型
                        response = f"""✅ 命令识别成功！

🏷️ 命令类型: {result.command_type.value}
⚡ 动作: {result.action}
📊 置信度: {result.confidence:.2f}
🔧 处理方法: {result.processing_method}

💬 AI响应: {result.response}

📋 参数: {result.parameters}

💡 这是一个演示环境，展示了系统的智能命令识别能力。
在真实环境中，系统会执行相应的自动化操作。"""

                else:
                    response = f"""❌ 命令识别失败

💬 {result.response}

🤖 AI建议:
• 尝试使用更明确的表达
• 输入'帮助'查看支持的命令
• 使用自然语言描述您的需求

💡 示例命令:
• "我要查看用户管理" - 导航命令
• "分析当前页面" - 分析命令
• "查询订单信息" - 查询命令
• "系统状态" - 系统命令"""

                # 发送响应到聊天窗口
                chat_window.send_response(response)

            except Exception as e:
                error_msg = f"❌ 处理消息时发生错误: {e}"
                chat_window.send_error_message(error_msg)
        
        # 设置消息处理器
        chat_window.set_message_handler(handle_message)
        
        # 启动聊天窗口（异步）
        chat_thread = chat_window.run_async()
        
        # 等待窗口创建
        await asyncio.sleep(2)
        
        # 发送欢迎消息
        welcome_msg = """🎉 欢迎使用 AI+RPA 智能助手演示！

这是一个简化的聊天窗口演示，专门展示:
• 🖥️ 图形化聊天界面
• 🤖 智能命令识别
• 🗣️ 自然语言交互
• 📝 实时响应反馈

请尝试输入各种命令来测试系统的智能识别能力！

💡 提示: 您可以输入任何自然语言命令，系统会智能分析并给出反馈。"""
        
        chat_window.send_response(welcome_msg)
        
        # 消息处理循环
        print("\n🚀 聊天窗口已启动！")
        print("💬 请在图形窗口中进行交互...")
        
        while chat_window.is_running:
            # 检查用户消息
            user_message = chat_window.get_user_message()
            
            if user_message:
                await handle_message(user_message)
            
            # 短暂休眠
            await asyncio.sleep(0.1)
        
        print("👋 聊天窗口已关闭")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


async def main():
    """主函数"""
    try:
        await test_chat_window_with_commands()
    except KeyboardInterrupt:
        print("\n👋 用户取消测试")
    except Exception as e:
        print(f"❌ 程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
