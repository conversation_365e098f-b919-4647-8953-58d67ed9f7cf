# 简单工作流示例
id: simple_workflow
name: 简单工作流示例
description: 一个简单的工作流示例，包含开始、任务和结束节点
version: 1.0.0

# 节点定义
nodes:
  - id: start
    type: start
    name: 开始
    description: 工作流开始节点
  
  - id: task1
    type: task
    name: 任务1
    description: 第一个任务
    metadata:
      operation_id: "navigate_to_url"
      params:
        url: "https://example.com"
      output: "navigate_result"
  
  - id: task2
    type: task
    name: 任务2
    description: 第二个任务
    metadata:
      operation_id: "click_element"
      params:
        selector: "a[href='/about']"
      output: "click_result"
  
  - id: end
    type: end
    name: 结束
    description: 工作流结束节点

# 边定义
edges:
  - source: start
    target: task1
    description: 开始到任务1
  
  - source: task1
    target: task2
    description: 任务1到任务2
  
  - source: task2
    target: end
    description: 任务2到结束

# 元数据
metadata:
  author: "Workflow Engine"
  created_at: "2025-05-31"
