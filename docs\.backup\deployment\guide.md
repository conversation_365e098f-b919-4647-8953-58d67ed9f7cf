# 部署与运维指南

**最后更新**：2025-05-30

## 目录

1. [系统要求](#系统要求)
2. [安装指南](#安装指南)
3. [配置管理](#配置管理)
4. [部署方式](#部署方式)
5. [监控与告警](#监控与告警)
6. [备份与恢复](#备份与恢复)
7. [性能调优](#性能调优)
8. [安全指南](#安全指南)
9. [故障排除](#故障排除)
10. [升级指南](#升级指南)

## 系统要求

### 硬件要求

| 组件 | 最低配置 | 推荐配置 |
|------|---------|---------|
| CPU  | 2核     | 4核+    |
| 内存 | 4GB     | 8GB+    |
| 存储 | 10GB    | 50GB+   |
| 网络 | 100Mbps | 1Gbps+  |

### 软件要求

- 操作系统: Linux/Windows Server 2016+/macOS 10.15+
- Python: 3.9+
- Node.js: 16.0.0+
- Docker: 20.10.0+ (容器化部署)
- Kubernetes: 1.20+ (Kubernetes部署)

## 安装指南

### 1. 从源码安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/playwright-automation.git
cd playwright-automation

# 创建并激活虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
.\venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 安装Playwright浏览器
playwright install
```

### 2. 使用Docker

```bash
# 构建Docker镜像
docker build -t playwright-automation .

# 运行容器
docker run -d --name automation -p 8000:8000 playwright-automation
```

### 3. 使用Docker Compose

```yaml
version: '3.8'

services:
  app:
    image: playwright-automation:latest
    ports:
      - "8000:8000"
    volumes:
      - ./config:/app/config
      - ./data:/app/data
    environment:
      - ENV=production
    restart: unless-stopped
```

## 配置管理

### 配置文件

配置文件位于`config`目录下：

- `config/default.yaml` - 默认配置
- `config/production.yaml` - 生产环境配置
- `config/development.yaml` - 开发环境配置

### 环境变量

| 变量名 | 必填 | 默认值 | 描述 |
|--------|------|--------|------|
| ENV | 否 | development | 运行环境 (development/production) |
| DEBUG | 否 | false | 调试模式 |
| LOG_LEVEL | 否 | INFO | 日志级别 (DEBUG/INFO/WARNING/ERROR) |
| DATABASE_URL | 是 | - | 数据库连接字符串 |
| REDIS_URL | 否 | - | Redis连接字符串 |
| SECRET_KEY | 是 | - | 加密密钥 |

## 部署方式

### 1. 单机部署

```bash
# 启动服务
python -m uvicorn src.main:app --host 0.0.0.0 --port 8000

# 使用Gunicorn (生产环境)
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
```

### 2. 使用Systemd

创建服务文件`/etc/systemd/system/playwright.service`:

```ini
[Unit]
Description=Playwright Automation Service
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/playwright-automation
Environment="PATH=/path/to/venv/bin"
ExecStart=/path/to/venv/bin/gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务:

```bash
sudo systemctl daemon-reload
sudo systemctl enable playwright.service
sudo systemctl start playwright.service
```

### 3. Kubernetes部署

创建Deployment:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: playwright-automation
  labels:
    app: playwright-automation
spec:
  replicas: 3
  selector:
    matchLabels:
      app: playwright-automation
  template:
    metadata:
      labels:
        app: playwright-automation
    spec:
      containers:
      - name: playwright
        image: playwright-automation:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENV
          value: "production"
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "0.5"
            memory: "512Mi"
```

创建Service:

```yaml
apiVersion: v1
kind: Service
metadata:
  name: playwright-service
spec:
  selector:
    app: playwright-automation
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
  type: LoadBalancer
```

## 监控与告警

### 1. 监控指标

- **系统指标**: CPU、内存、磁盘、网络使用率
- **应用指标**: 请求数、响应时间、错误率
- **业务指标**: 任务执行数、成功率、平均执行时间

### 2. 集成Prometheus

```yaml
# prometheus.yaml
scrape_configs:
  - job_name: 'playwright'
    static_configs:
      - targets: ['localhost:8000']
```

### 3. 告警规则

```yaml
# alert.rules
groups:
- name: example
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 10m
    labels:
      severity: critical
    annotations:
      summary: "High error rate on {{ $labels.instance }}"
      description: "Error rate is {{ $value }} for {{ $labels.instance }}"
```

## 备份与恢复

### 1. 数据库备份

```bash
# 备份PostgreSQL
pg_dump -U username -d dbname > backup.sql

# 恢复PostgreSQL
psql -U username -d dbname < backup.sql
```

### 2. 文件备份

```bash
# 备份重要目录
tar -czvf backup_$(date +%Y%m%d).tar.gz /path/to/important/directory

# 使用rsync同步备份
rsync -avz /path/to/source user@backup-server:/backup/destination
```

## 性能调优

### 1. 数据库优化

- 创建适当的索引
- 优化查询语句
- 配置连接池

### 2. 应用优化

- 启用GZIP压缩
- 配置缓存头
- 使用CDN加速静态资源

### 3. 系统优化

```bash
# 调整文件描述符限制
echo "* soft nofile 65535" >> /etc/security/limits.conf
echo "* hard nofile 65535" >> /etc/security/limits.conf

# 调整内核参数
echo "fs.file-max = 2097152" >> /etc/sysctl.conf
sysctl -p
```

## 安全指南

### 1. 网络安全

- 使用HTTPS
- 配置WAF
- 限制访问IP

### 2. 认证与授权

- 使用JWT进行认证
- 实现RBAC
- 定期轮换密钥

### 3. 数据安全

- 加密敏感数据
- 定期审计
- 遵守GDPR/CCPA

## 故障排除

### 1. 查看日志

```bash
# 查看应用日志
journalctl -u playwright.service -f

# 查看容器日志
docker logs -f container_id
```

### 2. 常见问题

**Q: 服务无法启动**
- 检查端口是否被占用
- 检查依赖是否安装完整
- 查看错误日志

**Q: 性能下降**
- 检查系统资源使用情况
- 分析慢查询
- 检查是否有内存泄漏

## 升级指南

### 1. 小版本升级

```bash
git pull origin main
pip install -r requirements.txt
systemctl restart playwright.service
```

### 2. 大版本升级

1. 备份数据和配置
2. 查看升级说明
3. 执行数据库迁移
4. 更新代码和依赖
5. 测试关键功能
6. 切换流量

### 3. 回滚步骤

```bash
# 回滚到上一个版本
git checkout <previous-version-tag>
pip install -r requirements.txt
systemctl restart playwright.service
```

## 支持与帮助

- 文档: [项目文档](https://your-docs-url.com)
- 问题跟踪: [GitHub Issues](https://github.com/yourusername/playwright-automation/issues)
- 社区支持: [Discord/Slack 链接]
