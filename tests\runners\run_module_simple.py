"""
使用 python -m 运行测试模块
"""
import sys
import os
import subprocess

def run_tests():
    """运行测试"""
    # 获取项目根目录
    project_root = os.path.abspath('.')
    
    # 设置 Python 路径
    env = os.environ.copy()
    if 'PYTHONPATH' in env:
        env['PYTHONPATH'] = project_root + os.pathsep + env['PYTHONPATH']
    else:
        env['PYTHONPATH'] = project_root
    
    # 运行测试模块
    cmd = [sys.executable, "-m", "unittest", "tests/test_operations.py", "-v"]
    
    # 执行命令
    result = subprocess.run(cmd, env=env, cwd=project_root, text=True, capture_output=True)
    
    # 输出结果
    print(result.stdout)
    print(result.stderr, file=sys.stderr)
    
    # 返回退出码
    return result.returncode == 0

if __name__ == "__main__":
    # 运行测试
    success = run_tests()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)
