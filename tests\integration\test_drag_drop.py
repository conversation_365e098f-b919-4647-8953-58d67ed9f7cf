"""
拖放操作测试

测试拖放功能的集成测试
"""
import os
import time
import pytest
from playwright.sync_api import Page, expect
from tests.test_utils import get_test_data_path, save_debug_info

# 测试数据
DRAG_DROP_PAGE = get_test_data_path("drag_drop_test_page.html")

class TestDragDrop:
    """拖放操作测试类"""
    
    @pytest.fixture(autouse=True)
    def setup(self, page: Page):
        """测试前置条件"""
        self.page = page
        self.page.goto(DRAG_DROP_PAGE)
        
        # 等待页面加载完成
        self.page.wait_for_selector("#source", state="visible")
        self.page.wait_for_selector("#target", state="visible")
    
    def test_drag_and_drop_item(self, page: Page):
        """测试拖放单个项目"""
        try:
            # 获取源项目和目标区域
            source_item = page.locator("#item1")
            target_zone = page.locator("#target")
            
            # 验证初始状态
            expect(source_item).to_be_visible()
            expect(target_zone).to_be_visible()
            expect(target_zone.locator(".draggable")).to_have_count(0)
            
            # 执行拖放操作
            source_item.drag_to(target_zone)
            
            # 验证拖放结果
            expect(target_zone.locator("#item1")).to_be_visible()
            
            # 验证结果日志
            result = page.locator("#result div").first
            expect(result).to_contain_text("已将 item1 移动到 target")
            
            # 验证元素确实移动到了目标区域
            expect(source_item).to_be_hidden()
            
        except Exception as e:
            # 保存调试信息
            save_debug_info(page, "drag_drop_test_failed")
            raise e
    
    def test_drag_and_drop_multiple_items(self, page: Page):
        """测试拖放多个项目"""
        try:
            target_zone = page.locator("#target")
            
            # 拖放所有可拖动项到目标区域
            for i in range(1, 4):
                item_id = f"#item{i}"
                source_item = page.locator(item_id)
                
                # 执行拖放
                source_item.drag_to(target_zone)
                
                # 验证项目已移动到目标区域
                expect(target_zone.locator(item_id)).to_be_visible()
                
                # 验证结果日志
                result = page.locator(f"#result div:has-text('已将 item{i} 移动到 target')")
                expect(result).to_be_visible()
            
            # 验证所有项目都在目标区域
            expect(target_zone.locator(".draggable")).to_have_count(3)
            
        except Exception as e:
            save_debug_info(page, "drag_drop_multiple_test_failed")
            raise e
    
    def test_drag_and_drop_between_containers(self, page: Page):
        """测试在两个容器之间拖放项目"""
        try:
            source_zone = page.locator("#source")
            target_zone = page.locator("#target")
            
            # 首先将项目1拖到目标区域
            item1 = page.locator("#item1")
            item1.drag_to(target_zone)
            
            # 验证项目1已移动到目标区域
            expect(target_zone.locator("#item1")).to_be_visible()
            
            # 将项目1拖回源区域
            item1 = target_zone.locator("#item1")
            item1.drag_to(source_zone)
            
            # 验证项目1已移回源区域
            expect(source_zone.locator("#item1")).to_be_visible()
            expect(target_zone.locator("#item1")).to_have_count(0)
            
            # 验证结果日志
            result = page.locator("#result div").first
            expect(result).to_contain_text("已将 item1 移动到 source")
            
        except Exception as e:
            save_debug_info(page, "drag_drop_between_containers_test_failed")
            raise e
    
    def test_drag_and_drop_with_position(self, page: Page):
        """测试带位置的拖放操作"""
        try:
            source_item = page.locator("#item1")
            target_zone = page.locator("#target")
            
            # 获取目标区域的边界框
            target_box = target_zone.bounding_box()
            
            # 计算目标位置（中心点）
            target_x = target_box["x"] + target_box["width"] / 2
            target_y = target_box["y"] + target_box["height"] / 2
            
            # 执行带位置的拖放
            source_item.drag_to(
                target_zone,
                target_position={"x": target_box["width"] / 2, "y": target_box["height"] / 2}
            )
            
            # 验证项目已移动到目标区域
            expect(target_zone.locator("#item1")).to_be_visible()
            
        except Exception as e:
            save_debug_info(page, "drag_drop_with_position_test_failed")
            raise e

    def test_drag_and_drop_with_keyboard(self, page: Page):
        """测试使用键盘进行拖放操作"""
        try:
            # 聚焦到可拖动元素
            page.locator("#item1").focus()
            
            # 模拟键盘操作开始拖拽
            page.keyboard.press(" ")
            page.keyboard.press("Enter")
            
            # 使用方向键移动
            for _ in range(5):
                page.keyboard.press("ArrowRight")
            for _ in range(5):
                page.keyboard.press("ArrowDown")
            
            # 完成拖放
            page.keyboard.press(" ")
            
            # 验证项目已移动（这里需要根据实际实现调整）
            # 注意：纯HTML5拖放API可能不完全支持键盘操作，此测试可能需要根据实际实现调整
            
        except Exception as e:
            save_debug_info(page, "keyboard_drag_drop_test_failed")
            # 暂时跳过此测试，因为不是所有浏览器都完全支持键盘拖放
            pytest.xfail("键盘拖放支持可能因浏览器而异")

    def test_drag_start_and_cancel(self, page: Page):
        """测试开始拖拽后取消"""
        try:
            source_item = page.locator("#item1")
            target_zone = page.locator("#target")
            
            # 开始拖拽
            source_item.hover()
            page.mouse.down()
            
            # 移动鼠标到目标区域外
            target_box = target_zone.bounding_box()
            page.mouse.move(
                target_box["x"] - 50,
                target_box["y"] - 50
            )
            
            # 取消拖拽（释放鼠标）
            page.mouse.up()
            
            # 验证项目未移动
            expect(source_item).to_be_visible()
            expect(target_zone.locator("#item1")).to_have_count(0)
            
        except Exception as e:
            save_debug_info(page, "drag_cancel_test_failed")
            raise e
