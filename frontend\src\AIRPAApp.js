import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  Tabs,
  Tab,
  ThemeProvider,
  createTheme,
  CssBaseline,
  IconButton,
  Tooltip,
  Badge,
  Alert,
  Snackbar
} from '@mui/material';
import {
  Chat as ChatIcon,
  AccountTree as WorkflowIcon,
  List as ListIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';

import AIRPAChatPanel from './components/AIRPAChatPanel';
import WorkflowListPanel from './components/WorkflowListPanel';
import App as WorkflowDesigner from './App'; // 原有的工作流设计器

// 创建主题
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    h6: {
      fontWeight: 600,
    },
  },
});

// API基础URL
const API_BASE_URL = 'http://localhost:8000';

// 标签页组件
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      style={{ height: '100%' }}
      {...other}
    >
      {value === index && (
        <Box sx={{ height: '100%' }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function AIRPAApp() {
  const [currentTab, setCurrentTab] = useState(0);
  const [workflows, setWorkflows] = useState([]);
  const [currentWorkflow, setCurrentWorkflow] = useState(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionStatus, setExecutionStatus] = useState({});
  const [notifications, setNotifications] = useState([]);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

  // WebSocket连接
  const [ws, setWs] = useState(null);

  // 初始化WebSocket连接
  useEffect(() => {
    const websocket = new WebSocket('ws://localhost:8000/ws');
    
    websocket.onopen = () => {
      console.log('WebSocket连接成功');
      setWs(websocket);
    };
    
    websocket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        handleWebSocketMessage(message);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };
    
    websocket.onclose = () => {
      console.log('WebSocket连接关闭');
      setWs(null);
    };
    
    websocket.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };
    
    return () => {
      if (websocket.readyState === WebSocket.OPEN) {
        websocket.close();
      }
    };
  }, []);

  // 处理WebSocket消息
  const handleWebSocketMessage = useCallback((message) => {
    switch (message.type) {
      case 'workflow_execution_started':
        setExecutionStatus(prev => ({
          ...prev,
          [message.data.workflow_id]: 'running'
        }));
        showSnackbar(`工作流 "${message.data.workflow_name}" 开始执行`, 'info');
        break;
        
      case 'workflow_execution_completed':
        setExecutionStatus(prev => ({
          ...prev,
          [message.data.workflow_id]: 'completed'
        }));
        showSnackbar(`工作流执行完成`, 'success');
        break;
        
      case 'workflow_execution_error':
        setExecutionStatus(prev => ({
          ...prev,
          [message.data.workflow_id]: 'error'
        }));
        showSnackbar(`工作流执行失败: ${message.data.error}`, 'error');
        break;
        
      case 'workflow_created':
      case 'workflow_updated':
      case 'workflow_deleted':
        loadWorkflows(); // 重新加载工作流列表
        break;
        
      default:
        console.log('未处理的WebSocket消息:', message);
    }
  }, []);

  // 显示提示消息
  const showSnackbar = useCallback((message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  }, []);

  // 关闭提示消息
  const handleCloseSnackbar = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  // 加载工作流列表
  const loadWorkflows = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/workflows`);
      const data = await response.json();
      setWorkflows(data.workflows || []);
    } catch (error) {
      console.error('加载工作流列表失败:', error);
      showSnackbar('加载工作流列表失败', 'error');
    }
  }, [showSnackbar]);

  // 初始化时加载工作流
  useEffect(() => {
    loadWorkflows();
  }, [loadWorkflows]);

  // 处理聊天消息
  const handleSendMessage = useCallback(async (message) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/chat/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: message,
          type: 'user'
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }, []);

  // 执行工作流
  const handleExecuteWorkflow = useCallback(async (workflow) => {
    try {
      setIsExecuting(true);
      setCurrentWorkflow(workflow);
      
      const response = await fetch(`${API_BASE_URL}/api/workflows/${workflow.id}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflowId: workflow.id,
          parameters: {}
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      showSnackbar('工作流开始执行', 'success');
      
    } catch (error) {
      console.error('执行工作流失败:', error);
      showSnackbar('执行工作流失败', 'error');
      setIsExecuting(false);
    }
  }, [showSnackbar]);

  // 停止执行
  const handleStopExecution = useCallback(() => {
    setIsExecuting(false);
    setCurrentWorkflow(null);
    showSnackbar('工作流执行已停止', 'info');
  }, [showSnackbar]);

  // 创建工作流
  const handleCreateWorkflow = useCallback(async (workflowData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/workflows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(workflowData),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      showSnackbar('工作流创建成功', 'success');
      loadWorkflows();
      
    } catch (error) {
      console.error('创建工作流失败:', error);
      showSnackbar('创建工作流失败', 'error');
    }
  }, [showSnackbar, loadWorkflows]);

  // 编辑工作流
  const handleEditWorkflow = useCallback((workflow) => {
    setCurrentWorkflow(workflow);
    setCurrentTab(1); // 切换到工作流设计器
  }, []);

  // 删除工作流
  const handleDeleteWorkflow = useCallback(async (workflowId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/workflows/${workflowId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      showSnackbar('工作流删除成功', 'success');
      loadWorkflows();
      
    } catch (error) {
      console.error('删除工作流失败:', error);
      showSnackbar('删除工作流失败', 'error');
    }
  }, [showSnackbar, loadWorkflows]);

  // 复制工作流
  const handleDuplicateWorkflow = useCallback(async (workflow) => {
    const duplicatedWorkflow = {
      ...workflow,
      name: `${workflow.name} (副本)`,
      id: undefined // 让后端生成新ID
    };
    
    await handleCreateWorkflow(duplicatedWorkflow);
  }, [handleCreateWorkflow]);

  // 导出工作流
  const handleExportWorkflow = useCallback((workflow) => {
    const dataStr = JSON.stringify(workflow, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${workflow.name}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    showSnackbar('工作流导出成功', 'success');
  }, [showSnackbar]);

  // 导入工作流
  const handleImportWorkflow = useCallback(async (workflowData) => {
    await handleCreateWorkflow(workflowData);
  }, [handleCreateWorkflow]);

  // 标签页切换
  const handleTabChange = useCallback((event, newValue) => {
    setCurrentTab(newValue);
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        {/* 顶部应用栏 */}
        <AppBar position="static" elevation={1}>
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              🤖 AI+RPA 智能自动化平台
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Tooltip title="通知">
                <IconButton color="inherit">
                  <Badge badgeContent={notifications.length} color="error">
                    <NotificationsIcon />
                  </Badge>
                </IconButton>
              </Tooltip>
              
              <Tooltip title="设置">
                <IconButton color="inherit">
                  <SettingsIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Toolbar>
          
          {/* 标签页 */}
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            sx={{ borderTop: 1, borderColor: 'divider' }}
            textColor="inherit"
            indicatorColor="secondary"
          >
            <Tab
              icon={<ChatIcon />}
              label="智能助手"
              id="tab-0"
              aria-controls="tabpanel-0"
            />
            <Tab
              icon={<WorkflowIcon />}
              label="工作流设计"
              id="tab-1"
              aria-controls="tabpanel-1"
            />
            <Tab
              icon={<ListIcon />}
              label="工作流管理"
              id="tab-2"
              aria-controls="tabpanel-2"
            />
          </Tabs>
        </AppBar>

        {/* 主内容区域 */}
        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          {/* AI聊天助手 */}
          <TabPanel value={currentTab} index={0}>
            <AIRPAChatPanel
              onSendMessage={handleSendMessage}
              onExecuteWorkflow={handleExecuteWorkflow}
              onStopExecution={handleStopExecution}
              isExecuting={isExecuting}
              workflows={workflows}
              onLoadWorkflow={handleEditWorkflow}
            />
          </TabPanel>

          {/* 工作流设计器 */}
          <TabPanel value={currentTab} index={1}>
            <WorkflowDesigner />
          </TabPanel>

          {/* 工作流管理 */}
          <TabPanel value={currentTab} index={2}>
            <WorkflowListPanel
              workflows={workflows}
              onCreateWorkflow={handleCreateWorkflow}
              onEditWorkflow={handleEditWorkflow}
              onDeleteWorkflow={handleDeleteWorkflow}
              onExecuteWorkflow={handleExecuteWorkflow}
              onDuplicateWorkflow={handleDuplicateWorkflow}
              onImportWorkflow={handleImportWorkflow}
              onExportWorkflow={handleExportWorkflow}
              currentWorkflow={currentWorkflow}
              executionStatus={executionStatus}
            />
          </TabPanel>
        </Box>

        {/* 提示消息 */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}

export default AIRPAApp;
