<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="email"], input[type="password"],
        select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #4CAF50; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .hidden { display: none; }
        .message { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #dff0d8; color: #3c763d; }
        .error { background: #f2dede; color: #a94442; }
    </style>
</head>
<body>
    <h1>表单测试页面</h1>
    
    <form id="test-form">
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" name="username" placeholder="请输入用户名">
        </div>
        
        <div class="form-group">
            <label for="email">电子邮箱:</label>
            <input type="email" id="email" name="email" placeholder="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" name="password">
        </div>
        
        <div class="form-group">
            <label for="country">国家/地区:</label>
            <select id="country" name="country">
                <option value="">-- 请选择 --</option>
                <option value="CN">中国</option>
                <option value="US">美国</option>
                <option value="JP">日本</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>性别:</label>
            <input type="radio" id="male" name="gender" value="male">
            <label for="male" style="display: inline;">男</label>
            <input type="radio" id="female" name="gender" value="female" style="margin-left: 15px;">
            <label for="female" style="display: inline;">女</label>
        </div>
        
        <div class="form-group">
            <label>兴趣爱好:</label>
            <input type="checkbox" id="sports" name="hobbies" value="sports">
            <label for="sports" style="display: inline;">运动</label>
            <input type="checkbox" id="music" name="hobbies" value="music" style="margin-left: 15px;">
            <label for="music" style="display: inline;">音乐</label>
        </div>
        
        <div class="form-group">
            <button type="button" id="submit-btn">提交</button>
            <button type="button" id="reset-btn">重置</button>
        </div>
    </form>
    
    <div id="success-message" class="message success hidden">提交成功！</div>
    <div id="error-message" class="message error hidden">提交失败，请检查输入！</div>
    
    <script>
        // 存储控制台消息
        window.consoleMessages = [];
        const originalConsoleLog = console.log;
        console.log = function(message) {
            window.consoleMessages.push(String(message));
            originalConsoleLog.apply(console, arguments);
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('表单测试页面已加载');
            
            document.getElementById('submit-btn').addEventListener('click', function() {
                const username = document.getElementById('username').value.trim();
                const email = document.getElementById('email').value.trim();
                
                if (username && email) {
                    showSuccess('表单提交成功！');
                    console.log('表单已提交:', { username, email });
                } else {
                    showError('请填写必填字段');
                }
            });
            
            document.getElementById('reset-btn').addEventListener('click', function() {
                document.getElementById('test-form').reset();
                hideMessages();
                console.log('表单已重置');
            });
        });
        
        function showSuccess(message) {
            hideMessages();
            document.getElementById('success-message').textContent = message;
            document.getElementById('success-message').classList.remove('hidden');
        }
        
        function showError(message) {
            hideMessages();
            document.getElementById('error-message').textContent = message;
            document.getElementById('error-message').classList.remove('hidden');
        }
        
        function hideMessages() {
            document.getElementById('success-message').classList.add('hidden');
            document.getElementById('error-message').classList.add('hidden');
        }
    </script>
</body>
</html>
