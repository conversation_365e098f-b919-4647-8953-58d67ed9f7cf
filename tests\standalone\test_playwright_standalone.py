"""
独立的 Playwright 测试脚本
"""
import asyncio
from playwright.async_api import async_playwright

async def main():
    """主函数"""
    print("启动 Playwright...")
    async with async_playwright() as p:
        # 启动浏览器
        print("启动浏览器...")
        browser = await p.chromium.launch(headless=False)
        
        try:
            # 创建页面
            print("创建新页面...")
            page = await browser.new_page()
            
            # 导航到示例网站
            print("导航到 example.com...")
            await page.goto('https://example.com')
            
            # 获取页面标题
            title = await page.title()
            print(f"页面标题: {title}")
            
            # 验证标题
            if 'Example' in title:
                print("测试通过！")
            else:
                print(f"测试失败: 标题不包含 'Example'")
            
        except Exception as e:
            print(f"发生错误: {e}")
        finally:
            # 关闭浏览器
            print("关闭浏览器...")
            await browser.close()

if __name__ == "__main__":
    asyncio.run(main())
