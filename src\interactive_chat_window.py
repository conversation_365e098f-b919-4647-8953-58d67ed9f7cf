"""
交互式聊天窗口

实现图形化的交互聊天界面，支持实时交互和AI协助
"""
import asyncio
import threading
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import queue

logger = logging.getLogger(__name__)


@dataclass
class ChatMessage:
    """聊天消息"""
    timestamp: str
    sender: str          # user, assistant, system
    message: str
    message_type: str    # text, command, result, error
    metadata: Dict = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class InteractionRecord:
    """交互记录"""
    session_id: str
    start_time: str
    end_time: str
    messages: List[ChatMessage]
    operations_performed: List[Dict]
    summary: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "session_id": self.session_id,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "messages": [msg.to_dict() for msg in self.messages],
            "operations_performed": self.operations_performed,
            "summary": self.summary
        }


class InteractiveChatWindow:
    """交互式聊天窗口"""
    
    def __init__(self, title: str = "AI+RPA 智能助手", width: int = 600, height: int = 700):
        self.title = title
        self.width = width
        self.height = height
        
        # 窗口组件
        self.root = None
        self.chat_display = None
        self.input_entry = None
        self.send_button = None
        self.status_label = None
        
        # 交互状态
        self.is_running = False
        self.message_queue = queue.Queue()
        self.response_queue = queue.Queue()
        self.message_handler = None
        
        # 聊天记录
        self.messages = []
        self.session_id = self._generate_session_id()
        self.start_time = datetime.now().isoformat()
        
        # 样式配置
        self.colors = {
            "bg": "#f0f0f0",
            "chat_bg": "#ffffff",
            "user_msg": "#e3f2fd",
            "assistant_msg": "#f3e5f5",
            "system_msg": "#e8f5e8",
            "error_msg": "#ffebee",
            "button": "#2196f3",
            "button_hover": "#1976d2"
        }
    
    def create_window(self):
        """创建聊天窗口"""
        try:
            self.root = tk.Tk()
            self.root.title(self.title)
            self.root.geometry(f"{self.width}x{self.height}")
            self.root.configure(bg=self.colors["bg"])
            
            # 设置窗口图标和属性
            self.root.resizable(True, True)
            self.root.minsize(400, 500)
            
            # 创建主框架
            main_frame = ttk.Frame(self.root)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 创建标题
            title_label = ttk.Label(
                main_frame, 
                text="🤖 AI+RPA 智能助手", 
                font=("Arial", 16, "bold")
            )
            title_label.pack(pady=(0, 10))
            
            # 创建聊天显示区域
            self._create_chat_display(main_frame)
            
            # 创建输入区域
            self._create_input_area(main_frame)
            
            # 创建状态栏
            self._create_status_bar(main_frame)
            
            # 创建菜单
            self._create_menu()
            
            # 绑定事件
            self._bind_events()
            
            # 显示欢迎消息
            self._show_welcome_message()
            
            logger.info("交互聊天窗口创建成功")
            
        except Exception as e:
            logger.error(f"创建聊天窗口失败: {e}")
            raise
    
    def _create_chat_display(self, parent):
        """创建聊天显示区域"""
        # 聊天显示框架
        chat_frame = ttk.LabelFrame(parent, text="对话记录", padding=5)
        chat_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建滚动文本框
        self.chat_display = scrolledtext.ScrolledText(
            chat_frame,
            wrap=tk.WORD,
            width=70,
            height=25,
            font=("Arial", 10),
            bg=self.colors["chat_bg"],
            state=tk.DISABLED
        )
        self.chat_display.pack(fill=tk.BOTH, expand=True)
        
        # 配置文本标签样式
        self.chat_display.tag_configure("user", background=self.colors["user_msg"], lmargin1=10, lmargin2=10)
        self.chat_display.tag_configure("assistant", background=self.colors["assistant_msg"], lmargin1=10, lmargin2=10)
        self.chat_display.tag_configure("system", background=self.colors["system_msg"], lmargin1=10, lmargin2=10)
        self.chat_display.tag_configure("error", background=self.colors["error_msg"], lmargin1=10, lmargin2=10)
        self.chat_display.tag_configure("timestamp", foreground="gray", font=("Arial", 8))
        self.chat_display.tag_configure("bold", font=("Arial", 10, "bold"))
    
    def _create_input_area(self, parent):
        """创建输入区域"""
        input_frame = ttk.Frame(parent)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 输入标签
        input_label = ttk.Label(input_frame, text="💬 输入您的需求或命令:")
        input_label.pack(anchor=tk.W, pady=(0, 5))
        
        # 输入框和按钮框架
        entry_frame = ttk.Frame(input_frame)
        entry_frame.pack(fill=tk.X)
        
        # 输入框
        self.input_entry = ttk.Entry(
            entry_frame,
            font=("Arial", 11),
            width=50
        )
        self.input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 发送按钮
        self.send_button = ttk.Button(
            entry_frame,
            text="发送",
            command=self._on_send_click,
            width=10
        )
        self.send_button.pack(side=tk.RIGHT)
        
        # 快捷操作按钮
        shortcuts_frame = ttk.Frame(input_frame)
        shortcuts_frame.pack(fill=tk.X, pady=(10, 0))
        
        shortcuts = [
            ("📊 分析页面", "分析当前页面"),
            ("🔍 查找功能", "查找功能"),
            ("📝 记录操作", "记录当前操作"),
            ("❓ 帮助", "帮助")
        ]
        
        for i, (text, command) in enumerate(shortcuts):
            btn = ttk.Button(
                shortcuts_frame,
                text=text,
                command=lambda cmd=command: self._insert_command(cmd),
                width=12
            )
            btn.grid(row=0, column=i, padx=2, sticky=tk.W)
    
    def _create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X)
        
        self.status_label = ttk.Label(
            status_frame,
            text="🟢 就绪 - 请输入您的需求",
            font=("Arial", 9),
            foreground="green"
        )
        self.status_label.pack(side=tk.LEFT)
        
        # 会话信息
        session_label = ttk.Label(
            status_frame,
            text=f"会话ID: {self.session_id[:8]}...",
            font=("Arial", 9),
            foreground="gray"
        )
        session_label.pack(side=tk.RIGHT)
    
    def _create_menu(self):
        """创建菜单"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="保存对话记录", command=self._save_chat_history)
        file_menu.add_command(label="导出操作记录", command=self._export_operation_record)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_close)
        
        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="清空对话", command=self._clear_chat)
        edit_menu.add_command(label="复制对话", command=self._copy_chat)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="页面分析", command=lambda: self._insert_command("分析当前页面"))
        tools_menu.add_command(label="操作记录", command=lambda: self._insert_command("显示操作记录"))
        tools_menu.add_command(label="系统状态", command=lambda: self._insert_command("系统状态"))
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self._show_help)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _bind_events(self):
        """绑定事件"""
        # 回车发送消息
        self.input_entry.bind("<Return>", lambda e: self._on_send_click())
        
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # Ctrl+Enter 换行
        self.input_entry.bind("<Control-Return>", lambda e: self.input_entry.insert(tk.INSERT, "\n"))
    
    def _show_welcome_message(self):
        """显示欢迎消息"""
        welcome_msg = """🎉 欢迎使用 AI+RPA 智能助手！

我可以帮助您：
• 🔍 分析当前页面的操作选项
• 🎯 根据您的需求智能导航
• 📝 记录和生成操作流程
• 🤖 提供AI协助和建议

您可以：
• 直接输入需求，如："我要查看用户管理"
• 使用命令，如："分析页面"、"帮助"
• 点击快捷按钮进行常用操作

开始对话吧！👋"""
        
        self._add_message("assistant", welcome_msg, "text")
    
    def _insert_command(self, command: str):
        """插入命令到输入框"""
        self.input_entry.delete(0, tk.END)
        self.input_entry.insert(0, command)
        self.input_entry.focus()
    
    def _on_send_click(self):
        """发送按钮点击事件"""
        message = self.input_entry.get().strip()
        if message:
            self._send_message(message)
            self.input_entry.delete(0, tk.END)
    
    def _send_message(self, message: str):
        """发送消息"""
        # 显示用户消息
        self._add_message("user", message, "text")
        
        # 更新状态
        self._update_status("🔄 处理中...", "orange")
        
        # 将消息放入队列
        self.message_queue.put(message)
    
    def _add_message(self, sender: str, message: str, msg_type: str = "text", metadata: Dict = None):
        """添加消息到聊天显示"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 创建消息对象
        chat_msg = ChatMessage(
            timestamp=timestamp,
            sender=sender,
            message=message,
            message_type=msg_type,
            metadata=metadata or {}
        )
        self.messages.append(chat_msg)
        
        # 显示消息
        self.chat_display.config(state=tk.NORMAL)
        
        # 添加时间戳
        self.chat_display.insert(tk.END, f"[{timestamp}] ", "timestamp")
        
        # 添加发送者标识
        sender_icons = {
            "user": "👤 您",
            "assistant": "🤖 助手",
            "system": "⚙️ 系统"
        }
        sender_text = sender_icons.get(sender, sender)
        self.chat_display.insert(tk.END, f"{sender_text}: ", "bold")
        
        # 添加消息内容
        tag = sender if sender in ["user", "assistant", "system"] else "system"
        if msg_type == "error":
            tag = "error"
        
        self.chat_display.insert(tk.END, f"{message}\n\n", tag)
        
        # 滚动到底部
        self.chat_display.see(tk.END)
        self.chat_display.config(state=tk.DISABLED)
    
    def _update_status(self, status: str, color: str = "green"):
        """更新状态"""
        if self.status_label:
            self.status_label.config(text=status, foreground=color)
    
    def set_message_handler(self, handler: Callable):
        """设置消息处理器"""
        self.message_handler = handler
    
    def get_user_message(self) -> Optional[str]:
        """获取用户消息（非阻塞）"""
        try:
            return self.message_queue.get_nowait()
        except queue.Empty:
            return None
    
    def send_response(self, response: str, msg_type: str = "text", metadata: Dict = None):
        """发送响应消息"""
        self._add_message("assistant", response, msg_type, metadata)
        self._update_status("🟢 就绪 - 请输入您的需求")
    
    def send_system_message(self, message: str):
        """发送系统消息"""
        self._add_message("system", message, "text")
    
    def send_error_message(self, error: str):
        """发送错误消息"""
        self._add_message("assistant", f"❌ 错误: {error}", "error")
        self._update_status("🔴 错误", "red")
    
    def _save_chat_history(self):
        """保存对话记录"""
        try:
            from tkinter import filedialog
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="保存对话记录"
            )
            
            if filename:
                record = InteractionRecord(
                    session_id=self.session_id,
                    start_time=self.start_time,
                    end_time=datetime.now().isoformat(),
                    messages=self.messages,
                    operations_performed=[],  # 这里可以从外部传入
                    summary="交互式对话记录"
                )
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(record.to_dict(), f, indent=2, ensure_ascii=False)
                
                self.send_system_message(f"对话记录已保存到: {filename}")
                
        except Exception as e:
            self.send_error_message(f"保存对话记录失败: {e}")
    
    def _export_operation_record(self):
        """导出操作记录"""
        self.send_system_message("操作记录导出功能开发中...")
    
    def _clear_chat(self):
        """清空对话"""
        if messagebox.askyesno("确认", "确定要清空对话记录吗？"):
            self.chat_display.config(state=tk.NORMAL)
            self.chat_display.delete(1.0, tk.END)
            self.chat_display.config(state=tk.DISABLED)
            self.messages.clear()
            self._show_welcome_message()
    
    def _copy_chat(self):
        """复制对话"""
        chat_text = self.chat_display.get(1.0, tk.END)
        self.root.clipboard_clear()
        self.root.clipboard_append(chat_text)
        self.send_system_message("对话内容已复制到剪贴板")
    
    def _show_help(self):
        """显示帮助"""
        help_text = """🔧 使用说明

基本操作：
• 直接输入需求进行智能导航
• 使用快捷按钮进行常用操作
• 回车键发送消息

支持的命令：
• "分析页面" - 分析当前页面操作
• "查找 [功能名]" - 查找特定功能
• "导航到 [页面]" - 导航到指定页面
• "记录操作" - 记录当前操作
• "帮助" - 显示帮助信息
• "状态" - 显示系统状态

快捷键：
• Enter - 发送消息
• Ctrl+Enter - 换行

菜单功能：
• 文件 - 保存/导出记录
• 编辑 - 清空/复制对话
• 工具 - 快速操作
• 帮助 - 使用说明"""
        
        self._add_message("assistant", help_text, "text")
    
    def _show_about(self):
        """显示关于"""
        about_text = """🤖 AI+RPA 智能助手

版本: 1.0.0
开发: AI+RPA 团队

功能特点：
• 智能页面分析
• 自然语言导航
• 操作流程记录
• AI协助处理

技术支持：
• 基于 Playwright 的页面操作
• Gemini AI 智能分析
• 图形化交互界面
• 完整的操作记录

感谢使用！🎉"""
        
        self._add_message("assistant", about_text, "text")
    
    def _on_close(self):
        """窗口关闭事件"""
        if messagebox.askyesno("确认", "确定要退出吗？"):
            self.is_running = False
            if self.root:
                self.root.quit()
                self.root.destroy()
    
    def _generate_session_id(self) -> str:
        """生成会话ID"""
        import hashlib
        import time
        content = f"chat_session_{int(time.time())}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    def run(self):
        """运行聊天窗口"""
        try:
            self.is_running = True
            self.create_window()
            
            if self.root:
                self.root.mainloop()
                
        except Exception as e:
            logger.error(f"运行聊天窗口失败: {e}")
            raise
    
    def run_async(self):
        """异步运行聊天窗口"""
        def run_in_thread():
            self.run()
        
        thread = threading.Thread(target=run_in_thread, daemon=True)
        thread.start()
        return thread
