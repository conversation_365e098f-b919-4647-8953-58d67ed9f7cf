"""
断点续跑功能集成测试
"""
import os
import asyncio
import tempfile
import pytest
from datetime import datetime

from src.workflow.engine.workflow import Workflow, WorkflowContext, WorkflowStatus, Node, NodeType, Edge
from src.workflow.engine.executor import WorkflowExecutor
from src.workflow.engine.snapshot import SnapshotManager, FileSystemSnapshotStorage


class MockOperationExecutor:
    """模拟操作执行器，用于测试"""
    
    def __init__(self):
        self.counter = 0
        self.executed_nodes = set()
    
    async def execute_operation(self, operation_id, **params):
        """模拟操作执行"""
        self.executed_nodes.add(operation_id)
        
        if operation_id == "increment":
            self.counter += 1
            return self.counter
        elif operation_id == "fail_once":
            if not hasattr(self, "failed_once"):
                self.failed_once = True
                raise ValueError("Simulated failure")
            return "Recovered"
        elif operation_id == "long_running":
            await asyncio.sleep(2)  # 模拟长时间运行的任务
            return "Completed"
            
        return None


@pytest.fixture
def temp_snapshot_dir():
    """创建临时目录用于存储快照"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def workflow_fixture():
    """创建测试用的工作流"""
    # 创建工作流
    workflow = Workflow("test_workflow")
    workflow.context = WorkflowContext(
        execution_id=f"test_exec_{int(datetime.now().timestamp())}",
        variables={"counter": 0},
        status=WorkflowStatus.PENDING
    )
    
    # 添加开始节点
    start_node = Node("start", NodeType.START)
    workflow.add_node(start_node)
    
    # 添加任务节点
    task1 = Node("task1", NodeType.TASK, metadata={"operation_id": "increment"})
    task2 = Node("task2", NodeType.TASK, metadata={"operation_id": "increment"})
    task3 = Node("task3", NodeType.TASK, metadata={"operation_id": "increment"})
    
    workflow.add_node(task1)
    workflow.add_node(task2)
    workflow.add_node(task3)
    
    # 添加边
    workflow.add_edge(Edge(start_node.id, task1.id))
    workflow.add_edge(Edge(task1.id, task2.id))
    workflow.add_edge(Edge(task2.id, task3.id))
    
    return workflow


@pytest.mark.asyncio
async def test_workflow_snapshot_basic(workflow_fixture, temp_snapshot_dir):
    """测试基本的工作流快照功能"""
    # 设置快照管理器
    snapshot_manager = SnapshotManager(FileSystemSnapshotStorage(temp_snapshot_dir))
    
    # 创建模拟操作执行器
    op_executor = MockOperationExecutor()
    
    # 创建执行器
    executor = WorkflowExecutor(operation_executor=op_executor, auto_snapshot=True, snapshot_interval=1)
    
    # 启动工作流
    task = asyncio.create_task(executor.execute(workflow_fixture))
    
    try:
        # 等待工作流开始执行
        await asyncio.sleep(0.5)
        
        # 获取当前状态
        status = executor.get_execution_status()
        assert status["status"] == "running"
        
        # 手动创建快照
        snapshot_id = await executor.create_snapshot(workflow_fixture, note="test_snapshot")
        assert snapshot_id is not None
        
        # 验证快照存在
        snapshot = await snapshot_manager.load_snapshot(snapshot_id)
        assert snapshot is not None
        assert snapshot.workflow_id == "test_workflow"
        assert snapshot.execution_id == workflow_fixture.context.execution_id
        
        # 停止当前执行
        await executor.stop()
        await task
        
        # 验证部分执行结果
        assert op_executor.counter > 0
        
        # 从快照恢复
        new_executor = await WorkflowExecutor.resume_from_snapshot(snapshot_id, operation_executor=op_executor)
        
        # 验证恢复的状态
        assert new_executor.execution_id == workflow_fixture.context.execution_id
        
        # 等待工作流完成
        await asyncio.sleep(1)
        
        # 验证最终结果
        assert op_executor.counter == 3  # 应该执行了3次increment操作
        
    finally:
        # 确保任务被取消
        if not task.done():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass


@pytest.mark.asyncio
async def test_auto_snapshot(workflow_fixture, temp_snapshot_dir):
    """测试自动快照功能"""
    # 设置快照管理器
    snapshot_manager = SnapshotManager(FileSystemSnapshotStorage(temp_snapshot_dir))
    
    # 创建模拟操作执行器
    op_executor = MockOperationExecutor()
    
    # 创建执行器，启用自动快照，间隔1秒
    executor = WorkflowExecutor(operation_executor=op_executor, auto_snapshot=True, snapshot_interval=1)
    
    # 启动工作流
    task = asyncio.create_task(executor.execute(workflow_fixture))
    
    try:
        # 等待足够长的时间让自动快照触发
        await asyncio.sleep(1.5)
        
        # 获取快照列表
        snapshots = await snapshot_manager.list_snapshots(workflow_id="test_workflow")
        assert len(snapshots) > 0, "应该生成了自动快照"
        
        # 停止工作流
        await executor.stop()
        
    finally:
        if not task.done():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass


@pytest.mark.asyncio
async def test_resume_with_failure(workflow_fixture, temp_snapshot_dir):
    """测试从失败中恢复"""
    # 修改工作流，添加一个会失败的操作
    fail_node = Node("fail_node", NodeType.TASK, metadata={
        "operation_id": "fail_once",
        "retry": {
            "max_attempts": 2,
            "backoff_factor": 0.1
        }
    })
    workflow_fixture.add_node(fail_node)
    workflow_fixture.add_edge(Edge("task3", fail_node.id))
    
    # 设置快照管理器
    snapshot_manager = SnapshotManager(FileSystemSnapshotStorage(temp_snapshot_dir))
    
    # 创建模拟操作执行器
    op_executor = MockOperationExecutor()
    
    # 创建执行器
    executor = WorkflowExecutor(operation_executor=op_executor, auto_snapshot=True, snapshot_interval=0.5)
    
    # 启动工作流
    task = asyncio.create_task(executor.execute(workflow_fixture))
    
    try:
        # 等待工作流执行一段时间
        await asyncio.sleep(1)
        
        # 获取快照
        snapshots = await snapshot_manager.list_snapshots(workflow_id="test_workflow")
        assert len(snapshots) > 0, "应该生成了快照"
        
        # 停止当前执行
        await executor.stop()
        
        # 从最新的快照恢复
        latest_snapshot = snapshots[0]
        new_executor = await WorkflowExecutor.resume_from_snapshot(
            latest_snapshot.id, 
            operation_executor=op_executor
        )
        
        # 等待恢复的工作流完成
        await asyncio.sleep(2)
        
        # 验证操作已恢复并完成
        assert hasattr(op_executor, 'failed_once'), "应该已经处理了失败情况"
        
    finally:
        if not task.done():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
