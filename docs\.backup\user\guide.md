# 用户指南

**最后更新**：2025-05-30

## 目录

1. [快速开始](#快速开始)
2. [功能特性](#功能特性)
3. [工作流系统](#工作流系统)
   - [基本概念](#基本概念)
   - [工作流定义](#工作流定义)
   - [步骤类型](#步骤类型)
   - [条件执行](#条件执行)
   - [错误处理](#错误处理)
   - [变量系统](#变量系统)
   - [示例](#示例)
4. [工作流录制](#工作流录制)
   - [录制功能概述](#录制功能概述)
   - [录制命令参考](#录制命令参考)
   - [录制示例](#录制示例)
5. [自愈系统](#自愈系统)
   - [自愈定位器](#自愈定位器)
   - [定位策略](#定位策略)
   - [缓存机制](#缓存机制)
6. [性能优化](#性能优化)
   - [工作流优化](#工作流优化)
   - [自愈系统优化](#自愈系统优化)
7. [使用示例](#使用示例)
8. [常见问题](#常见问题)
9. [故障排除](#故障排除)
10. [最佳实践](#最佳实践)
11. [API参考](#api参考)
9. [联系我们](#联系我们)

## 快速开始

### 1. 安装

```bash
# 使用pip安装
pip install playwright-automation

# 安装Playwright浏览器
playwright install
```

### 2. 创建第一个测试

创建 `test_example.py`:

```python
from playwright.sync_api import Page, expect

def test_example(page: Page):
    # 导航到网页
    page.goto("https://example.com")
    
    # 断言页面标题
    expect(page).to_have_title("Example Domain")
    
    # 点击链接
    page.get_by_text("More information...").click()
    
    # 验证导航结果
    expect(page).to_have_url("https://www.iana.org/domains/reserved")
```

### 3. 运行测试

```bash
# 运行测试
pytest test_example.py -v

# 生成HTML报告
pytest --html=report.html
```

## 功能特性

### 1. 工作流系统

工作流系统是一个强大的自动化工具，允许您定义和执行复杂的工作流程。主要特性包括：

- **声明式工作流定义**：使用YAML或JSON定义工作流，易于理解和维护
- **多种步骤类型**：支持HTTP请求、文件操作、条件判断等多种步骤类型
- **条件执行**：支持if/else条件判断，实现复杂逻辑流程
- **错误处理**：内置重试机制和错误处理策略
- **变量系统**：支持变量引用和作用域管理
- **并行执行**：支持步骤的并行执行，提高效率
- **自愈能力**：自动检测和修复执行过程中的问题

### 2. 测试录制

```python
from playwright.sync_api import sync_playwright

def record_test():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(record_video_dir="videos/")
        page = context.new_page()
        
        # 开始录制
        page.pause()  # 打开Playwright Inspector
        
        # 在这里执行操作...
        page.goto("https://example.com")
        
        # 关闭浏览器
        context.close()
        browser.close()
```

### 2. 数据驱动测试

```python
import pytest

@pytest.mark.parametrize("username,password", [
    ("user1", "pass1"),
    ("user2", "pass2"),
])
def test_login(page, username, password):
    page.goto("https://example.com/login")
    page.fill("#username", username)
    page.fill("#password", password)
    page.click("button[type='submit']")
    assert "Welcome" in page.text_content("h1")
```

## 工作流系统

### 基本概念

工作流由一系列步骤(Step)组成，每个步骤执行特定的操作。工作流可以包含条件判断、循环等控制结构，支持变量和上下文传递。

### 工作流定义

工作流使用YAML或JSON格式定义，包含以下主要部分：

```yaml
name: 工作流名称
description: 工作流描述
version: 1.0.0
variables:  # 全局变量
  base_url: https://example.com
  username: testuser
steps:  # 步骤定义
  - id: step1
    type: log
    name: 记录日志
    parameters:
      message: "Hello, ${username}!"
      level: info
```

### 步骤类型

支持多种步骤类型，包括：

- **log**: 记录日志
- **http_get/http_post/...**: 发送HTTP请求
- **file_read/file_write**: 文件读写操作
- **condition**: 条件判断
- **switch**: 多条件分支
- **parallel**: 并行执行多个步骤
- **script**: 执行Python脚本

### 条件执行

使用`condition`步骤实现条件执行：

```yaml
- id: check_user
  type: condition
  parameters:
    condition: "${user.role} == 'admin'"
    then:
      - type: log
        parameters:
          message: "管理员用户，执行特权操作"
    else:
      - type: log
        parameters:
          message: "普通用户，执行标准操作"
```

### 错误处理

支持多种错误处理机制：

1. **重试机制**：
   ```yaml
   - id: retry_step
     type: http_get
     retry: 3  # 重试3次
     retry_delay: 1  # 每次重试间隔1秒
     parameters:
       url: "${base_url}/api"
   ```

2. **错误捕获**：
   ```yaml
   - id: safe_step
     type: http_get
     continue_on_error: true  # 出错时继续执行后续步骤
     parameters:
       url: "${base_url}/unstable-api"
   ```

### 变量系统

支持多种变量作用域：

1. **全局变量**：在工作流开始时定义，所有步骤可访问
2. **步骤输出**：每个步骤的输出可以保存为变量供后续步骤使用
3. **环境变量**：通过`${env.VAR_NAME}`访问系统环境变量
4. **临时变量**：在步骤执行过程中创建的临时变量

### 示例

#### 简单工作流示例

```python
from src.workflow import create_workflow_runner
import asyncio

async def main():
    workflow = {
        "name": "simple_workflow",
        "steps": [
            {
                "id": "step1",
                "type": "log",
                "parameters": {
                    "message": "Hello, Workflow!"
                }
            }
        ]
    }
    
    runner = await create_workflow_runner(workflow)
    result = await runner.run()
    print(f"Workflow completed: {result['success']}")

asyncio.run(main())
```

#### 复杂工作流示例

```python
from src.workflow import create_workflow_runner
import asyncio

async def main():
    workflow = {
        "name": "complex_workflow",
        "variables": {
            "base_url": "https://jsonplaceholder.typicode.com"
        },
        "steps": [
            {
                "id": "get_users",
                "type": "http_get",
                "parameters": {
                    "url": "${base_url}/users"
                }
            },
            {
                "id": "process_users",
                "type": "condition",
                "parameters": {
                    "condition": "${get_users.response.status} == 200",
                    "then": [
                        {
                            "type": "log",
                            "parameters": {
                                "message": "Found ${len(get_users.response.data)} users"
                            }
                        },
                        {
                            "type": "script",
                            "parameters": {
                                "code": """
                                # 处理用户数据
                                users = context.variables['get_users']['response']['data']
                                admin_users = [u for u in users if u.get('is_admin', False)]
                                return {'admin_count': len(admin_users)}
                                """
                            }
                        }
                    ],
                    "else": [
                        {
                            "type": "log",
                            "parameters": {
                                "message": "Failed to fetch users: ${get_users.response.status}",
                                "level": "error"
                            }
                        }
                    ]
                },
                "depends_on": ["get_users"]
            }
        ]
    }
    
    runner = await create_workflow_runner(workflow)
    result = await runner.run()
    print(f"Workflow completed: {result['success']}")

asyncio.run(main())
```

### 3. 页面对象模型

```python
class LoginPage:
    def __init__(self, page):
        self.page = page
        self.username = page.locator("#username")
        self.password = page.locator("#password")
        self.submit = page.locator("button[type='submit']")
    
    def login(self, username, password):
        self.username.fill(username)
        self.password.fill(password)
        self.submit.click()

def test_login_with_pom(page):
    login_page = LoginPage(page)
    login_page.login("testuser", "password123")
    assert page.url == "https://example.com/dashboard"
```

## 工作流录制

### 录制功能概述

工作流录制功能允许您将浏览器操作自动转换为可执行的工作流定义。录制器会捕获您的交互并生成相应的步骤，包括点击、输入、导航等操作。

### 录制命令参考

录制器支持以下命令：

| 命令 | 描述 | 示例 |
|------|------|------|
| `var <name>=<value>` | 定义变量 | `var username=testuser` |
| `wait <seconds>` | 添加等待步骤 | `wait 5` |
| `assert <condition>` | 添加断言 | `assert page.url == '...'` |
| `comment <text>` | 添加注释 | `comment 登录步骤` |
| `help` | 显示帮助信息 | `help` |
| `exit` 或 `quit` | 结束录制 | `exit` |

### 录制示例

```python
from src.recorder import WorkflowRecorder

# 启动录制器
recorder = WorkflowRecorder("my_workflow.json")

# 开始录制，指定起始URL
recorder.record(url="https://example.com")
```

录制完成后，您将获得一个包含所有步骤的JSON文件，可以在工作流运行器中执行。

## 自愈系统

### 自愈定位器

自愈定位器能够自动适应页面变化，提高测试的健壮性。它通过以下方式工作：

1. 维护一个定位策略的优先级列表
2. 当元素无法通过首选策略定位时，尝试备用策略
3. 记录成功的定位器供后续使用
4. 使用缓存提高性能

### 定位策略

自愈系统支持多种定位策略，按优先级排序：

1. **CSS选择器** - 通过CSS选择器定位元素
2. **XPath** - 使用XPath表达式定位
3. **文本匹配** - 通过元素文本内容定位
4. **测试ID** - 使用`data-testid`属性定位
5. **ARIA角色** - 通过ARIA角色定位
6. **标签文本** - 通过关联的标签文本定位
7. **占位符** - 通过输入框占位符定位
8. **标题** - 通过`title`属性定位
9. **替代文本** - 通过`alt`属性定位

### 缓存机制

自愈系统使用多级缓存来提高性能：

1. **内存缓存** - 存储最近使用的定位器
2. **持久化缓存** - 将定位器保存到文件，在多次运行间保持
3. **策略缓存** - 记录每种策略的成功率

```python
from src.workflow.self_healing import SelfHealingLocator

# 创建自愈定位器
locator = SelfHealingLocator(
    page,
    cache_enabled=True,
    cache_file="locator_cache.json"
)

# 查找元素
element = await locator.find_element("#login-button")
```

## 性能优化

### 工作流优化

1. **减少不必要的等待**
   - 使用智能等待而非固定等待
   - 仅在必要时添加显式等待

2. **并行执行**
   - 使用`parallel`步骤执行独立任务
   - 合理设置并行度，避免资源竞争

3. **变量管理**
   - 避免在循环中重复计算
   - 使用局部变量提高访问速度

4. **资源清理**
   - 及时关闭不再使用的页面和上下文
   - 定期清理临时文件

### 自愈系统优化

1. **缓存策略**
   - 启用持久化缓存减少重复计算
   - 调整缓存大小平衡内存使用和命中率

2. **策略选择**
   - 优先使用高性能定位策略
   - 禁用不常用的定位策略

3. **监控和调优**
   - 监控缓存命中率
   - 根据实际使用情况调整策略权重

## 使用示例

### 1. 元素操作

```python
# 点击元素
page.click("button#submit")


# 输入文本
page.fill("input#search", "Playwright")


# 选择下拉框
page.select_option("select#colors", "blue")

# 勾选复选框
page.check("input[type='checkbox']")

# 上传文件
page.set_input_files("input[type='file']", "example.txt")
```

### 2. 等待和断言

```python
# 等待元素可见
page.wait_for_selector(".success-message", state="visible")

# 等待导航完成
with page.expect_navigation():
    page.click("a.nav-link")

# 断言元素文本
assert "Success" in page.text_content(".status")

# 使用expect断言
expect(page.locator(".status")).to_have_text("Success")
```

### 3. 处理弹窗和对话框

```python
# 处理确认对话框
page.on("dialog", lambda dialog: dialog.accept())
page.click("button#delete")

# 处理提示框
page.on("dialog", lambda dialog: dialog.accept("John"))
page.click("button#prompt")
```

## 常见问题

### 1. 元素定位问题

**问题**: 无法找到元素

**解决方案**:
- 使用更稳定的选择器
- 添加等待
- 使用Playwright的调试工具

```python
# 调试选择器
page.pause()
```

### 2. 测试不稳定

**问题**: 测试有时通过，有时失败

**解决方案**:
- 添加显式等待
- 使用更可靠的定位器
- 增加超时时间

```python
# 显式等待元素
page.wait_for_selector("button.submit", state="visible", timeout=10000)
```

## 故障排除

### 1. 查看日志

```bash
# 启用调试日志
DEBUG=pw:api pytest test_example.py -v

# 查看浏览器日志
page.on("console", lambda msg: print(msg.text))
```

### 2. 常见错误

**错误**: `TimeoutError: Timeout 30000ms exceeded`

**解决方案**:
- 增加超时时间
- 检查网络连接
- 验证选择器是否正确

```python
# 增加超时时间
page.wait_for_selector(".element", timeout=60000)
```

## 最佳实践

### 1. 测试组织

```python
# tests/
#   ├── conftest.py
#   ├── test_login.py
#   ├── test_dashboard.py
#   └── pages/
#       ├── login_page.py
#       └── dashboard_page.py
```

### 2. 使用Fixture

```python
import pytest
from playwright.sync_api import Page, expect

@pytest.fixture
def login_page(page: Page):
    page.goto("https://example.com/login")
    return page

def test_successful_login(login_page):
    login_page.fill("#username", "testuser")
    login_page.fill("#password", "password123")
    login_page.click("button[type='submit']")
    expect(login_page).to_have_url("https://example.com/dashboard")
```

### 3. 并行执行

```bash
# 使用pytest-xdist并行运行测试
pytest -n 4  # 使用4个工作进程
```

## API参考

### 常用方法

| 方法 | 描述 | 示例 |
|------|------|------|
| `page.goto(url)` | 导航到指定URL | `page.goto("https://example.com")` |
| `page.click(selector)` | 点击元素 | `page.click("button.submit")` |
| `page.fill(selector, value)` | 填写表单 | `page.fill("#username", "user1")` |
| `page.wait_for_selector(selector)` | 等待元素出现 | `page.wait_for_selector(".success")` |
| `page.screenshot()` | 截取屏幕截图 | `page.screenshot(path="screenshot.png")` |

### 断言方法

| 断言 | 描述 | 示例 |
|------|------|------|
| `expect(page).to_have_title()` | 验证页面标题 | `expect(page).to_have_title("Example")` |
| `expect(page).to_have_url()` | 验证URL | `expect(page).to_have_url("example.com")` |
| `expect(locator).to_be_visible()` | 验证元素可见 | `expect(page.locator(".status")).to_be_visible()` |
| `expect(locator).to_have_text()` | 验证元素文本 | `expect(page.locator("h1")).to_have_text("Welcome")` |

## 联系我们

- 邮箱: <EMAIL>
- 文档: [官方文档](https://docs.example.com)
- GitHub: [问题跟踪](https://github.com/yourusername/playwright-automation/issues)
