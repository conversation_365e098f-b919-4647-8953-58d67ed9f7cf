# API文档

**版本**: v1.0 | **更新**: 2025-12-19

## 📋 API概览

AI+RPA系统提供RESTful API和Python SDK两种接口方式，支持完整的工作流自动化功能。

## 🚀 快速开始

### Python SDK
```python
from src.real_browser_use_integration import get_real_browser_use_agent

# 创建AI代理
agent = get_real_browser_use_agent()

# 执行AI任务
result = await agent.execute_user_request("请帮我打开example.com")
print(f"执行结果: {result['success']}")
```

### REST API (计划中)
```bash
# 执行AI任务
curl -X POST http://localhost:8080/api/v1/execute \
  -H "Content-Type: application/json" \
  -d '{"task": "请帮我打开example.com"}'
```

## 🔧 核心API

### 1. AI代理执行

#### execute_user_request()
执行用户自然语言请求

**参数**:
- `user_input` (str): 用户需求描述

**返回**:
```python
{
    "success": bool,
    "execution_method": "real_browser_use",
    "user_requirement": str,
    "parsed_intent": str,
    "business_domain": str,
    "confidence": float,
    "parameters_used": dict,
    "execution_time": float,
    "browser_use_result": any,
    "message": str,
    "timestamp": str
}
```

**示例**:
```python
result = await agent.execute_user_request(
    "在Google上搜索'AI自动化'相关信息"
)
```

### 2. 工作流执行

#### WorkflowEngine.execute_step()
执行单个工作流步骤

**参数**:
- `step` (Dict[str, Any]): 步骤定义
- `context` (ExecutionContext): 执行上下文

**返回**:
```python
{
    "status": "passed" | "failed" | "skipped",
    "result": any,
    "error": str,
    "execution_time": float
}
```

### 3. 监控和分析

#### BrowserUseMonitor.start_monitoring()
启动实时监控

**参数**:
- `page`: Playwright页面对象
- `operation_info` (Dict[str, Any]): 操作信息

**功能**:
- 页面状态监控
- 异常检测
- 自动恢复

### 4. AI分析

#### AIBusinessAnalyzer.analyze_user_requirement()
分析用户需求

**参数**:
- `user_input` (str): 用户输入

**返回**:
```python
{
    "original_text": str,
    "parsed_intent": str,
    "business_domain": str,
    "confidence": float,
    "extracted_entities": dict
}
```

## 📊 数据模型

### UserRequirement
```python
@dataclass
class UserRequirement:
    original_text: str
    parsed_intent: str
    business_domain: Optional[str]
    confidence: float
    extracted_entities: Dict[str, Any]
```

### WorkflowMatch
```python
@dataclass
class WorkflowMatch:
    workflow_id: str
    match_score: float
    required_parameters: Dict[str, Any]
    description: str
```

### Operation
```python
@dataclass
class Operation:
    type: str
    selector: Optional[str] = None
    value: Optional[str] = None
    wait_condition: Optional[str] = None
    timeout: int = 30000
```

## 🔒 认证和授权

### API密钥配置
```python
import os
os.environ['OPENAI_API_KEY'] = 'your-api-key-here'
```

### 权限控制 (计划中)
- 用户认证
- 角色权限
- 操作审计

## 📈 监控和日志

### 执行统计
```python
# 获取执行统计
stats = agent.get_execution_statistics()
print(f"成功率: {stats['success_rate']:.1f}%")
```

### 执行历史
```python
# 获取执行历史
history = agent.get_execution_history()
for entry in history[-5:]:  # 最近5次执行
    print(f"{entry['timestamp']}: {entry['success']}")
```

## 🚨 错误处理

### 错误类型
- `WorkflowError`: 工作流执行错误
- `OperationError`: 操作执行错误
- `ValidationError`: 参数验证错误
- `TimeoutError`: 超时错误

### 错误响应格式
```python
{
    "success": false,
    "error": "错误描述",
    "error_type": "WorkflowError",
    "error_code": "WORKFLOW_001",
    "timestamp": "2025-12-19T10:30:00Z"
}
```

## 🔧 配置选项

### 代理配置
```python
agent = RealBrowserUseAgent(
    llm_model="gpt-4o",          # LLM模型
    max_steps=10,                # 最大执行步数
    save_conversation=True       # 保存对话历史
)
```

### 浏览器配置
```python
browser_config = {
    "headless": False,           # 是否无头模式
    "timeout": 30000,           # 默认超时时间
    "viewport": {"width": 1280, "height": 720}
}
```

## 📚 SDK参考

### 安装
```bash
pip install -r requirements.txt
pip install browser-use
```

### 导入
```python
from src.real_browser_use_integration import get_real_browser_use_agent
from src.ai_intelligent_interaction import get_interaction_manager
from src.workflow.engine import WorkflowEngine
```

### 完整示例
```python
import asyncio
from src.real_browser_use_integration import get_real_browser_use_agent

async def main():
    # 创建代理
    agent = get_real_browser_use_agent()
    
    # 执行任务
    result = await agent.execute_user_request(
        "请帮我在GitHub上搜索browser-use项目"
    )
    
    # 处理结果
    if result['success']:
        print(f"✅ 任务执行成功")
        print(f"执行时间: {result['execution_time']:.2f}秒")
    else:
        print(f"❌ 任务执行失败: {result['error']}")

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔄 版本兼容性

### 当前版本: v1.0
- 支持基础AI代理功能
- 支持工作流执行
- 支持实时监控

### 计划版本: v1.1
- REST API支持
- Web界面集成
- 批量执行功能

---

> 📖 **更多信息**: 查看[开发指南](../development/README.md)了解详细的开发说明
