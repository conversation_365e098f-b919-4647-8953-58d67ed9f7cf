"""
AI+RPA npm安装和启动助手

专门处理npm安装问题并启动应用
"""
import subprocess
import sys
import os
import time
from pathlib import Path


def check_node():
    """检查Node.js"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✅ Node.js版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js不可用")
            return False
    except Exception as e:
        print(f"❌ 检查Node.js失败: {e}")
        return False


def check_npm():
    """检查npm"""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✅ npm版本: {result.stdout.strip()}")
            return True
        else:
            print("⚠️ npm命令执行失败")
            return False
    except Exception as e:
        print(f"⚠️ npm检查失败: {e}")
        return False


def install_npm_via_node():
    """尝试通过Node.js安装npm"""
    print("🔧 尝试安装npm...")
    
    try:
        # 方法1: 使用Node.js内置的npm安装
        print("方法1: 使用corepack启用npm...")
        result = subprocess.run(['corepack', 'enable'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print("✅ corepack启用成功")
            return check_npm()
        
        # 方法2: 下载npm
        print("方法2: 尝试下载npm...")
        result = subprocess.run([
            'powershell', '-Command',
            'Invoke-WebRequest -Uri "https://registry.npmjs.org/npm/-/npm-latest.tgz" -OutFile "npm.tgz"'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ npm下载成功")
            # 这里需要解压和安装，比较复杂
            
    except Exception as e:
        print(f"❌ npm安装失败: {e}")
    
    return False


def provide_manual_solution():
    """提供手动解决方案"""
    print("\n🛠️ 手动解决方案:")
    print("=" * 50)
    print("1. 重新安装Node.js (推荐方案):")
    print("   - 访问: https://nodejs.org/")
    print("   - 下载LTS版本 (长期支持版)")
    print("   - 运行安装程序")
    print("   - ⚠️ 重要: 安装时勾选 'Add to PATH' 选项")
    print("   - 重启命令行窗口")
    
    print("\n2. 使用Chocolatey安装:")
    print("   - 以管理员身份运行PowerShell")
    print("   - 运行: choco install nodejs")
    
    print("\n3. 使用Scoop安装:")
    print("   - 运行: scoop install nodejs")
    
    print("\n4. 手动添加到PATH:")
    print("   - 找到Node.js安装目录 (通常在 C:\\Program Files\\nodejs\\)")
    print("   - 将该目录添加到系统PATH环境变量")
    
    print("\n5. 验证安装:")
    print("   - 重启命令行")
    print("   - 运行: node --version")
    print("   - 运行: npm --version")


def try_alternative_package_managers():
    """尝试其他包管理器"""
    print("🔍 寻找替代包管理器...")
    
    # 检查yarn
    try:
        result = subprocess.run(['yarn', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✅ 找到yarn版本: {result.stdout.strip()}")
            return 'yarn'
    except:
        pass
    
    # 检查pnpm
    try:
        result = subprocess.run(['pnpm', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✅ 找到pnpm版本: {result.stdout.strip()}")
            return 'pnpm'
    except:
        pass
    
    print("❌ 未找到可用的包管理器")
    return None


def install_frontend_deps_with_alternative(package_manager):
    """使用替代包管理器安装前端依赖"""
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    try:
        print(f"📦 使用{package_manager}安装前端依赖...")
        result = subprocess.run([package_manager, 'install'], cwd=frontend_dir, shell=True)
        if result.returncode == 0:
            print("✅ 前端依赖安装成功")
            return True
        else:
            print("❌ 前端依赖安装失败")
            return False
    except Exception as e:
        print(f"❌ 安装失败: {e}")
        return False


def start_frontend_with_alternative(package_manager):
    """使用替代包管理器启动前端"""
    frontend_dir = Path("frontend")
    
    try:
        print(f"🚀 使用{package_manager}启动前端...")
        
        if package_manager == 'yarn':
            cmd = ['yarn', 'start']
        elif package_manager == 'pnpm':
            cmd = ['pnpm', 'start']
        else:
            cmd = ['npm', 'start']
        
        # 设置环境变量
        env = os.environ.copy()
        env['BROWSER'] = 'none'  # 不自动打开浏览器
        
        process = subprocess.Popen(cmd, cwd=frontend_dir, env=env, shell=True)
        print("✅ 前端服务启动成功")
        return process
        
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        return None


def start_backend_only():
    """只启动后端服务"""
    print("🚀 启动后端API服务...")
    
    backend_dir = Path("backend_api")
    if not backend_dir.exists():
        print("❌ backend_api目录不存在")
        return None
    
    try:
        # 安装Python依赖
        print("📦 安装Python依赖...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'fastapi', 'uvicorn', 'python-multipart'], check=True)
        
        # 启动FastAPI服务
        process = subprocess.Popen([
            sys.executable, '-m', 'uvicorn', 'main:app',
            '--host', '0.0.0.0',
            '--port', '8000',
            '--reload'
        ], cwd=backend_dir)
        
        print("✅ 后端服务启动成功 (http://localhost:8000)")
        return process
        
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return None


def main():
    """主函数"""
    print("🤖 AI+RPA npm安装和启动助手")
    print("=" * 50)
    
    # 检查Node.js
    if not check_node():
        print("❌ Node.js未安装或不可用")
        provide_manual_solution()
        return
    
    # 检查npm
    if not check_npm():
        print("⚠️ npm不可用，尝试解决...")
        
        # 尝试安装npm
        if not install_npm_via_node():
            # 寻找替代包管理器
            alt_pm = try_alternative_package_managers()
            
            if alt_pm:
                print(f"💡 使用{alt_pm}作为替代方案")
                
                # 安装前端依赖
                if install_frontend_deps_with_alternative(alt_pm):
                    # 启动服务
                    backend_process = start_backend_only()
                    frontend_process = start_frontend_with_alternative(alt_pm)
                    
                    if backend_process and frontend_process:
                        print("\n🎉 AI+RPA平台启动成功！")
                        print("📱 前端应用: http://localhost:3000")
                        print("🔧 后端API: http://localhost:8000")
                        print("\n按 Ctrl+C 停止服务...")
                        
                        try:
                            while True:
                                time.sleep(1)
                        except KeyboardInterrupt:
                            print("\n👋 正在停止服务...")
                            try:
                                backend_process.terminate()
                                frontend_process.terminate()
                            except:
                                pass
                    else:
                        print("❌ 服务启动失败")
                else:
                    print("❌ 前端依赖安装失败")
                    provide_manual_solution()
            else:
                print("❌ 未找到可用的包管理器")
                provide_manual_solution()
    else:
        print("✅ npm可用，可以运行完整启动脚本")
        print("💡 请运行: python start_ai_rpa_app.py")


if __name__ == "__main__":
    main()
