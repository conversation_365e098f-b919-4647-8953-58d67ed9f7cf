"""
并行工作流示例程序

展示如何在工作流中使用并行执行控制器来处理并行任务。
"""

import asyncio
import logging
from datetime import datetime
from src.workflow.engine.parallel import ParallelExecutionController

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def simulate_web_task(name: str, delay: float) -> dict:
    """
    模拟一个Web任务
    
    Args:
        name: 任务名称
        delay: 模拟的执行时间
    
    Returns:
        任务执行结果
    """
    logger.info(f"开始执行Web任务: {name}")
    await asyncio.sleep(delay)
    
    if name == "error_task":
        raise ValueError(f"任务 {name} 执行失败")
    
    logger.info(f"完成Web任务: {name}")
    return {
        "name": name,
        "execution_time": delay,
        "timestamp": datetime.now().isoformat()
    }

async def run_parallel_workflow():
    """运行并行工作流示例"""
    try:
        # 创建并行执行控制器
        controller = ParallelExecutionController(
            max_concurrent=3,  # 最大并行数
            fail_fast=True     # 快速失败模式
        )
        
        # 定义并行任务
        tasks = {
            "login": simulate_web_task("login", 0.5),
            "load_profile": simulate_web_task("load_profile", 1.0),
            "error_task": simulate_web_task("error_task", 0.3),
            "load_settings": simulate_web_task("load_settings", 0.8)
        }
        
        logger.info("开始执行并行工作流")
        start_time = datetime.now()
        
        try:
            # 执行所有任务
            results = await controller.execute_all(tasks)
            logger.info("所有任务执行成功")
            logger.info(f"执行结果: {results}")
            
        except Exception as e:
            logger.error(f"工作流执行失败: {str(e)}")
            
            # 获取失败的分支信息
            failed_branches = controller.get_failed_branches()
            logger.error(f"失败的分支: {failed_branches}")
            
            # 获取已完成的分支信息
            completed_branches = controller.get_completed_branches()
            logger.info(f"已完成的分支: {completed_branches}")
            
            # 获取被取消的分支
            all_branches = controller.get_all_branch_info()
            cancelled_branches = [
                branch_id
                for branch_id, info in all_branches.items()
                if info.status == "cancelled"
            ]
            logger.info(f"被取消的分支: {cancelled_branches}")
        
        finally:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"工作流执行时间: {duration:.2f} 秒")
            
            # 输出所有分支的详细信息
            all_branches = controller.get_all_branch_info()
            for branch_id, info in all_branches.items():
                logger.info(f"\n分支 {branch_id} 信息:")
                logger.info(f"  状态: {info.status}")
                logger.info(f"  开始时间: {info.start_time}")
                logger.info(f"  结束时间: {info.end_time}")
                if info.error:
                    logger.info(f"  错误信息: {str(info.error)}")
                if info.result:
                    logger.info(f"  执行结果: {info.result}")
    
    except Exception as e:
        logger.error(f"示例程序执行错误: {str(e)}")
        raise

async def main():
    """主函数"""
    await run_parallel_workflow()

if __name__ == "__main__":
    # 运行示例程序
    asyncio.run(main()) 