"""
操作重试和超时集成测试

测试操作执行器中的重试和超时功能。
"""
import asyncio
import time
import pytest
from pathlib import Path
from unittest.mock import patch, MagicMock, AsyncMock

from playwright.async_api import async_playwright

# 添加项目根目录到Python路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.workflow.operations.executor import OperationExecutor
from src.workflow.operations.operations import (
    ClickOperation, FillOperation, WaitOperation, ExtractOperation, NavigateOperation
)
from src.workflow.operations.base import ElementSelector
from src.workflow.operations.exceptions import OperationError, OperationTimeoutError

# 测试页面路径
TEST_HTML = Path(__file__).parent.parent / "test_data" / "test_page.html"

class MockFailingOperation:
    """模拟会失败的操作"""
    def __init__(self, fail_times=0, success_after=None, exception=Exception("Test error"), 
                 id=None, timeout=None, max_retries=3, retry_delay=0.1):
        self.fail_times = fail_times
        self.success_after = success_after
        self.exception = exception
        self.attempts = 0
        self.id = id or f"mock_operation_{id(self)}"
        self.type = "mock"
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.retry_on = (Exception,)
        self.timeout = timeout
        self.continue_on_failure = False
        self.parameters = {}
        self.wait_conditions = []
        self.metadata = {}
    
    async def execute(self, *args, **kwargs):
        self.attempts += 1
        if self.timeout and self.attempts == 1:
            await asyncio.sleep(self.timeout * 1.5)  # 确保超时
        if self.success_after is not None and self.attempts > self.success_after:
            return "success"
        if self.attempts <= self.fail_times:
            raise self.exception
        return "success"
    
    def to_dict(self):
        return {
            "id": self.id,
            "type": self.type,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "timeout": self.timeout
        }

@pytest.fixture
def event_loop():
    """为测试创建事件循环"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()

@pytest.fixture
async def browser():
    """启动Playwright浏览器"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        yield browser
        await browser.close()

@pytest.fixture
async def page(browser):
    """创建测试页面"""
    page = await browser.new_page()
    await page.goto(f"file://{TEST_HTML}")
    return page

@pytest.fixture
def executor(page):
    """创建操作执行器"""
    return OperationExecutor(page, default_timeout=2.0)

@pytest.mark.asyncio
async def test_retry_mechanism(executor, page):
    """测试重试机制"""
    # 创建一个会失败2次然后成功的操作
    operation = MockFailingOperation(fail_times=2, max_retries=3, retry_delay=0.1)
    
    # 执行操作
    result = await executor.execute_operation(operation)
    
    # 验证结果
    assert result == "success"
    assert operation.attempts == 3  # 初始尝试 + 2次重试

@pytest.mark.asyncio
async def test_retry_exhausted(executor, page):
    """测试重试次数用尽"""
    # 创建一个总是失败的操作
    operation = MockFailingOperation(fail_times=10, max_retries=2, retry_delay=0.1)
    
    # 执行操作，应该抛出异常
    with pytest.raises(OperationError) as exc_info:
        await executor.execute_operation(operation)
    
    # 验证重试次数
    assert operation.attempts == 3  # 初始尝试 + 2次重试
    assert "Max retries (2) exceeded" in str(exc_info.value)

@pytest.mark.asyncio
async def test_operation_timeout(executor, page):
    """测试操作超时"""
    # 创建一个会超时的操作
    operation = MockFailingOperation(timeout=1.0, max_retries=0)
    
    # 执行操作，应该抛出超时异常
    with pytest.raises(OperationTimeoutError):
        await executor.execute_operation(operation)

@pytest.mark.asyncio
async def test_retry_after_timeout(executor, page):
    """测试超时后的重试"""
    # 创建一个第一次会超时，然后成功的操作
    operation = MockFailingOperation(
        success_after=1,  # 第二次成功
        timeout=1.0,      # 1秒超时
        max_retries=3,    # 最多重试3次
        retry_delay=0.1   # 重试延迟0.1秒
    )
    
    # 执行操作
    result = await executor.execute_operation(operation)
    
    # 验证结果
    assert result == "success"
    assert operation.attempts == 2  # 初始尝试(超时) + 1次重试(成功)

@pytest.mark.asyncio
async def test_operation_sequence_with_retry(executor, page):
    """测试带重试的操作序列"""
    # 创建多个操作，其中一些会失败
    operations = [
        MockFailingOperation(id="op1", fail_times=1, max_retries=2, retry_delay=0.1),
        MockFailingOperation(id="op2", fail_times=2, max_retries=3, retry_delay=0.1),
        MockFailingOperation(id="op3", fail_times=0, max_retries=2, retry_delay=0.1)
    ]
    
    # 执行操作序列
    results = []
    for op in operations:
        result = await executor.execute_operation(op)
        results.append((op.id, result))
    
    # 验证所有操作都成功执行
    assert len(results) == 3
    for op_id, result in results:
        assert result == "success", f"Operation {op_id} failed"
    
    # 验证重试次数
    assert operations[0].attempts == 2  # 失败1次，重试1次
    assert operations[1].attempts == 3  # 失败2次，重试2次
    assert operations[2].attempts == 1  # 没有失败，不需要重试
