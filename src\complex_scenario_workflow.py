"""
复杂场景工作流系统

实现协助登录 → 页面分析 → 操作数据生成 → 智能导航的完整流程
"""
import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

from login_state_manager import get_login_state_manager
from ai_login_workflow_generator import get_login_state_executor
from page_operation_analyzer import get_page_operation_analyzer, get_page_operation_manager
from intelligent_page_navigator import get_intelligent_page_navigator

logger = logging.getLogger(__name__)


@dataclass
class ComplexScenarioSession:
    """复杂场景会话"""
    session_id: str
    name: str
    description: str
    login_session_id: str           # 关联的登录会话ID
    initial_page_data: Dict         # 初始页面操作数据
    navigation_history: List[Dict]  # 导航历史
    created_at: str
    last_used: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ComplexScenarioSession':
        return cls(**data)


class ComplexScenarioWorkflow:
    """复杂场景工作流"""
    
    def __init__(self):
        self.login_manager = get_login_state_manager()
        self.login_executor = get_login_state_executor()
        self.page_analyzer = get_page_operation_analyzer()
        self.page_manager = get_page_operation_manager()
        self.navigator = get_intelligent_page_navigator()
        
        self.current_session = None
        self.current_page_data = None
        self.current_page = None
        self.current_browser = None
    
    async def start_complex_scenario(
        self, 
        session_name: str, 
        login_session_id: str = None,
        description: str = ""
    ) -> Dict[str, Any]:
        """启动复杂场景"""
        try:
            logger.info(f"启动复杂场景: {session_name}")
            
            # 步骤1: 协助登录
            login_result = await self._assisted_login(login_session_id)
            
            if not login_result["success"]:
                return {
                    "success": False,
                    "step": "login",
                    "message": f"登录失败: {login_result['message']}",
                    "error": login_result.get("error", "")
                }
            
            # 步骤2: 分析当前页面
            analysis_result = await self._analyze_current_page()
            
            if not analysis_result["success"]:
                return {
                    "success": False,
                    "step": "analysis",
                    "message": f"页面分析失败: {analysis_result['message']}",
                    "error": analysis_result.get("error", "")
                }
            
            # 步骤3: 创建复杂场景会话
            session_result = await self._create_scenario_session(
                session_name, 
                login_result["login_session_id"], 
                analysis_result["page_data"],
                description
            )
            
            return {
                "success": True,
                "message": "复杂场景启动成功",
                "scenario_session_id": session_result["session_id"],
                "login_session_id": login_result["login_session_id"],
                "current_url": login_result["current_url"],
                "current_title": login_result["current_title"],
                "page_analysis": {
                    "links_count": len(analysis_result["page_data"]["links"]),
                    "functional_areas": analysis_result["page_data"]["functional_areas"],
                    "ai_summary": analysis_result["page_data"]["ai_summary"]
                },
                "available_operations": [
                    {
                        "text": link["text"],
                        "category": link["category"],
                        "description": link["description"],
                        "priority": link["priority"]
                    }
                    for link in analysis_result["page_data"]["links"][:10]
                ]
            }
            
        except Exception as e:
            logger.error(f"启动复杂场景失败: {e}")
            return {
                "success": False,
                "step": "unknown",
                "message": f"启动失败: {e}",
                "error": str(e)
            }
    
    async def navigate_by_requirement(self, user_requirement: str) -> Dict[str, Any]:
        """根据用户要求导航"""
        try:
            if not self.current_page_data or not self.current_page:
                return {
                    "success": False,
                    "message": "当前没有活跃的复杂场景会话"
                }
            
            logger.info(f"根据要求导航: {user_requirement}")
            
            # 使用智能导航器
            from page_operation_analyzer import PageOperationData
            page_data = PageOperationData.from_dict(self.current_page_data)
            
            navigation_result = await self.navigator.auto_navigate_by_requirement(
                self.current_page, 
                page_data, 
                user_requirement
            )
            
            # 记录导航历史
            if self.current_session:
                navigation_record = {
                    "timestamp": datetime.now().isoformat(),
                    "user_requirement": user_requirement,
                    "navigation_result": navigation_result,
                    "success": navigation_result["success"]
                }
                
                self.current_session.navigation_history.append(navigation_record)
                self.current_session.last_used = datetime.now().isoformat()
            
            # 如果导航成功，更新当前页面数据
            if navigation_result["success"] and navigation_result.get("new_page_data"):
                self.current_page_data = navigation_result["new_page_data"]
            
            return navigation_result
            
        except Exception as e:
            logger.error(f"根据要求导航失败: {e}")
            return {
                "success": False,
                "message": f"导航失败: {e}",
                "error": str(e)
            }
    
    async def get_current_page_operations(self) -> Dict[str, Any]:
        """获取当前页面操作"""
        try:
            if not self.current_page_data:
                return {
                    "success": False,
                    "message": "当前没有页面数据"
                }
            
            return {
                "success": True,
                "page_data": self.current_page_data,
                "operations_summary": {
                    "total_links": len(self.current_page_data["links"]),
                    "categories": list(set(link["category"] for link in self.current_page_data["links"])),
                    "high_priority_links": [
                        link for link in self.current_page_data["links"] 
                        if link["priority"] >= 7
                    ][:5]
                }
            }
            
        except Exception as e:
            logger.error(f"获取当前页面操作失败: {e}")
            return {
                "success": False,
                "message": f"获取失败: {e}",
                "error": str(e)
            }
    
    async def refresh_page_analysis(self) -> Dict[str, Any]:
        """刷新页面分析"""
        try:
            if not self.current_page:
                return {
                    "success": False,
                    "message": "当前没有活跃的页面"
                }
            
            logger.info("刷新页面分析")
            
            # 重新分析当前页面
            analysis_result = await self._analyze_current_page()
            
            if analysis_result["success"]:
                self.current_page_data = analysis_result["page_data"]
                
                return {
                    "success": True,
                    "message": "页面分析已刷新",
                    "page_data": self.current_page_data,
                    "changes": {
                        "links_count": len(self.current_page_data["links"]),
                        "new_functional_areas": self.current_page_data["functional_areas"]
                    }
                }
            else:
                return analysis_result
            
        except Exception as e:
            logger.error(f"刷新页面分析失败: {e}")
            return {
                "success": False,
                "message": f"刷新失败: {e}",
                "error": str(e)
            }
    
    async def close_scenario(self) -> Dict[str, Any]:
        """关闭复杂场景"""
        try:
            logger.info("关闭复杂场景")
            
            # 保存会话数据
            if self.current_session:
                # 这里可以保存会话到文件
                pass
            
            # 关闭浏览器
            if self.current_browser:
                await self.current_browser.close()
            
            # 清理状态
            self.current_session = None
            self.current_page_data = None
            self.current_page = None
            self.current_browser = None
            
            return {
                "success": True,
                "message": "复杂场景已关闭"
            }
            
        except Exception as e:
            logger.error(f"关闭复杂场景失败: {e}")
            return {
                "success": False,
                "message": f"关闭失败: {e}",
                "error": str(e)
            }
    
    async def _assisted_login(self, login_session_id: str = None) -> Dict[str, Any]:
        """协助登录"""
        try:
            if login_session_id:
                # 使用指定的登录会话
                result = await self.login_executor.execute_with_cached_login(
                    session_id=login_session_id,
                    headless=False
                )
                
                if result["success"]:
                    self.current_page = result["page"]
                    self.current_browser = result["browser"]
                    
                    return {
                        "success": True,
                        "message": "使用缓存登录成功",
                        "login_session_id": login_session_id,
                        "current_url": result["current_url"],
                        "current_title": result.get("current_title", ""),
                        "method": "cached_login"
                    }
                else:
                    return {
                        "success": False,
                        "message": f"缓存登录失败: {result['error']}",
                        "error": result["error"]
                    }
            else:
                # 列出可用的登录会话供用户选择
                sessions = await self.login_executor.list_available_sessions()
                valid_sessions = [s for s in sessions if s["is_valid"]]
                
                if not valid_sessions:
                    return {
                        "success": False,
                        "message": "没有可用的登录会话，请先完成登录",
                        "available_sessions": []
                    }
                
                # 使用最近使用的会话
                latest_session = valid_sessions[0]
                return await self._assisted_login(latest_session["session_id"])
            
        except Exception as e:
            logger.error(f"协助登录失败: {e}")
            return {
                "success": False,
                "message": f"登录失败: {e}",
                "error": str(e)
            }
    
    async def _analyze_current_page(self) -> Dict[str, Any]:
        """分析当前页面"""
        try:
            if not self.current_page:
                return {
                    "success": False,
                    "message": "当前没有活跃的页面"
                }
            
            logger.info("分析当前页面")
            
            # 使用页面操作分析器
            operation_data = await self.page_analyzer.analyze_page_operations(self.current_page)
            
            # 保存页面操作数据
            filepath = await self.page_manager.save_page_operations(operation_data)
            
            return {
                "success": True,
                "message": "页面分析完成",
                "page_data": operation_data.to_dict(),
                "data_filepath": filepath
            }
            
        except Exception as e:
            logger.error(f"分析当前页面失败: {e}")
            return {
                "success": False,
                "message": f"页面分析失败: {e}",
                "error": str(e)
            }
    
    async def _create_scenario_session(
        self, 
        session_name: str, 
        login_session_id: str, 
        page_data: Dict,
        description: str
    ) -> Dict[str, Any]:
        """创建复杂场景会话"""
        try:
            import hashlib
            import time
            
            # 生成会话ID
            session_id = hashlib.md5(f"{session_name}_{int(time.time())}".encode()).hexdigest()[:16]
            
            # 创建会话对象
            session = ComplexScenarioSession(
                session_id=session_id,
                name=session_name,
                description=description,
                login_session_id=login_session_id,
                initial_page_data=page_data,
                navigation_history=[],
                created_at=datetime.now().isoformat(),
                last_used=datetime.now().isoformat()
            )
            
            self.current_session = session
            
            return {
                "success": True,
                "session_id": session_id,
                "message": "复杂场景会话创建成功"
            }
            
        except Exception as e:
            logger.error(f"创建复杂场景会话失败: {e}")
            return {
                "success": False,
                "message": f"创建会话失败: {e}",
                "error": str(e)
            }


# 全局实例
global_complex_scenario_workflow = ComplexScenarioWorkflow()


def get_complex_scenario_workflow() -> ComplexScenarioWorkflow:
    """获取复杂场景工作流实例"""
    return global_complex_scenario_workflow
