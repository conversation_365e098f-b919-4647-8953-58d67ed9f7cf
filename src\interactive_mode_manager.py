"""
交互模式管理器

整合聊天窗口、命令处理和复杂场景工作流
"""
import asyncio
import logging
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from interactive_chat_window import InteractiveChatWindow, InteractionRecord
from intelligent_command_processor import get_intelligent_command_processor, CommandType
from complex_scenario_workflow import get_complex_scenario_workflow
from ai_login_workflow_generator import get_login_state_executor

logger = logging.getLogger(__name__)


class InteractiveModeManager:
    """交互模式管理器"""
    
    def __init__(self):
        self.chat_window = None
        self.command_processor = get_intelligent_command_processor()
        self.scenario_workflow = get_complex_scenario_workflow()
        self.login_executor = get_login_state_executor()
        
        # 状态管理
        self.is_running = False
        self.current_scenario = None
        self.operation_records = []
        self.session_start_time = None
        
        # 存储目录
        self.records_dir = Path("interaction_records")
        self.records_dir.mkdir(exist_ok=True)
    
    async def start_interactive_mode(self, scenario_name: str = "交互式操作", login_session_id: str = None):
        """启动交互模式"""
        try:
            logger.info("启动交互模式")
            
            # 创建聊天窗口
            self.chat_window = InteractiveChatWindow(
                title=f"AI+RPA 智能助手 - {scenario_name}",
                width=700,
                height=800
            )
            
            # 设置消息处理器
            self.chat_window.set_message_handler(self._handle_user_message)
            
            # 启动聊天窗口（异步）
            chat_thread = self.chat_window.run_async()
            
            # 等待窗口创建
            await asyncio.sleep(2)
            
            # 发送启动消息
            self.chat_window.send_system_message("🚀 交互模式启动中...")
            
            # 检查登录状态
            login_result = await self._check_login_status(login_session_id)
            
            if login_result["success"]:
                # 启动复杂场景
                scenario_result = await self._start_scenario(scenario_name, login_result["session_id"])
                
                if scenario_result["success"]:
                    self.current_scenario = scenario_result
                    self.session_start_time = datetime.now()
                    self.is_running = True
                    
                    # 发送成功消息
                    success_msg = f"""✅ 交互模式启动成功！

🔐 登录状态: 已连接 ({login_result['session_name']})
📱 当前页面: {scenario_result['current_title']}
🔗 页面链接: {scenario_result['page_analysis']['links_count']} 个
🎯 AI摘要: {scenario_result['page_analysis']['ai_summary']}

💡 您现在可以：
• 输入需求进行智能导航，如："我要查看用户管理"
• 使用命令进行操作，如："分析页面"、"记录操作"
• 点击快捷按钮进行常用操作

开始您的智能操作之旅吧！🎉"""
                    
                    self.chat_window.send_response(success_msg)
                    
                    # 启动消息处理循环
                    await self._message_processing_loop()
                    
                else:
                    self.chat_window.send_error_message(f"启动场景失败: {scenario_result['message']}")
            else:
                self.chat_window.send_error_message(f"登录检查失败: {login_result['message']}")
            
        except Exception as e:
            logger.error(f"启动交互模式失败: {e}")
            if self.chat_window:
                self.chat_window.send_error_message(f"启动失败: {e}")
    
    async def _check_login_status(self, login_session_id: str = None) -> Dict[str, Any]:
        """检查登录状态"""
        try:
            # 获取可用的登录会话
            sessions = await self.login_executor.list_available_sessions()
            valid_sessions = [s for s in sessions if s["is_valid"]]
            
            if not valid_sessions:
                return {
                    "success": False,
                    "message": "没有可用的登录会话，请先完成登录"
                }
            
            # 选择登录会话
            if login_session_id:
                selected_session = next((s for s in valid_sessions if s["session_id"] == login_session_id), None)
                if not selected_session:
                    selected_session = valid_sessions[0]
            else:
                selected_session = valid_sessions[0]
            
            return {
                "success": True,
                "session_id": selected_session["session_id"],
                "session_name": selected_session["name"],
                "domain": selected_session["domain"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"检查登录状态失败: {e}"
            }
    
    async def _start_scenario(self, scenario_name: str, login_session_id: str) -> Dict[str, Any]:
        """启动复杂场景"""
        try:
            result = await self.scenario_workflow.start_complex_scenario(
                session_name=scenario_name,
                login_session_id=login_session_id,
                description="交互式操作场景"
            )
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "message": f"启动场景失败: {e}"
            }
    
    async def _message_processing_loop(self):
        """消息处理循环"""
        try:
            while self.is_running and self.chat_window and self.chat_window.is_running:
                # 检查用户消息
                user_message = self.chat_window.get_user_message()
                
                if user_message:
                    await self._handle_user_message(user_message)
                
                # 短暂休眠避免CPU占用过高
                await asyncio.sleep(0.1)
                
        except Exception as e:
            logger.error(f"消息处理循环异常: {e}")
            if self.chat_window:
                self.chat_window.send_error_message(f"消息处理异常: {e}")
    
    async def _handle_user_message(self, message: str):
        """处理用户消息"""
        try:
            logger.info(f"处理用户消息: {message}")
            
            # 记录用户输入
            self._record_operation("user_input", {"message": message})
            
            # 使用智能命令处理器分析命令
            command_result = await self.command_processor.process_command(message)
            
            # 根据命令类型执行相应操作
            if command_result.success:
                response = await self._execute_command(command_result)
            else:
                response = command_result.response
            
            # 发送响应
            if self.chat_window:
                self.chat_window.send_response(response)
            
            # 记录处理结果
            self._record_operation("command_processed", {
                "command_result": command_result.to_dict(),
                "response": response
            })
            
        except Exception as e:
            logger.error(f"处理用户消息失败: {e}")
            if self.chat_window:
                self.chat_window.send_error_message(f"处理消息失败: {e}")
    
    async def _execute_command(self, command_result) -> str:
        """执行命令"""
        try:
            command_type = command_result.command_type
            action = command_result.action
            parameters = command_result.parameters
            
            if command_type == CommandType.NAVIGATION:
                return await self._handle_navigation_command(parameters)
            
            elif command_type == CommandType.ANALYSIS:
                return await self._handle_analysis_command(parameters)
            
            elif command_type == CommandType.OPERATION:
                return await self._handle_operation_command(parameters)
            
            elif command_type == CommandType.QUERY:
                return await self._handle_query_command(parameters)
            
            elif command_type == CommandType.SYSTEM:
                return await self._handle_system_command(parameters)
            
            elif command_type == CommandType.HELP:
                return self._handle_help_command(parameters)
            
            else:
                return f"❓ 未知命令类型: {command_type.value}"
            
        except Exception as e:
            logger.error(f"执行命令失败: {e}")
            return f"❌ 执行命令失败: {e}"
    
    async def _handle_navigation_command(self, parameters: Dict) -> str:
        """处理导航命令"""
        try:
            target = parameters.get("target", "")
            
            if not self.current_scenario:
                return "❌ 当前没有活跃的场景，无法执行导航"
            
            # 执行智能导航
            nav_result = await self.scenario_workflow.navigate_by_requirement(target)
            
            if nav_result["success"]:
                # 记录导航操作
                self._record_operation("navigation", {
                    "target": target,
                    "result": nav_result
                })
                
                response = f"""✅ 导航成功！

🎯 目标: {target}
📍 当前页面: {nav_result['current_title']}
🔗 当前URL: {nav_result['current_url']}
📊 置信度: {nav_result['confidence']:.2f}
💡 AI推理: {nav_result['ai_reasoning']}

🔧 推荐操作: {nav_result['recommended_action']}"""

                # 显示替代选项
                if nav_result.get("alternative_options"):
                    response += "\n\n🔄 其他选项:"
                    for alt in nav_result["alternative_options"][:3]:
                        response += f"\n• {alt['link']['text']} (置信度: {alt['confidence']:.2f})"
                
                return response
            else:
                return f"❌ 导航失败: {nav_result['message']}"
            
        except Exception as e:
            return f"❌ 导航命令执行失败: {e}"
    
    async def _handle_analysis_command(self, parameters: Dict) -> str:
        """处理分析命令"""
        try:
            # 刷新页面分析
            analysis_result = await self.scenario_workflow.refresh_page_analysis()
            
            if analysis_result["success"]:
                page_data = analysis_result["page_data"]
                
                # 记录分析操作
                self._record_operation("page_analysis", {
                    "result": analysis_result
                })
                
                response = f"""🔍 页面分析完成！

📄 页面标题: {page_data['title']}
🔗 发现链接: {len(page_data['links'])} 个
🏗️ 功能区域: {len(page_data['functional_areas'])} 个
🤖 AI摘要: {page_data['ai_summary']}

📊 链接分类统计:"""
                
                # 统计链接分类
                categories = {}
                for link in page_data['links']:
                    category = link['category']
                    categories[category] = categories.get(category, 0) + 1
                
                for category, count in categories.items():
                    response += f"\n• {category}: {count} 个"
                
                # 显示高优先级链接
                high_priority_links = [link for link in page_data['links'] if link['priority'] >= 7]
                if high_priority_links:
                    response += f"\n\n⭐ 重要操作 (优先级 ≥ 7):"
                    for link in high_priority_links[:5]:
                        response += f"\n• {link['text']} (优先级: {link['priority']})"
                
                return response
            else:
                return f"❌ 页面分析失败: {analysis_result['message']}"
            
        except Exception as e:
            return f"❌ 分析命令执行失败: {e}"
    
    async def _handle_operation_command(self, parameters: Dict) -> str:
        """处理操作命令"""
        operation = parameters.get("operation", "")
        target = parameters.get("target", "")
        
        # 记录操作命令
        self._record_operation("operation_command", parameters)
        
        return f"⚡ 操作命令已记录: {operation} {target}\n\n💡 提示: 具体的操作执行功能正在开发中，当前主要支持导航和分析功能。"
    
    async def _handle_query_command(self, parameters: Dict) -> str:
        """处理查询命令"""
        query = parameters.get("query", "")
        
        # 获取当前页面操作信息
        operations_result = await self.scenario_workflow.get_current_page_operations()
        
        if operations_result["success"]:
            page_data = operations_result["page_data"]
            
            # 简单的查询匹配
            matching_links = []
            query_lower = query.lower()
            
            for link in page_data["links"]:
                if (query_lower in link["text"].lower() or 
                    query_lower in link["description"].lower() or
                    query_lower in link["category"].lower()):
                    matching_links.append(link)
            
            if matching_links:
                response = f"🔎 查询结果: '{query}'\n\n找到 {len(matching_links)} 个匹配项:"
                
                for link in matching_links[:10]:  # 最多显示10个
                    response += f"\n• {link['text']} ({link['category']}) - 优先级: {link['priority']}"
                    response += f"\n  描述: {link['description']}"
                
                return response
            else:
                return f"🔎 查询结果: '{query}'\n\n❌ 未找到匹配的操作项"
        else:
            return f"❌ 查询失败: 无法获取当前页面信息"
    
    async def _handle_system_command(self, parameters: Dict) -> str:
        """处理系统命令"""
        action = parameters.get("action", "")
        
        if "状态" in action or "信息" in action:
            return self._get_system_status()
        elif "记录" in action:
            return self._get_operation_records()
        elif "清空" in action or "清除" in action:
            return self._clear_records()
        elif "退出" in action or "关闭" in action:
            await self._close_interactive_mode()
            return "👋 正在关闭交互模式..."
        else:
            return f"⚙️ 系统命令: {action}\n\n💡 支持的系统命令: 状态、记录、清空、退出"
    
    def _handle_help_command(self, parameters: Dict) -> str:
        """处理帮助命令"""
        return """📖 AI+RPA 智能助手使用指南

🎯 主要功能:
• 智能导航 - 用自然语言描述需求进行页面导航
• 页面分析 - 分析当前页面的所有操作选项
• 操作记录 - 记录所有交互和操作流程
• AI协助 - 智能理解和处理用户命令

💬 使用方式:
• 直接输入需求: "我要查看用户管理"、"找到设置页面"
• 使用命令: "分析页面"、"系统状态"、"记录操作"
• 点击快捷按钮进行常用操作

🔧 支持的命令类型:
• 导航命令: 导航到、进入、查看、找到
• 分析命令: 分析页面、获取信息、页面分析
• 查询命令: 查询、搜索、显示、列出
• 系统命令: 系统状态、记录操作、清空记录
• 帮助命令: 帮助、使用说明、命令列表

💡 使用技巧:
• 使用自然语言描述您的需求
• 命令识别失败时会自动使用AI协助
• 所有操作都会被记录，可随时查看
• 支持快捷键和菜单操作

🆘 需要帮助时:
• 输入"帮助"查看使用说明
• 输入"命令列表"查看支持的命令
• 输入"系统状态"查看当前状态"""
    
    def _get_system_status(self) -> str:
        """获取系统状态"""
        status = f"""⚙️ 系统状态信息

🔄 运行状态: {'🟢 运行中' if self.is_running else '🔴 已停止'}
⏰ 会话时长: {self._get_session_duration()}
📊 操作记录: {len(self.operation_records)} 条
💬 命令历史: {len(self.command_processor.get_command_history())} 条

🎭 当前场景:"""
        
        if self.current_scenario:
            status += f"""
• 场景ID: {self.current_scenario.get('scenario_session_id', 'N/A')}
• 当前页面: {self.current_scenario.get('current_title', 'N/A')}
• 页面链接: {self.current_scenario.get('page_analysis', {}).get('links_count', 0)} 个"""
        else:
            status += "\n• 无活跃场景"
        
        return status
    
    def _get_operation_records(self) -> str:
        """获取操作记录"""
        if not self.operation_records:
            return "📝 操作记录为空"
        
        response = f"📝 操作记录 (最近10条):\n"
        
        for record in self.operation_records[-10:]:
            timestamp = record["timestamp"]
            operation_type = record["type"]
            response += f"\n• [{timestamp}] {operation_type}"
        
        response += f"\n\n📊 总计: {len(self.operation_records)} 条记录"
        
        return response
    
    def _clear_records(self) -> str:
        """清空记录"""
        self.operation_records.clear()
        return "🗑️ 操作记录已清空"
    
    def _get_session_duration(self) -> str:
        """获取会话持续时间"""
        if not self.session_start_time:
            return "未知"
        
        duration = datetime.now() - self.session_start_time
        hours = duration.seconds // 3600
        minutes = (duration.seconds % 3600) // 60
        seconds = duration.seconds % 60
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟"
        elif minutes > 0:
            return f"{minutes}分钟{seconds}秒"
        else:
            return f"{seconds}秒"
    
    def _record_operation(self, operation_type: str, data: Dict):
        """记录操作"""
        record = {
            "timestamp": datetime.now().strftime("%H:%M:%S"),
            "type": operation_type,
            "data": data
        }
        
        self.operation_records.append(record)
        
        # 保持记录在合理范围内
        if len(self.operation_records) > 1000:
            self.operation_records = self.operation_records[-500:]
    
    async def _close_interactive_mode(self):
        """关闭交互模式"""
        try:
            self.is_running = False
            
            # 保存交互记录
            await self._save_interaction_record()
            
            # 关闭复杂场景
            if self.current_scenario:
                await self.scenario_workflow.close_scenario()
            
            # 关闭聊天窗口
            if self.chat_window:
                self.chat_window._on_close()
            
            logger.info("交互模式已关闭")
            
        except Exception as e:
            logger.error(f"关闭交互模式失败: {e}")
    
    async def _save_interaction_record(self):
        """保存交互记录"""
        try:
            if not self.chat_window or not self.session_start_time:
                return
            
            # 创建交互记录
            record = InteractionRecord(
                session_id=self.chat_window.session_id,
                start_time=self.session_start_time.isoformat(),
                end_time=datetime.now().isoformat(),
                messages=self.chat_window.messages,
                operations_performed=self.operation_records,
                summary=f"交互式操作会话，共 {len(self.operation_records)} 个操作"
            )
            
            # 保存到文件
            filename = f"interaction_{record.session_id}_{int(time.time())}.json"
            filepath = self.records_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(record.to_dict(), f, indent=2, ensure_ascii=False)
            
            logger.info(f"交互记录已保存: {filepath}")
            
        except Exception as e:
            logger.error(f"保存交互记录失败: {e}")


# 全局实例
global_interactive_manager = InteractiveModeManager()


def get_interactive_mode_manager() -> InteractiveModeManager:
    """获取交互模式管理器实例"""
    return global_interactive_manager
