"""
使用 unittest discover 运行测试
"""
import sys
import os
import unittest

def run_tests():
    """运行测试"""
    # 设置测试目录
    test_dir = os.path.abspath('tests')
    
    # 使用 discover 查找测试
    test_suite = unittest.TestLoader().discover(
        start_dir=test_dir,
        pattern='test_*.py',
        top_level_dir=os.path.dirname(test_dir)
    )
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n测试结果:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败测试数: {len(result.failures) if hasattr(result, 'failures') else 0}")
    print(f"错误测试数: {len(result.errors) if hasattr(result, 'errors') else 0}")
    
    # 返回测试结果
    return result.wasSuccessful()

if __name__ == "__main__":
    # 运行测试
    success = run_tests()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)
