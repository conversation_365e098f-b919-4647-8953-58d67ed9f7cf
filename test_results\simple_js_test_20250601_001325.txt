测试时间: 2025-06-01 00:13:25
工作流名称: 简单JavaScript执行测试
执行状态: failed
执行时间: 1.51 秒

变量状态:
  test_var = 测试变量
  system = {'timestamp': '2025-05-31T16:13:23.841886+00:00', 'date': '2025-05-31', 'time': '16:13:23', 'platform': 'unknown'}

步骤执行结果:
  1. ✅ [navigate] : passed (0.77秒)
  2. ❌ [execute_script] : failed (0.01秒)
     错误: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:313:29)
    at UtilityScript.<anonymous> (<anonymous>:1:44)

功能验证结果:
❌ JavaScript执行功能异常
