"""
高级异常检测示例程序

展示增强后的异常检测和预警功能。
"""

import asyncio
import logging
import random
from datetime import datetime
from src.workflow.ai.anomaly import AnomalyDetector
from src.workflow.ai.alert import AlertManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_detector_config() -> dict:
    """获取检测器配置"""
    return {
        # 模型配置
        "contamination": 0.1,
        "n_estimators": 100,
        "max_samples": "auto",
        "max_features": 1.0,
        "bootstrap": False,
        "n_jobs": -1,
        
        # 阈值配置
        "execution_time_thresholds": {
            "low": 1.5,
            "medium": 2.0,
            "high": 3.0,
            "critical": 5.0
        },
        "resource_usage_thresholds": {
            "cpu": {
                "low": 50,
                "medium": 70,
                "high": 85,
                "critical": 95
            },
            "memory": {
                "low": 60,
                "medium": 75,
                "high": 90,
                "critical": 95
            }
        },
        "behavior_thresholds": {
            "error_rate": {
                "low": 0.05,
                "medium": 0.1,
                "high": 0.3,
                "critical": 0.5
            }
        },
        "data_consistency_thresholds": {
            "mismatch_threshold": 0.1,
            "validation_score_min": 0.8,
            "schema_violation_max": 0.05
        },
        "performance_degradation_thresholds": {
            "response_time_increase": 1.5,
            "throughput_decrease": 0.7,
            "error_rate_increase": 1.3
        },
        "security_risk_thresholds": {
            "suspicious_patterns": {
                "low": 0.3,
                "medium": 0.5,
                "high": 0.7,
                "critical": 0.9
            },
            "vulnerability_score": {
                "low": 3.0,
                "medium": 5.0,
                "high": 7.0,
                "critical": 9.0
            }
        }
    }

def get_alert_config() -> dict:
    """获取告警配置"""
    return {
        "alert_rules": {
            "data_consistency": {
                "rule_id": "rule_004",
                "name": "数据一致性异常",
                "description": "检测到数据一致性问题",
                "severity_threshold": "MEDIUM",
                "anomaly_types": ["data_consistency"],
                "cooldown_period": 900,  # 15分钟
                "aggregation_window": 300,  # 5分钟
                "suppression_condition": {
                    "min_occurrences": 2,
                    "time_window": 300
                },
                "notification_channels": ["email", "slack"]
            },
            "security_alert": {
                "rule_id": "rule_005",
                "name": "安全风险告警",
                "description": "检测到潜在的安全风险",
                "severity_threshold": "HIGH",
                "anomaly_types": ["security_risk"],
                "cooldown_period": 1800,  # 30分钟
                "aggregation_window": 300,  # 5分钟
                "suppression_condition": None,
                "notification_channels": ["email", "slack", "webhook"]
            }
        }
    }

def generate_advanced_test_scenarios() -> list:
    """生成高级测试场景"""
    return [
        {
            "name": "数据一致性异常",
            "data": {
                "node_id": "node_7",
                "execution_time": 10.0,
                "step_count": 5,
                "complexity": 2,
                "resource_usage": {
                    "cpu": 50,
                    "memory": 60,
                    "io": 30
                },
                "behavior": {
                    "action_frequency": 1.0,
                    "error_rate": 0.05,
                    "pattern_score": 0.8
                },
                "data_validation": {
                    "mismatch_rate": 0.15,
                    "validation_score": 0.75,
                    "schema_violations": 0.08
                }
            }
        },
        {
            "name": "性能退化异常",
            "data": {
                "node_id": "node_8",
                "execution_time": 15.0,
                "step_count": 5,
                "complexity": 2,
                "resource_usage": {
                    "cpu": 60,
                    "memory": 70,
                    "io": 40
                },
                "behavior": {
                    "action_frequency": 1.0,
                    "error_rate": 0.05,
                    "pattern_score": 0.8
                },
                "performance_metrics": {
                    "response_time_ratio": 1.8,
                    "throughput_ratio": 0.6,
                    "error_rate_ratio": 1.4
                }
            }
        },
        {
            "name": "安全风险异常",
            "data": {
                "node_id": "node_9",
                "execution_time": 10.0,
                "step_count": 5,
                "complexity": 2,
                "resource_usage": {
                    "cpu": 50,
                    "memory": 60,
                    "io": 30
                },
                "behavior": {
                    "action_frequency": 1.0,
                    "error_rate": 0.05,
                    "pattern_score": 0.8
                },
                "security_metrics": {
                    "suspicious_pattern_score": 0.85,
                    "vulnerability_score": 7.5
                }
            }
        },
        {
            "name": "多重高严重度异常",
            "data": {
                "node_id": "node_10",
                "execution_time": 80.0,
                "step_count": 5,
                "complexity": 2,
                "resource_usage": {
                    "cpu": 92,
                    "memory": 88,
                    "io": 75
                },
                "behavior": {
                    "action_frequency": 0.2,
                    "error_rate": 0.4,
                    "pattern_score": 0.4
                },
                "security_metrics": {
                    "suspicious_pattern_score": 0.95,
                    "vulnerability_score": 8.5
                },
                "performance_metrics": {
                    "response_time_ratio": 2.5,
                    "throughput_ratio": 0.4,
                    "error_rate_ratio": 2.0
                }
            }
        }
    ]

async def run_advanced_anomaly_detection_demo():
    """运行高级异常检测示例"""
    try:
        logger.info("初始化异常检测器和告警管理器...")
        detector = AnomalyDetector(config=get_detector_config())
        alert_manager = AlertManager(config=get_alert_config())
        
        # 运行测试场景
        scenarios = generate_advanced_test_scenarios()
        
        for scenario in scenarios:
            logger.info(f"\n执行测试场景: {scenario['name']}")
            logger.info("输入数据:")
            logger.info(scenario['data'])
            
            # 检测异常
            anomalies = detector.detect_anomalies(scenario['data'])
            
            if anomalies:
                logger.info(f"检测到 {len(anomalies)} 个异常:")
                for anomaly in anomalies:
                    logger.info(f"\n异常ID: {anomaly.anomaly_id}")
                    logger.info(f"类型: {anomaly.type.value}")
                    logger.info(f"严重程度: {anomaly.severity.value}")
                    logger.info(f"节点ID: {anomaly.node_id}")
                    logger.info(f"置信度: {anomaly.confidence:.2f}")
                    logger.info("详细信息:")
                    logger.info(anomaly.details)
                    logger.info("建议操作:")
                    for action in anomaly.suggested_actions:
                        logger.info(f"- {action}")
                
                # 处理告警
                alerts = alert_manager.process_anomalies(anomalies)
                if alerts:
                    logger.info(f"\n生成了 {len(alerts)} 个告警:")
                    for alert in alerts:
                        logger.info(f"\n告警ID: {alert.alert_id}")
                        logger.info(f"级别: {alert.level.value}")
                        logger.info(f"状态: {alert.status.value}")
                        logger.info(f"标题: {alert.title}")
                        logger.info("描述:")
                        logger.info(alert.description)
            else:
                logger.info("未检测到异常")
            
            # 模拟执行间隔
            await asyncio.sleep(1)
    
    except Exception as e:
        logger.error(f"示例程序执行错误: {str(e)}")
        raise

async def main():
    """主函数"""
    await run_advanced_anomaly_detection_demo()

if __name__ == "__main__":
    # 运行示例程序
    asyncio.run(main()) 