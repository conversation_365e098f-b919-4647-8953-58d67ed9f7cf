"""
集成测试的共享固件
"""
import pytest
import os
import tempfile
import shutil
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional, List

import playwright
from playwright.async_api import async_playwright, <PERSON><PERSON>er, BrowserContext, Page

from workflow.operations import Operation, ClickOperation, FillOperation, NavigateOperation, WaitOperation
from workflow.execution import OperationExecutor, ExecutionContext
from workflow.recording import OperationRecorder
from workflow.workflow_engine import WorkflowEngine

# 获取测试文件所在目录
TEST_DIR = Path(__file__).parent
TEST_DATA_DIR = TEST_DIR / "test_data"

# 测试页面路径
TEST_PAGE = f"file://{TEST_DATA_DIR / 'test_page.html'}"

@pytest.fixture(scope="session")
def event_loop():
    """创建一个事件循环，用于所有测试"""
    loop = asyncio.get_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def browser():
    """创建Playwright浏览器实例"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        try:
            yield browser
        finally:
            await browser.close()

@pytest.fixture
async def context(browser: Browser):
    """创建浏览器上下文"""
    context = await browser.new_context()
    try:
        yield context
    finally:
        await context.close()

@pytest.fixture
async def page(context: BrowserContext):
    """创建测试页面"""
    page = await context.new_page()
    await page.goto(TEST_PAGE)
    return page

@pytest.fixture
def operation_factory():
    """创建操作工厂"""
    return OperationFactory()

@pytest.fixture
def recorder() -> OperationRecorder:
    """创建操作记录器"""
    return OperationRecorder()

@pytest.fixture
def executor() -> OperationExecutor:
    """创建操作执行器"""
    return OperationExecutor()

@pytest.fixture
def workflow_engine() -> WorkflowEngine:
    """创建工作流引擎"""
    return WorkflowEngine()

@pytest.fixture
def execution_context(page: Page) -> ExecutionContext:
    """创建执行上下文"""
    return ExecutionContext(page=page)

class OperationTestBase:
    """操作测试基类"""
    
    @pytest.fixture(autouse=True)
    async def setup(self, page: Page, executor: OperationExecutor, execution_context: ExecutionContext):
        """测试设置"""
        self.page = page
        self.executor = executor
        self.context = execution_context
        # 确保每次测试前都回到测试页面
        await self.page.goto(TEST_PAGE)
