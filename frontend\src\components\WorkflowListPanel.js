import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Menu,
  MenuItem,
  Tooltip,
  Alert,
  Divider,
  Avatar,
  Card,
  CardContent,
  CardActions,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  FileCopy as CopyIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  MoreVert as MoreIcon,
  Schedule as ScheduleIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Pause as PauseIcon,
  AccountTree as WorkflowIcon,
  Search as SearchIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';

const WorkflowListPanel = ({ 
  workflows = [], 
  onCreateWorkflow, 
  onEditWorkflow, 
  onDeleteWorkflow, 
  onExecuteWorkflow,
  onDuplicateWorkflow,
  onImportWorkflow,
  onExportWorkflow,
  currentWorkflow = null,
  executionStatus = {}
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedWorkflow, setSelectedWorkflow] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState(''); // 'create', 'edit', 'delete'
  const [workflowForm, setWorkflowForm] = useState({
    name: '',
    description: '',
    domain: '',
    tags: []
  });
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [menuWorkflow, setMenuWorkflow] = useState(null);

  // 过滤工作流
  const filteredWorkflows = workflows.filter(workflow => {
    const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workflow.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workflow.domain?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterStatus === 'all' || 
                         (filterStatus === 'recent' && isRecentWorkflow(workflow)) ||
                         (filterStatus === 'favorites' && workflow.isFavorite);
    
    return matchesSearch && matchesFilter;
  });

  // 判断是否是最近的工作流
  const isRecentWorkflow = (workflow) => {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    return new Date(workflow.updated_at || workflow.created_at) > oneWeekAgo;
  };

  // 获取工作流状态
  const getWorkflowStatus = (workflowId) => {
    return executionStatus[workflowId] || 'idle';
  };

  // 获取状态图标和颜色
  const getStatusInfo = (status) => {
    switch (status) {
      case 'running':
        return { icon: <PlayIcon />, color: 'primary', label: '运行中' };
      case 'paused':
        return { icon: <PauseIcon />, color: 'warning', label: '已暂停' };
      case 'completed':
        return { icon: <SuccessIcon />, color: 'success', label: '已完成' };
      case 'error':
        return { icon: <ErrorIcon />, color: 'error', label: '执行错误' };
      default:
        return { icon: <ScheduleIcon />, color: 'default', label: '就绪' };
    }
  };

  // 打开对话框
  const openDialog = (type, workflow = null) => {
    setDialogType(type);
    setSelectedWorkflow(workflow);
    
    if (type === 'create') {
      setWorkflowForm({
        name: '',
        description: '',
        domain: '',
        tags: []
      });
    } else if (type === 'edit' && workflow) {
      setWorkflowForm({
        name: workflow.name || '',
        description: workflow.description || '',
        domain: workflow.domain || '',
        tags: workflow.tags || []
      });
    }
    
    setDialogOpen(true);
  };

  // 关闭对话框
  const closeDialog = () => {
    setDialogOpen(false);
    setSelectedWorkflow(null);
    setWorkflowForm({
      name: '',
      description: '',
      domain: '',
      tags: []
    });
  };

  // 处理表单提交
  const handleSubmit = () => {
    if (dialogType === 'create') {
      onCreateWorkflow(workflowForm);
    } else if (dialogType === 'edit') {
      onEditWorkflow(selectedWorkflow.id, workflowForm);
    } else if (dialogType === 'delete') {
      onDeleteWorkflow(selectedWorkflow.id);
    }
    closeDialog();
  };

  // 处理菜单操作
  const handleMenuClick = (event, workflow) => {
    setMenuAnchor(event.currentTarget);
    setMenuWorkflow(workflow);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setMenuWorkflow(null);
  };

  // 处理文件导入
  const handleFileImport = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const workflowData = JSON.parse(e.target.result);
          onImportWorkflow(workflowData);
        } catch (error) {
          alert('导入失败：文件格式错误');
        }
      };
      reader.readAsText(file);
    }
  };

  // 渲染工作流卡片
  const renderWorkflowCard = (workflow) => {
    const status = getWorkflowStatus(workflow.id);
    const statusInfo = getStatusInfo(status);
    const isActive = currentWorkflow?.id === workflow.id;

    return (
      <Grid item xs={12} sm={6} md={4} key={workflow.id}>
        <Card 
          sx={{ 
            height: '100%',
            border: isActive ? 2 : 1,
            borderColor: isActive ? 'primary.main' : 'divider',
            '&:hover': {
              boxShadow: 4
            }
          }}
        >
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
              <Typography variant="h6" component="div" noWrap sx={{ flex: 1 }}>
                {workflow.name}
              </Typography>
              <IconButton 
                size="small" 
                onClick={(e) => handleMenuClick(e, workflow)}
              >
                <MoreIcon />
              </IconButton>
            </Box>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 2, minHeight: 40 }}>
              {workflow.description || '暂无描述'}
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <Chip
                icon={statusInfo.icon}
                label={statusInfo.label}
                size="small"
                color={statusInfo.color}
              />
              {workflow.domain && (
                <Chip
                  label={workflow.domain}
                  size="small"
                  variant="outlined"
                />
              )}
            </Box>

            {workflow.tags && workflow.tags.length > 0 && (
              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 2 }}>
                {workflow.tags.slice(0, 3).map((tag, index) => (
                  <Chip
                    key={index}
                    label={tag}
                    size="small"
                    variant="outlined"
                    sx={{ fontSize: '0.7rem' }}
                  />
                ))}
                {workflow.tags.length > 3 && (
                  <Chip
                    label={`+${workflow.tags.length - 3}`}
                    size="small"
                    variant="outlined"
                    sx={{ fontSize: '0.7rem' }}
                  />
                )}
              </Box>
            )}

            <Typography variant="caption" color="text.secondary">
              创建时间: {new Date(workflow.created_at).toLocaleDateString()}
            </Typography>
          </CardContent>

          <CardActions>
            <Button
              size="small"
              startIcon={<PlayIcon />}
              onClick={() => onExecuteWorkflow(workflow)}
              disabled={status === 'running'}
            >
              执行
            </Button>
            <Button
              size="small"
              startIcon={<EditIcon />}
              onClick={() => onEditWorkflow(workflow)}
            >
              编辑
            </Button>
          </CardActions>
        </Card>
      </Grid>
    );
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部工具栏 */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WorkflowIcon color="primary" />
            工作流管理
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <input
              accept=".json"
              style={{ display: 'none' }}
              id="import-workflow-file"
              type="file"
              onChange={handleFileImport}
            />
            <label htmlFor="import-workflow-file">
              <Tooltip title="导入工作流">
                <IconButton component="span" size="small">
                  <UploadIcon />
                </IconButton>
              </Tooltip>
            </label>
            
            <Tooltip title="创建新工作流">
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => openDialog('create')}
                size="small"
              >
                新建
              </Button>
            </Tooltip>
          </Box>
        </Box>

        {/* 搜索和过滤 */}
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <TextField
            size="small"
            placeholder="搜索工作流..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
            sx={{ flex: 1 }}
          />
          
          <Button
            size="small"
            startIcon={<FilterIcon />}
            onClick={(e) => {
              // 实现过滤菜单
            }}
          >
            过滤
          </Button>
        </Box>
      </Box>

      {/* 工作流列表 */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
        {filteredWorkflows.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <WorkflowIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {searchTerm ? '未找到匹配的工作流' : '暂无工作流'}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {searchTerm ? '尝试调整搜索条件' : '创建您的第一个工作流开始自动化之旅'}
            </Typography>
            {!searchTerm && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => openDialog('create')}
              >
                创建工作流
              </Button>
            )}
          </Box>
        ) : (
          <Grid container spacing={2}>
            {filteredWorkflows.map(renderWorkflowCard)}
          </Grid>
        )}
      </Box>

      {/* 创建/编辑对话框 */}
      <Dialog open={dialogOpen} onClose={closeDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {dialogType === 'create' ? '创建新工作流' : 
           dialogType === 'edit' ? '编辑工作流' : '删除工作流'}
        </DialogTitle>
        <DialogContent>
          {dialogType === 'delete' ? (
            <Typography>
              确定要删除工作流 "{selectedWorkflow?.name}" 吗？此操作无法撤销。
            </Typography>
          ) : (
            <Box sx={{ pt: 1 }}>
              <TextField
                fullWidth
                label="工作流名称"
                value={workflowForm.name}
                onChange={(e) => setWorkflowForm(prev => ({ ...prev, name: e.target.value }))}
                margin="normal"
                required
              />
              <TextField
                fullWidth
                label="描述"
                value={workflowForm.description}
                onChange={(e) => setWorkflowForm(prev => ({ ...prev, description: e.target.value }))}
                margin="normal"
                multiline
                rows={3}
              />
              <TextField
                fullWidth
                label="目标域名"
                value={workflowForm.domain}
                onChange={(e) => setWorkflowForm(prev => ({ ...prev, domain: e.target.value }))}
                margin="normal"
                placeholder="例如: www.baidu.com"
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDialog}>取消</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            color={dialogType === 'delete' ? 'error' : 'primary'}
          >
            {dialogType === 'create' ? '创建' : 
             dialogType === 'edit' ? '保存' : '删除'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 右键菜单 */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          onExecuteWorkflow(menuWorkflow);
          handleMenuClose();
        }}>
          <ListItemIcon><PlayIcon /></ListItemIcon>
          执行工作流
        </MenuItem>
        <MenuItem onClick={() => {
          openDialog('edit', menuWorkflow);
          handleMenuClose();
        }}>
          <ListItemIcon><EditIcon /></ListItemIcon>
          编辑工作流
        </MenuItem>
        <MenuItem onClick={() => {
          onDuplicateWorkflow(menuWorkflow);
          handleMenuClose();
        }}>
          <ListItemIcon><CopyIcon /></ListItemIcon>
          复制工作流
        </MenuItem>
        <MenuItem onClick={() => {
          onExportWorkflow(menuWorkflow);
          handleMenuClose();
        }}>
          <ListItemIcon><DownloadIcon /></ListItemIcon>
          导出工作流
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          openDialog('delete', menuWorkflow);
          handleMenuClose();
        }}>
          <ListItemIcon><DeleteIcon /></ListItemIcon>
          删除工作流
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default WorkflowListPanel;
