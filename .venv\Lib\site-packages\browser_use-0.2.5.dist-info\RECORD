../../Scripts/browser-use.exe,sha256=UXntpAXGng-W8RHaSesmiZ44XjoRWUkgsTsHGnzC320,108395
../../Scripts/browseruse.exe,sha256=UXntpAXGng-W8RHaSesmiZ44XjoRWUkgsTsHGnzC320,108395
browser_use-0.2.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
browser_use-0.2.5.dist-info/METADATA,sha256=An5c2mPT9GUs7aRNu2r2Zp-dmRYgRhhmo_gM2E80cpo,9640
browser_use-0.2.5.dist-info/RECORD,,
browser_use-0.2.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
browser_use-0.2.5.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
browser_use-0.2.5.dist-info/entry_points.txt,sha256=NceUXLtKZs9AznxXL8P1rxVQVpF8jyk0x3SXZGhU1VE,87
browser_use-0.2.5.dist-info/licenses/LICENSE,sha256=E1xXZxsO6VdmmwWgygDMBvZFSW01Hi5zKDLG4nbaml4,1069
browser_use/__init__.py,sha256=5r7TknTJNvbe2G5LsSxGsIK6KkLXYvlChQ_Ca5sgdts,710
browser_use/__pycache__/__init__.cpython-312.pyc,,
browser_use/__pycache__/cli.cpython-312.pyc,,
browser_use/__pycache__/exceptions.cpython-312.pyc,,
browser_use/__pycache__/logging_config.cpython-312.pyc,,
browser_use/__pycache__/utils.cpython-312.pyc,,
browser_use/agent/__pycache__/gif.cpython-312.pyc,,
browser_use/agent/__pycache__/playwright_script_generator.cpython-312.pyc,,
browser_use/agent/__pycache__/playwright_script_helpers.cpython-312.pyc,,
browser_use/agent/__pycache__/prompts.cpython-312.pyc,,
browser_use/agent/__pycache__/service.cpython-312.pyc,,
browser_use/agent/__pycache__/views.cpython-312.pyc,,
browser_use/agent/gif.py,sha256=eU217IHSd3rbjzVJdb7YNg5mucXls6-ZIIq5mmWrxoo,10334
browser_use/agent/memory/__init__.py,sha256=X70G1C3hyfUIpxGCHlCzvb17whQFdf0hr72PHqZRfoI,146
browser_use/agent/memory/__pycache__/__init__.cpython-312.pyc,,
browser_use/agent/memory/__pycache__/service.cpython-312.pyc,,
browser_use/agent/memory/__pycache__/views.cpython-312.pyc,,
browser_use/agent/memory/service.py,sha256=_BMyiv8S9Y7b62YZ73S-lUWncY-IDFoAiFCjUvnxAj8,5677
browser_use/agent/memory/views.py,sha256=oZm4eeixeHH4hx6-YZvoSWubni56gLB_LbFBKmxdT8U,5410
browser_use/agent/message_manager/__pycache__/service.cpython-312.pyc,,
browser_use/agent/message_manager/__pycache__/utils.cpython-312.pyc,,
browser_use/agent/message_manager/__pycache__/views.cpython-312.pyc,,
browser_use/agent/message_manager/service.py,sha256=oCwDwRhyY-vD-BN_JYKFav9Db1nZRlT2ZaSXwE8GHN0,19499
browser_use/agent/message_manager/utils.py,sha256=iHSMOhayQWdMgPnMf6D64lz28chKEjIK_IFM5MX6eZs,5177
browser_use/agent/message_manager/views.py,sha256=wCyUJX6GfQMxErGkx8lUuZ-yqrdzUTIXkL-qiAspqfo,4093
browser_use/agent/playwright_script_generator.py,sha256=an2jybBpiKQQwTPb_saGgSJ97WC5Ri-weNUuMinihx0,33755
browser_use/agent/playwright_script_helpers.py,sha256=yxoMIX7V1HjIEnvPG5GDxXCSRnJsa-TqHtf9R2uXq7k,4123
browser_use/agent/prompts.py,sha256=LjiTanGywhkpAfrevepz4tHZxFd0VUqOwjglPIk-Bis,6180
browser_use/agent/service.py,sha256=iK-dAAVRP9ju3zYsAcDOpUSXeLkTuz6GYTdX_XoAXws,72187
browser_use/agent/system_prompt.md,sha256=_ag08P22uv3iOHmPquS7Uanx6loGsRjM88JAWnUvnwU,5081
browser_use/agent/views.py,sha256=Izj4knO8PP1oxaANLPr8ctNx4P8c_q4k9GVe1R791XQ,14865
browser_use/browser/__init__.py,sha256=I0EhXT22YZMx1Z4C9cFvxUWu088TjCBeDkcnMKW3Bzg,292
browser_use/browser/__pycache__/__init__.cpython-312.pyc,,
browser_use/browser/__pycache__/browser.cpython-312.pyc,,
browser_use/browser/__pycache__/context.cpython-312.pyc,,
browser_use/browser/__pycache__/extensions.cpython-312.pyc,,
browser_use/browser/__pycache__/profile.cpython-312.pyc,,
browser_use/browser/__pycache__/session.cpython-312.pyc,,
browser_use/browser/__pycache__/views.cpython-312.pyc,,
browser_use/browser/browser.py,sha256=Gf9mbJdqnfWfNyCnq03jQheSikEsaOV6DoGLifhGQFY,269
browser_use/browser/context.py,sha256=HQUnwuVfXnWGGmc91JtZ3KTi2QKQsRDurfpnd-yB3k8,319
browser_use/browser/extensions.py,sha256=n5Z3hUrnvUcDTynaGR4fRiUDSM60Ex-cPSB0eBb6kaQ,15630
browser_use/browser/profile.py,sha256=ClVUP2bLAtppVTQVhUQM-nDds3vJit_pUp6wmUbLBf4,31455
browser_use/browser/session.py,sha256=Z1Q5Kt2BT0ru-Iu1_ym5HNGkUpIYfyT3zdLG3SNR6rc,89593
browser_use/browser/views.py,sha256=R9nkeSlL5J20njiTParX5_7j3nQt-8rugTbh8LSrPj8,1614
browser_use/cli.py,sha256=MnwMw8KgE9z7dXMXvbsshU3bgKwC8y4S85Okqm6DSJA,48520
browser_use/controller/__pycache__/service.cpython-312.pyc,,
browser_use/controller/__pycache__/views.cpython-312.pyc,,
browser_use/controller/registry/__pycache__/service.cpython-312.pyc,,
browser_use/controller/registry/__pycache__/views.cpython-312.pyc,,
browser_use/controller/registry/service.py,sha256=vmjXEWb8O1Uf_AB0ANsrllNEW5O2ryP0At7rxVNyq3E,18985
browser_use/controller/registry/views.py,sha256=D7eeF3xNKtYbxTk5BYiW73LC0DZszQFv2MEQvvTHn1g,6191
browser_use/controller/service.py,sha256=kNU_BOFrOPewdtu-c0qpJtaaVLrVV_GpsJOb29Sy94c,34253
browser_use/controller/views.py,sha256=f4ikW36BpasEnN9gHaQkdbhvxlmAID1_P0hN4xgD3S8,2537
browser_use/dom/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
browser_use/dom/__pycache__/__init__.cpython-312.pyc,,
browser_use/dom/__pycache__/service.cpython-312.pyc,,
browser_use/dom/__pycache__/views.cpython-312.pyc,,
browser_use/dom/buildDomTree.js,sha256=6oqSMe3OpHozAskY2f2KBJhsh_vYvjptLLJxozYY7rk,49508
browser_use/dom/clickable_element_processor/__pycache__/service.cpython-312.pyc,,
browser_use/dom/clickable_element_processor/service.py,sha256=oFKPQw84HJCirAN_ic1lZrKBfwDvripRoAK7zWQ5eoE,2681
browser_use/dom/history_tree_processor/__pycache__/service.cpython-312.pyc,,
browser_use/dom/history_tree_processor/__pycache__/view.cpython-312.pyc,,
browser_use/dom/history_tree_processor/service.py,sha256=LElri3-bsiVaXgiZpj3TzOGDgw_WfHWOaTw9_QWDOVA,4143
browser_use/dom/history_tree_processor/view.py,sha256=W_1O4njhqaAtKkTUuH4mrjTy-Zsanq5ecbf2qGDHL10,1646
browser_use/dom/service.py,sha256=QSk1BwiPHW30eHdpxJrenFOu86_CGBzJMYW2nGdIErw,6385
browser_use/dom/views.py,sha256=t2sXVk1l6CiA_xwXSKiU8QD8zJIf8BbA-l3o_Fz3y90,7225
browser_use/exceptions.py,sha256=Bn79JaO4kwiJ7YKDbtiebrgaNvzHn6PgKmbaNf_XrpI,186
browser_use/logging_config.py,sha256=GVrMStN3ZEvmd8iTQETLcyGl1FEKWrsUha-d2HpYp5w,4272
browser_use/telemetry/__init__.py,sha256=aTazKKnX7o2ja439oQO44B9rRmchHIwd7lgB-Akttl4,300
browser_use/telemetry/__pycache__/__init__.cpython-312.pyc,,
browser_use/telemetry/__pycache__/service.cpython-312.pyc,,
browser_use/telemetry/__pycache__/views.cpython-312.pyc,,
browser_use/telemetry/service.py,sha256=dfU_jHXZOWX12shgCbdy4Zp_Dwy9byxkJZ1YZ5bFeB4,3366
browser_use/telemetry/views.py,sha256=yQmEY0-CkyAQLoUChsx4U-RvK_OrDzYJF-FRf9V1NHA,1196
browser_use/utils.py,sha256=w8p2u-BOeoncmN6ZkczR1pslQGvGad2SvjVhrmUq8QM,16768
