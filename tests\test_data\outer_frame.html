<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Outer Frame</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 15px;
            background-color: #f0f8ff;
        }
        .header {
            background-color: #4CAF50;
            color: white;
            padding: 10px;
            margin: -15px -15px 15px -15px;
            text-align: center;
        }
        .content {
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .frame-container {
            margin-top: 15px;
            border: 2px dashed #4CAF50;
            padding: 10px;
            background: white;
        }
        iframe {
            width: 100%;
            height: 300px;
            border: 1px solid #ccc;
        }
        .controls {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 4px;
        }
        button {
            padding: 5px 10px;
            margin-right: 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>Outer Frame</h2>
    </div>
    
    <div class="content">
        <p>This is the outer frame containing another iframe below.</p>
        
        <div class="controls">
            <button id="change-theme">Toggle Theme</button>
            <button id="show-message">Show Message</button>
        </div>
        
        <div class="frame-container">
            <h3>Inner Frame</h3>
            <iframe id="inner-iframe" src="inner_frame.html"></iframe>
        </div>
    </div>

    <script>
        // 主题切换
        document.getElementById('change-theme').addEventListener('click', function() {
            document.body.style.backgroundColor = 
                document.body.style.backgroundColor === 'lightgray' ? '#f0f8ff' : 'lightgray';
        });
        
        // 显示消息
        document.getElementById('show-message').addEventListener('click', function() {
            alert('This is a message from the outer frame!');
        });
        
        // 监听来自内层iframe的消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'searchPerformed') {
                console.log('Search performed in inner frame:', event.data.term);
            }
        });
    </script>
</body>
</html>
