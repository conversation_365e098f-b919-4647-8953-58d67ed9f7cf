"""
异常检测示例程序

展示如何使用异常检测器来监控和分析工作流执行。
"""

import asyncio
import logging
import random
from datetime import datetime
from src.workflow.ai.anomaly import AnomalyDetector, AnomalySeverity

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_detector_config() -> dict:
    """
    获取检测器配置
    
    Returns:
        dict: 配置信息
    """
    return {
        # 模型配置
        "contamination": 0.1,
        "n_estimators": 100,
        "max_samples": "auto",
        "max_features": 1.0,
        "bootstrap": False,
        "n_jobs": -1,
        
        # 执行时间阈值
        "execution_time_thresholds": {
            "low": 1.5,
            "medium": 2.0,
            "high": 3.0,
            "critical": 5.0
        },
        
        # 资源使用阈值
        "resource_usage_thresholds": {
            "cpu": {
                "low": 50,
                "medium": 70,
                "high": 85,
                "critical": 95
            },
            "memory": {
                "low": 60,
                "medium": 75,
                "high": 90,
                "critical": 95
            },
            "io": {
                "low": 40,
                "medium": 60,
                "high": 80,
                "critical": 90
            }
        },
        
        # 行为阈值
        "behavior_thresholds": {
            "error_rate": {
                "low": 0.05,
                "medium": 0.1,
                "high": 0.3,
                "critical": 0.5
            },
            "action_frequency": {
                "min": 0.1,
                "max": 10.0
            },
            "pattern_score": {
                "min": 0.7
            }
        }
    }

def generate_training_data(num_records: int = 100) -> list:
    """
    生成训练数据
    
    Args:
        num_records: 生成的记录数量
    
    Returns:
        训练数据列表
    """
    data = []
    for _ in range(num_records):
        # 生成正常数据
        record = {
            "execution_time": random.uniform(8.0, 12.0),
            "step_count": random.randint(4, 6),
            "complexity": random.randint(1, 3),
            "resource_usage": {
                "cpu": random.uniform(40, 60),
                "memory": random.uniform(50, 70),
                "io": random.uniform(20, 40)
            },
            "behavior": {
                "action_frequency": random.uniform(0.8, 1.2),
                "error_rate": random.uniform(0.01, 0.08),
                "pattern_score": random.uniform(0.75, 0.95)
            }
        }
        data.append(record)
    
    # 添加少量异常数据
    for _ in range(num_records // 10):
        # 生成异常数据
        record = {
            "execution_time": random.uniform(50.0, 100.0),
            "step_count": random.randint(4, 6),
            "complexity": random.randint(1, 3),
            "resource_usage": {
                "cpu": random.uniform(85, 98),
                "memory": random.uniform(80, 95),
                "io": random.uniform(70, 90)
            },
            "behavior": {
                "action_frequency": random.uniform(0.05, 0.2),
                "error_rate": random.uniform(0.4, 0.8),
                "pattern_score": random.uniform(0.2, 0.4)
            }
        }
        data.append(record)
    
    return data

def generate_test_scenarios() -> list:
    """
    生成测试场景
    
    Returns:
        测试场景列表
    """
    return [
        {
            "name": "正常执行",
            "data": {
                "node_id": "node_1",
                "execution_time": 10.0,
                "step_count": 5,
                "complexity": 2,
                "resource_usage": {
                    "cpu": 50,
                    "memory": 60,
                    "io": 30
                },
                "behavior": {
                    "action_frequency": 1.0,
                    "error_rate": 0.05,
                    "pattern_score": 0.8
                }
            }
        },
        {
            "name": "执行时间异常",
            "data": {
                "node_id": "node_2",
                "execution_time": 80.0,
                "step_count": 5,
                "complexity": 2,
                "resource_usage": {
                    "cpu": 50,
                    "memory": 60,
                    "io": 30
                },
                "behavior": {
                    "action_frequency": 1.0,
                    "error_rate": 0.05,
                    "pattern_score": 0.8
                }
            }
        },
        {
            "name": "资源使用异常",
            "data": {
                "node_id": "node_3",
                "execution_time": 10.0,
                "step_count": 5,
                "complexity": 2,
                "resource_usage": {
                    "cpu": 95,
                    "memory": 90,
                    "io": 80
                },
                "behavior": {
                    "action_frequency": 1.0,
                    "error_rate": 0.05,
                    "pattern_score": 0.8
                }
            }
        },
        {
            "name": "行为异常",
            "data": {
                "node_id": "node_4",
                "execution_time": 10.0,
                "step_count": 5,
                "complexity": 2,
                "resource_usage": {
                    "cpu": 50,
                    "memory": 60,
                    "io": 30
                },
                "behavior": {
                    "action_frequency": 0.1,
                    "error_rate": 0.6,
                    "pattern_score": 0.3
                }
            }
        },
        {
            "name": "错误模式",
            "data": {
                "node_id": "node_5",
                "execution_time": 10.0,
                "step_count": 5,
                "complexity": 2,
                "resource_usage": {
                    "cpu": 50,
                    "memory": 60,
                    "io": 30
                },
                "behavior": {
                    "action_frequency": 1.0,
                    "error_rate": 0.05,
                    "pattern_score": 0.8
                },
                "error": "Element not found: button#submit"
            }
        },
        {
            "name": "多重异常",
            "data": {
                "node_id": "node_6",
                "execution_time": 90.0,
                "step_count": 5,
                "complexity": 2,
                "resource_usage": {
                    "cpu": 95,
                    "memory": 90,
                    "io": 80
                },
                "behavior": {
                    "action_frequency": 0.1,
                    "error_rate": 0.6,
                    "pattern_score": 0.3
                },
                "error": "Network error: connection refused"
            }
        }
    ]

async def run_anomaly_detection_demo():
    """运行异常检测示例"""
    try:
        logger.info("初始化异常检测器...")
        detector = AnomalyDetector(config=get_detector_config())
        
        # 生成并加载训练数据
        logger.info("生成训练数据...")
        training_data = generate_training_data()
        
        logger.info("训练异常检测模型...")
        detector.train_models(training_data)
        
        # 运行测试场景
        scenarios = generate_test_scenarios()
        
        for scenario in scenarios:
            logger.info(f"\n执行测试场景: {scenario['name']}")
            logger.info("输入数据:")
            logger.info(scenario['data'])
            
            # 检测异常
            anomalies = detector.detect_anomalies(scenario['data'])
            
            if anomalies:
                logger.info(f"检测到 {len(anomalies)} 个异常:")
                for anomaly in anomalies:
                    logger.info(f"\n异常ID: {anomaly.anomaly_id}")
                    logger.info(f"类型: {anomaly.type.value}")
                    logger.info(f"严重程度: {anomaly.severity.value}")
                    logger.info(f"节点ID: {anomaly.node_id}")
                    logger.info(f"置信度: {anomaly.confidence:.2f}")
                    logger.info("详细信息:")
                    logger.info(anomaly.details)
                    logger.info("建议操作:")
                    for action in anomaly.suggested_actions:
                        logger.info(f"- {action}")
            else:
                logger.info("未检测到异常")
            
            # 模拟执行间隔
            await asyncio.sleep(1)
    
    except Exception as e:
        logger.error(f"示例程序执行错误: {str(e)}")
        raise

async def main():
    """主函数"""
    await run_anomaly_detection_demo()

if __name__ == "__main__":
    # 运行示例程序
    asyncio.run(main()) 