# API 参考文档

本文档详细介绍系统的 API 接口。

## 录制器 API

### CodegenWrapper

工作流录制器的核心类，封装了 Playwright Codegen 功能。

```python
class CodegenWrapper:
    def __init__(self, output_file: str):
        """
        初始化录制器
        
        Args:
            output_file (str): 输出文件路径
        """
        pass
        
    def start_recording(
        self,
        url: str,
        viewport: tuple = (1280, 720),
        device: str = None,
        lang: str = None,
        color_scheme: str = None,
        timezone: str = None
    ) -> None:
        """
        启动录制
        
        Args:
            url (str): 目标URL
            viewport (tuple): 视口大小
            device (str): 设备名称
            lang (str): 语言设置
            color_scheme (str): 颜色方案
            timezone (str): 时区设置
        """
        pass
        
    def stop_recording(self) -> None:
        """停止录制"""
        pass
        
    def convert_to_workflow(self) -> dict:
        """
        转换为工作流定义
        
        Returns:
            dict: 工作流定义
        """
        pass
```

### EventHandler

事件处理基类，用于处理录制过程中的各种事件。

```python
class EventHandler:
    async def handle_click(self, event: dict) -> None:
        """
        处理点击事件
        
        Args:
            event (dict): 事件数据
        """
        pass
        
    async def handle_input(self, event: dict) -> None:
        """
        处理输入事件
        
        Args:
            event (dict): 事件数据
        """
        pass
        
    async def handle_navigation(self, event: dict) -> None:
        """
        处理导航事件
        
        Args:
            event (dict): 事件数据
        """
        pass
```

## 执行器 API

### WorkflowExecutor

工作流执行器的核心类，负责执行工作流定义。

```python
class WorkflowExecutor:
    def __init__(
        self,
        workflow_file: str,
        headless: bool = False,
        debug: bool = False
    ):
        """
        初始化执行器
        
        Args:
            workflow_file (str): 工作流文件路径
            headless (bool): 是否无头模式
            debug (bool): 是否调试模式
        """
        pass
        
    async def execute(self) -> dict:
        """
        执行工作流
        
        Returns:
            dict: 执行结果
        """
        pass
        
    async def execute_step(self, step: Step) -> dict:
        """
        执行单个步骤
        
        Args:
            step (Step): 步骤对象
            
        Returns:
            dict: 执行结果
        """
        pass
        
    async def handle_error(self, error: Exception) -> None:
        """
        处理错误
        
        Args:
            error (Exception): 错误对象
        """
        pass
```

### Step

步骤基类，定义了步骤的基本接口。

```python
class Step:
    def __init__(self, data: dict):
        """
        初始化步骤
        
        Args:
            data (dict): 步骤数据
        """
        pass
        
    async def execute(self, context: dict) -> dict:
        """
        执行步骤
        
        Args:
            context (dict): 执行上下文
            
        Returns:
            dict: 执行结果
        """
        pass
        
    async def validate(self) -> bool:
        """
        验证步骤
        
        Returns:
            bool: 验证结果
        """
        pass
```

## 监控器 API

### BrowserMonitor

浏览器监控器的核心类，负责收集和分析浏览器数据。

```python
class BrowserMonitor:
    def __init__(
        self,
        config: dict = None,
        metrics: list = None,
        alert: bool = False
    ):
        """
        初始化监控器
        
        Args:
            config (dict): 配置信息
            metrics (list): 指标列表
            alert (bool): 是否启用告警
        """
        pass
        
    async def start(self) -> None:
        """启动监控"""
        pass
        
    async def stop(self) -> None:
        """停止监控"""
        pass
        
    async def collect_metrics(self) -> dict:
        """
        收集指标
        
        Returns:
            dict: 指标数据
        """
        pass
        
    async def analyze_data(self, metrics: dict) -> dict:
        """
        分析数据
        
        Args:
            metrics (dict): 指标数据
            
        Returns:
            dict: 分析结果
        """
        pass
```

### Metric

指标基类，定义了指标的基本接口。

```python
class Metric:
    def __init__(self, config: dict = None):
        """
        初始化指标
        
        Args:
            config (dict): 配置信息
        """
        pass
        
    async def collect(self) -> dict:
        """
        收集指标数据
        
        Returns:
            dict: 指标数据
        """
        pass
        
    async def analyze(self, data: dict) -> dict:
        """
        分析指标数据
        
        Args:
            data (dict): 指标数据
            
        Returns:
            dict: 分析结果
        """
        pass
```

## AI API

### BaseAI

AI 引擎基类，提供 AI 能力支持。

```python
class BaseAI:
    def __init__(self, config: dict = None):
        """
        初始化 AI 引擎
        
        Args:
            config (dict): 配置信息
        """
        pass
        
    async def analyze(self, data: dict) -> dict:
        """
        分析数据
        
        Args:
            data (dict): 输入数据
            
        Returns:
            dict: 分析结果
        """
        pass
        
    async def optimize(self, workflow: dict) -> dict:
        """
        优化工作流
        
        Args:
            workflow (dict): 工作流定义
            
        Returns:
            dict: 优化结果
        """
        pass
        
    async def repair(self, error: Exception) -> dict:
        """
        修复错误
        
        Args:
            error (Exception): 错误对象
            
        Returns:
            dict: 修复结果
        """
        pass
```

### Repair

错误修复基类，提供自动修复能力。

```python
class Repair:
    def __init__(self, config: dict = None):
        """
        初始化修复器
        
        Args:
            config (dict): 配置信息
        """
        pass
        
    async def diagnose(self, error: Exception) -> dict:
        """
        诊断错误
        
        Args:
            error (Exception): 错误对象
            
        Returns:
            dict: 诊断结果
        """
        pass
        
    async def repair(self, diagnosis: dict) -> dict:
        """
        执行修复
        
        Args:
            diagnosis (dict): 诊断结果
            
        Returns:
            dict: 修复结果
        """
        pass
```

## 工具 API

### Browser

浏览器工具类，提供浏览器操作相关功能。

```python
class Browser:
    @staticmethod
    async def launch(
        headless: bool = False,
        viewport: tuple = None,
        device: str = None
    ) -> "Browser":
        """
        启动浏览器
        
        Args:
            headless (bool): 是否无头模式
            viewport (tuple): 视口大小
            device (str): 设备名称
            
        Returns:
            Browser: 浏览器实例
        """
        pass
        
    async def new_page(self) -> "Page":
        """
        创建新页面
        
        Returns:
            Page: 页面实例
        """
        pass
        
    async def close(self) -> None:
        """关闭浏览器"""
        pass
```

### Logger

日志工具类，提供日志记录功能。

```python
class Logger:
    def __init__(
        self,
        name: str,
        level: str = "INFO",
        file: str = None
    ):
        """
        初始化日志器
        
        Args:
            name (str): 日志器名称
            level (str): 日志级别
            file (str): 日志文件路径
        """
        pass
        
    def info(self, message: str) -> None:
        """
        记录信息日志
        
        Args:
            message (str): 日志消息
        """
        pass
        
    def error(self, message: str, exc_info: bool = True) -> None:
        """
        记录错误日志
        
        Args:
            message (str): 日志消息
            exc_info (bool): 是否包含异常信息
        """
        pass
```

### Config

配置工具类，提供配置管理功能。

```python
class Config:
    @staticmethod
    def load(file: str) -> dict:
        """
        加载配置文件
        
        Args:
            file (str): 配置文件路径
            
        Returns:
            dict: 配置信息
        """
        pass
        
    @staticmethod
    def save(config: dict, file: str) -> None:
        """
        保存配置文件
        
        Args:
            config (dict): 配置信息
            file (str): 配置文件路径
        """
        pass
```

## 数据模型

### Workflow

工作流定义模型。

```python
class Workflow(BaseModel):
    """工作流定义"""
    
    name: str
    version: str
    steps: List[Step]
    variables: Dict[str, Any] = {}
    
    class Config:
        schema_extra = {
            "example": {
                "name": "login_workflow",
                "version": "1.0.0",
                "steps": [
                    {
                        "id": "step_1",
                        "type": "navigate",
                        "url": "https://example.com"
                    }
                ],
                "variables": {
                    "username": "test_user"
                }
            }
        }
```

### Step

步骤定义模型。

```python
class Step(BaseModel):
    """步骤定义"""
    
    id: str
    type: str
    description: str = None
    metadata: Dict[str, Any] = {}
    
    class Config:
        schema_extra = {
            "example": {
                "id": "step_1",
                "type": "click",
                "description": "点击登录按钮",
                "metadata": {
                    "selector": "#login-button"
                }
            }
        }
```

### Metric

指标数据模型。

```python
class Metric(BaseModel):
    """指标数据"""
    
    name: str
    value: Any
    timestamp: datetime
    metadata: Dict[str, Any] = {}
    
    class Config:
        schema_extra = {
            "example": {
                "name": "page_load_time",
                "value": 1234,
                "timestamp": "2024-03-19T10:30:00Z",
                "metadata": {
                    "url": "https://example.com"
                }
            }
        }
```

## 错误定义

### WorkflowError

工作流相关错误。

```python
class WorkflowError(Exception):
    """工作流错误基类"""
    pass

class WorkflowValidationError(WorkflowError):
    """工作流验证错误"""
    pass

class WorkflowExecutionError(WorkflowError):
    """工作流执行错误"""
    pass
```

### MonitorError

监控相关错误。

```python
class MonitorError(Exception):
    """监控错误基类"""
    pass

class MetricCollectionError(MonitorError):
    """指标收集错误"""
    pass

class AlertError(MonitorError):
    """告警错误"""
    pass
```

## 常量定义

### StepType

步骤类型常量。

```python
class StepType:
    NAVIGATE = "navigate"
    CLICK = "click"
    INPUT = "input"
    SELECT = "select"
    WAIT = "wait"
    SCREENSHOT = "screenshot"
```

### MetricType

指标类型常量。

```python
class MetricType:
    PERFORMANCE = "performance"
    NETWORK = "network"
    DOM = "dom"
    CONSOLE = "console"
    RESOURCE = "resource"
```

## 工具函数

### selector

选择器相关工具函数。

```python
def get_best_selector(element: Element) -> str:
    """
    获取最佳选择器
    
    Args:
        element (Element): 页面元素
        
    Returns:
        str: 选择器字符串
    """
    pass

def validate_selector(selector: str) -> bool:
    """
    验证选择器
    
    Args:
        selector (str): 选择器字符串
        
    Returns:
        bool: 验证结果
    """
    pass
```

### utils

通用工具函数。

```python
def generate_id() -> str:
    """
    生成唯一ID
    
    Returns:
        str: ID字符串
    """
    pass

def merge_config(base: dict, override: dict) -> dict:
    """
    合并配置
    
    Args:
        base (dict): 基础配置
        override (dict): 覆盖配置
        
    Returns:
        dict: 合并后的配置
    """
    pass
```

## 下一步

- 查看[开发者指南](../README.md)了解如何使用这些 API
- 查看[示例代码](../../examples/README.md)了解具体使用方法
- 查看[故障排除](../../06-advanced/troubleshooting/README.md)了解如何解决常见问题 