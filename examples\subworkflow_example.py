"""
子工作流示例

展示如何在主工作流中嵌入和执行子工作流。
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加项目根目录到Python路径
import sys
from pathlib import Path
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入工作流引擎
from src.workflow.engine.workflow import Workflow, Node, Edge, NodeType
from src.workflow.engine.executor import WorkflowExecutor

# 导入操作执行器
try:
    from src.operations.executor import OperationExecutor
except ImportError:
    # 如果operations模块不存在，创建一个简单的模拟类
    class OperationExecutor:
        def __init__(self):
            self.operations = {}
            
        def register_operation(self, name, operation=None):
            if operation is None:
                def decorator(op):
                    self.operations[name] = op
                    return op
                return decorator
            self.operations[name] = operation
            
        async def execute_operation(self, operation_id, params, context=None):
            if operation_id not in self.operations:
                raise ValueError(f"Operation {operation_id} not found")
                
            operation = self.operations[operation_id]
            if callable(operation):
                return await operation(**(params or {}), **(context or {}))
            elif hasattr(operation, 'execute') and callable(operation.execute):
                return await operation.execute(params or {}, context or {})
            else:
                raise ValueError(f"Invalid operation: {operation_id}")

# 配置日志
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建子工作流
def create_subworkflow() -> Workflow:
    """创建一个简单的子工作流"""
    workflow = Workflow(
        workflow_id="subworkflow_example",
        name="子工作流示例",
        description="一个简单的子工作流示例"
    )
    
    # 添加节点
    start_node = Node(
        id="start",
        type=NodeType.START,
        name="开始"
    )
    
    task1_node = Node(
        id="sub_task1",
        type=NodeType.TASK,
        name="子任务1",
        metadata={
            "operation_id": "log_message",
            "params": {
                "message": "这是子工作流的任务1"
            }
        }
    )
    
    task2_node = Node(
        id="sub_task2",
        type=NodeType.TASK,
        name="子任务2",
        metadata={
            "operation_id": "log_message",
            "params": {
                "message": "这是子工作流的任务2"
            },
            "output": "subworkflow_result"
        }
    )
    
    end_node = Node(
        id="end",
        type=NodeType.END,
        name="结束"
    )
    
    # 添加边
    workflow.add_node(start_node)
    workflow.add_node(task1_node)
    workflow.add_node(task2_node)
    workflow.add_node(end_node)
    
    workflow.add_edge(Edge(source_id="start", target_id="sub_task1"))
    workflow.add_edge(Edge(source_id="sub_task1", target_id="sub_task2"))
    workflow.add_edge(Edge(source_id="sub_task2", target_id="end"))
    
    return workflow

# 创建主工作流
def create_main_workflow() -> Workflow:
    """创建主工作流，包含子工作流节点"""
    workflow = Workflow(
        workflow_id="main_workflow",
        name="主工作流",
        description="包含子工作流的主工作流示例"
    )
    
    # 添加节点
    start_node = Node(
        id="start",
        type=NodeType.START,
        name="开始"
    )
    
    # 主工作流任务1
    task1_node = Node(
        id="main_task1",
        type=NodeType.TASK,
        name="主任务1",
        metadata={
            "operation_id": "log_message",
            "params": {
                "message": "这是主工作流的任务1"
            }
        }
    )
    
    # 子工作流节点
    subworkflow = create_subworkflow()
    subworkflow_node = Node(
        id="subworkflow_node",
        type=NodeType.SUBWORKFLOW,
        name="执行子工作流",
        metadata={
            "workflow": subworkflow,
            "update_parent_context": True,  # 更新父工作流上下文
            "output": "subworkflow_output",  # 输出变量名
            "ignore_errors": False  # 是否忽略子工作流错误
        }
    )
    
    # 主工作流任务2
    task2_node = Node(
        id="main_task2",
        type=NodeType.TASK,
        name="主任务2",
        metadata={
            "operation_id": "log_message",
            "params": {
                "message": "这是主工作流的任务2，子工作流结果: {{subworkflow_output}}"
            }
        }
    )
    
    end_node = Node(
        id="end",
        type=NodeType.END,
        name="结束"
    )
    
    # 添加节点
    workflow.add_node(start_node)
    workflow.add_node(task1_node)
    workflow.add_node(subworkflow_node)
    workflow.add_node(task2_node)
    workflow.add_node(end_node)
    
    # 添加边
    workflow.add_edge(Edge(source_id="start", target_id="main_task1"))
    workflow.add_edge(Edge(source_id="main_task1", target_id="subworkflow_node"))
    workflow.add_edge(Edge(source_id="subworkflow_node", target_id="main_task2"))
    workflow.add_edge(Edge(source_id="main_task2", target_id="end"))
    
    return workflow

async def main():
    """主函数"""
    print("=" * 50)
    print("Starting workflow engine with subworkflow example...")
    print("=" * 50)
    print()
    
    # 初始化操作执行器
    operation_executor = OperationExecutor()
    
    # 注册一个简单的日志操作
    @operation_executor.register_operation("log_message")
    async def log_message_operation(message: str, **kwargs):
        """简单的日志操作"""
        logger.info(f"[Operation] {message}")
        print(f"[Operation] {message}")  # 同时输出到控制台
        return {"status": "success", "message": message}
    
    # 创建工作流执行器
    workflow_executor = WorkflowExecutor(operation_executor)
    
    # 创建工作流
    workflow = create_main_workflow()
    
    # 验证工作流
    print("\nValidating workflow...")
    if not workflow.validate():
        print("❌ Workflow validation failed!")
        return
    print("✅ Workflow validation passed")
    
    print(f"Workflow loaded: {workflow.name} (ID: {workflow.workflow_id})")
    
    try:
        # 执行工作流
        print("\n" + "=" * 50)
        print("🚀 Starting workflow execution...")
        print("=" * 50 + "\n")
        
        result = await workflow_executor.execute(workflow)
            
        # 输出执行结果
        print("\nWorkflow execution completed!")
        print(f"Status: {result.status}")
        print(f"Duration: {result.end_time - result.start_time:.2f} seconds")
        
        # 输出节点执行结果
        print("\nNode execution results:")
        for node_id, node_result in result.node_results.items():
            print(f"\nNode: {node_id} ({node_result['node_type']})")
            print(f"Status: {node_result['status']}")
            print(f"Duration: {node_result['duration']:.2f}s")
            if node_result.get('error'):
                print(f"Error: {node_result['error']}")
    
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    asyncio.run(main())
