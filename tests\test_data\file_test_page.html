<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Test Page</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .container { 
            margin: 20px 0; 
            padding: 20px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
        }
        button { 
            background: #4CAF50; 
            color: white; 
            padding: 10px 15px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px;
        }
        .hidden { display: none; }
        #file-list { margin: 10px 0; }
        .file-item { 
            padding: 5px; 
            margin: 5px 0; 
            background: #f5f5f5; 
            border-radius: 3px; 
        }
    </style>
</head>
<body>
    <h1>File Test Page</h1>
    
    <div class="container">
        <h2>File Upload</h2>
        <input type="file" id="file-upload">
        <button id="upload-btn">Upload</button>
        <div id="upload-status" class="hidden"></div>
        <div id="file-list"></div>
    </div>
    
    <div class="container">
        <h2>File Download</h2>
        <button id="download-txt">Download Text File</button>
        <button id="download-json">Download JSON File</button>
        <div id="download-status" class="hidden"></div>
    </div>

    <script>
        // 存储控制台日志
        window.consoleMessages = [];
        const originalConsoleLog = console.log;
        console.log = function(message) {
            const msg = String(message);
            window.consoleMessages.push(msg);
            originalConsoleLog.apply(console, arguments);
        };

        document.addEventListener('DOMContentLoaded', function() {
            console.log('File test page loaded');
            
            // 文件上传
            const fileUpload = document.getElementById('file-upload');
            const uploadBtn = document.getElementById('upload-btn');
            const uploadStatus = document.getElementById('upload-status');
            const fileList = document.getElementById('file-list');
            
            uploadBtn.addEventListener('click', function() {
                const files = fileUpload.files;
                if (files.length === 0) {
                    showStatus(uploadStatus, 'Please select a file first', 'error');
                    return;
                }
                
                showStatus(uploadStatus, 'Uploading file...', 'info');
                
                // 模拟上传延迟
                setTimeout(() => {
                    const file = files[0];
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.textContent = `Uploaded: ${file.name} (${formatFileSize(file.size)})`;
                    fileList.appendChild(fileItem);
                    
                    showStatus(uploadStatus, 'File uploaded successfully!', 'success');
                    console.log(`File uploaded: ${file.name}, size: ${file.size} bytes`);
                }, 1000);
            });
            
            // 文件下载
            document.getElementById('download-txt').addEventListener('click', function() {
                downloadFile('test.txt', 'This is a test text file.');
                console.log('Downloaded: test.txt');
            });
            
            document.getElementById('download-json').addEventListener('click', function() {
                const data = { name: 'Test', value: 123, items: ['a', 'b', 'c'] };
                downloadFile('data.json', JSON.stringify(data, null, 2));
                console.log('Downloaded: data.json');
            });
            
            // 辅助函数
            function showStatus(element, message, type) {
                element.textContent = message;
                element.className = type || '';
                element.classList.remove('hidden');
                
                // 3秒后隐藏状态
                setTimeout(() => {
                    element.classList.add('hidden');
                }, 3000);
            }
            
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            function downloadFile(filename, text) {
                const element = document.createElement('a');
                element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(text));
                element.setAttribute('download', filename);
                element.style.display = 'none';
                document.body.appendChild(element);
                element.click();
                document.body.removeChild(element);
            }
        });
    </script>
</body>
</html>
