# 自愈系统使用指南

## 目录
- [简介](#简介)
- [快速开始](#快速开始)
- [核心概念](#核心概念)
- [API 参考](#api-参考)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)
- [示例](#示例)

## 简介

自愈系统是一个强大的自动化测试工具，能够在网页元素发生变化时自动调整定位策略，从而提高测试的稳定性和可靠性。它通过多种定位策略和智能算法，在元素定位失败时自动尝试其他可行的定位方式。

### 主要特性

- **多策略定位**：支持 CSS、XPath、文本、ARIA 角色等多种定位策略
- **智能恢复**：当元素定位失败时自动尝试其他定位方式
- **上下文感知**：利用页面上下文信息提高定位准确性
- **易于集成**：提供简洁的 API 与现有测试框架集成
- **可扩展**：支持自定义定位策略和恢复逻辑

## 快速开始

### 安装

确保已安装 Python 3.8+ 和 Playwright：

```bash
pip install playwright
playwright install
```

### 基本用法

```python
import asyncio
from playwright.async_api import async_playwright
from src.workflow.self_healing import SelfHealingMiddleware

async def main():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # 启用自愈中间件
        middleware = SelfHealingMiddleware(page)
        
        # 访问测试页面
        await page.goto('https://example.com')
        
        # 使用自愈定位器点击元素
        element = await middleware.locator.find_element('button.submit', strategy='css')
        if element:
            await element.click()
        
        await browser.close()

asyncio.run(main())
```

## 核心概念

### 定位策略 (LocatorStrategy)

自愈系统支持多种定位策略：

- **CSS**：使用 CSS 选择器定位元素
- **XPATH**：使用 XPath 表达式定位元素
- **TEXT**：通过元素文本内容定位
- **ROLE**：通过 ARIA 角色定位
- **TEST_ID**：通过测试 ID 定位
- **LABEL**：通过关联的标签文本定位
- **PLACEHOLDER**：通过占位符文本定位
- **TITLE**：通过标题属性定位
- **ALT_TEXT**：通过 alt 文本定位（适用于图片）
- **VISUAL**：通过视觉匹配定位（实验性）

### 自愈定位器 (SelfHealingLocator)

自愈定位器是系统的核心组件，负责：

1. 根据指定策略定位元素
2. 在定位失败时尝试其他策略
3. 评估和选择最佳定位方式
4. 缓存成功的定位策略

### 中间件 (SelfHealingMiddleware)

中间件提供了与 Playwright 页面对象集成的接口，可以：

1. 包装页面操作方法（如 click、fill 等）
2. 在操作失败时自动触发自愈逻辑
3. 收集和报告自愈过程的信息

## API 参考

### SelfHealingMiddleware

#### 初始化

```python
middleware = SelfHealingMiddleware(
    page,                  # Playwright 页面对象
    config={}              # 可选配置
)
```

#### 配置选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `enabled` | bool | True | 是否启用自愈功能 |
| `retry_attempts` | int | 3 | 重试次数 |
| `retry_delay` | float | 0.5 | 重试间隔（秒） |
| `strategies` | list | ['css', 'xpath', 'text', 'role'] | 定位策略优先级 |
| `log_level` | str | 'info' | 日志级别 |

#### 方法

##### `execute_step(step, next_step)`

执行一个测试步骤，在失败时尝试自愈。

参数：
- `step` (dict): 步骤定义
- `next_step` (callable): 下一步执行函数

返回：
- 任意: 下一步执行结果

### SelfHealingLocator

#### 初始化

```python
locator = SelfHealingLocator(page)
```

#### 方法

##### `find_element(selector, strategy, context=None)`

查找元素，支持自愈。

参数：
- `selector` (str): 选择器
- `strategy` (str/LocatorStrategy): 定位策略
- `context` (dict, optional): 上下文信息

返回：
- ElementHandle: 找到的元素，如果未找到则返回 None

## 最佳实践

### 1. 优先使用稳定的选择器

```python
# 不推荐
await page.click('div.container > div > button')

# 推荐
await page.click('[data-testid="submit-button"]')
```

### 2. 使用自愈中间件包装关键操作

```python
# 包装页面方法
original_click = page.click
async def click_with_self_healing(selector, **kwargs):
    try:
        return await original_click(selector, **kwargs)
    except Exception as e:
        if "element not found" in str(e).lower():
            element = await middleware.locator.find_element(selector, 'css')
            if element:
                return await element.click()
        raise

page.click = click_with_self_healing
```

### 3. 提供上下文信息

```python
element = await locator.find_element(
    'button.submit',
    strategy='css',
    context={
        'page_url': page.url,
        'page_title': await page.title(),
        'element_text': 'Submit',
        'element_attributes': {'type': 'submit'}
    }
)
```

## 故障排除

### 常见问题

#### 1. 自愈失败

**问题**：元素仍然无法定位。

**解决方案**：
- 检查页面是否已完全加载
- 验证选择器是否正确
- 增加重试次数和延迟
- 检查是否有 iframe 或 shadow DOM

#### 2. 性能问题

**问题**：测试运行变慢。

**解决方案**：
- 减少重试次数
- 限制定位策略数量
- 使用更具体的选择器

## 示例

### 完整示例

```python
import asyncio
from playwright.async_api import async_playwright
from src.workflow.self_healing import SelfHealingMiddleware

async def run_test():
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        # 初始化自愈中间件
        middleware = SelfHealingMiddleware(page, config={
            'retry_attempts': 3,
            'retry_delay': 0.5,
            'strategies': ['css', 'xpath', 'text', 'role']
        })
        
        try:
            # 访问测试页面
            await page.goto('https://example.com')
            
            # 使用自愈定位器查找元素
            element = await middleware.locator.find_element(
                'button.primary',
                strategy='css',
                context={
                    'page_url': page.url,
                    'element_text': 'Submit'
                }
            )
            
            if element:
                await element.click()
                print("Successfully clicked the button!")
            else:
                print("Element not found")
                
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(run_test())
```

### 与 Pytest 集成

```python
import pytest
from playwright.async_api import async_playwright
from src.workflow.self_healing import SelfHealingMiddleware

@pytest.fixture
async def page():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        context = await browser.new_context()
        page = await context.new_page()
        
        # 初始化自愈中间件
        middleware = SelfHealingMiddleware(page)
        
        yield page
        
        await browser.close()

@pytest.mark.asyncio
async def test_login(page):
    await page.goto('https://example.com/login')
    
    # 使用自愈定位器查找元素
    username = await page.locator('input[name="username"]')
    password = await page.locator('input[type="password"]')
    
    await username.fill('testuser')
    await password.fill('password123')
    
    # 点击登录按钮（带自愈）
    login_button = await page.locator('button[type="submit"]')
    await login_button.click()
    
    # 验证登录成功
    assert await page.is_visible('text=Welcome, testuser!')
```

## 贡献指南

欢迎提交 Issue 和 Pull Request。在提交代码前，请确保：

1. 通过所有测试
2. 更新相关文档
3. 遵循代码风格指南

## 许可证

MIT
