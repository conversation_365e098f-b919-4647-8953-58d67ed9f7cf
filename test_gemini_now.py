"""
立即测试Gemini配置
"""
import os
import sys

# 添加src目录
sys.path.insert(0, 'src')

print("🎭 立即测试Gemini配置")
print("=" * 30)

# 1. 检查环境变量
print("1. 检查环境变量:")
gemini_key = os.getenv('GEMINI_API_KEY')
if gemini_key:
    print(f"   ✅ GEMINI_API_KEY: 已设置 (长度: {len(gemini_key)})")
else:
    print("   ❌ GEMINI_API_KEY: 未设置")

gemini_model = os.getenv('GEMINI_MODEL', 'gemini-pro')
print(f"   ✅ GEMINI_MODEL: {gemini_model}")

# 2. 测试导入
print("\n2. 测试导入:")
try:
    import google.generativeai as genai
    print("   ✅ google-generativeai 导入成功")
    genai_available = True
except ImportError as e:
    print(f"   ❌ google-generativeai 导入失败: {e}")
    genai_available = False

# 3. 测试LLM管理器
print("\n3. 测试LLM管理器:")
try:
    from ai_llm_manager import get_llm_manager, LLMProvider
    
    manager = get_llm_manager()
    available_providers = manager.get_available_providers()
    
    print(f"   可用提供商: {[p.value for p in available_providers]}")
    
    if LLMProvider.GEMINI in available_providers:
        print("   ✅ Gemini在管理器中可用")
    else:
        print("   ❌ Gemini在管理器中不可用")
        
except Exception as e:
    print(f"   ❌ LLM管理器测试失败: {e}")

# 4. 如果可能，测试Gemini API调用
if genai_available and gemini_key:
    print("\n4. 测试Gemini API调用:")
    try:
        genai.configure(api_key=gemini_key)
        model = genai.GenerativeModel(gemini_model)
        
        response = model.generate_content("请说'Hello from Gemini'")
        print(f"   ✅ API调用成功")
        print(f"   📝 响应: {response.text}")
        
    except Exception as e:
        print(f"   ❌ API调用失败: {e}")

print("\n✅ Gemini配置检查完成")
