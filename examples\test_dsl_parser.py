"""
测试工作流DSL解析器
"""
import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from workflow.dsl import DSLParser, WorkflowDefinition

async def test_parse_workflow():
    """测试解析工作流定义"""
    print("=== 测试解析工作流定义 ===\n")
    
    # 初始化解析器
    parser = DSLParser()
    
    # 加载示例工作流文件
    workflow_file = Path(__file__).parent / "dsl_workflow_example.yaml"
    print(f"加载工作流文件: {workflow_file}\n")
    
    try:
        # 解析工作流
        workflow = parser.parse_file(workflow_file)
        print("✅ 工作流解析成功!")
        print(f"工作流ID: {workflow.id}")
        print(f"工作流名称: {workflow.name}")
        print(f"版本: {workflow.version}")
        print(f"描述: {workflow.description}")
        print(f"起始节点: {workflow.start_at}")
        print(f"节点数量: {len(workflow.nodes)}")
        print("\n节点列表:")
        for node_id, node in workflow.nodes.items():
            print(f"- {node_id} ({node.type}): {node.name}")
        
        # 验证工作流
        print("\n验证工作流...")
        is_valid = parser.validate_workflow(workflow)
        print(f"✅ 工作流验证{'成功' if is_valid else '失败'}")
        
        # 转换为字典并打印
        print("\n工作流定义(JSON格式):")
        print(parser.to_json(workflow, indent=2))
        
        return True
        
    except Exception as e:
        print(f"❌ 解析工作流失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("=== 工作流DSL解析器测试 ===\n")
    
    # 测试解析工作流
    success = await test_parse_workflow()
    
    print("\n=== 测试完成 ===")
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
