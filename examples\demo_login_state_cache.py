"""
登录状态缓存完整演示

演示登录状态缓存的完整使用流程
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value


async def demo_login_state_cache():
    """演示登录状态缓存完整流程"""
    print("🎭 登录状态缓存完整演示")
    print("=" * 50)
    
    try:
        from ai_login_workflow_generator import get_login_state_executor
        from login_state_manager import get_login_state_manager
        
        # 获取管理器和执行器
        state_manager = get_login_state_manager()
        state_executor = get_login_state_executor()
        
        print("✅ 登录状态缓存系统初始化完成")
        
        while True:
            print(f"\n🎯 登录状态缓存系统菜单:")
            print(f"   1. 📋 查看所有登录会话")
            print(f"   2. 🚀 使用缓存登录状态")
            print(f"   3. 💾 手动保存登录状态")
            print(f"   4. 🗑️ 删除登录会话")
            print(f"   5. 🧪 测试登录状态验证")
            print(f"   6. 📊 显示会话统计")
            print(f"   7. ❌ 退出")
            
            choice = input(f"\n请选择操作 (1-7): ").strip()
            
            if choice == "1":
                await show_all_sessions(state_manager)
            elif choice == "2":
                await use_cached_login(state_executor)
            elif choice == "3":
                await manual_save_login_state(state_manager)
            elif choice == "4":
                await delete_login_session(state_manager)
            elif choice == "5":
                await test_login_state_validation(state_manager)
            elif choice == "6":
                await show_session_statistics(state_manager)
            elif choice == "7":
                print("👋 退出登录状态缓存系统")
                break
            else:
                print("❌ 无效的选择，请重新输入")
        
    except Exception as e:
        print(f"❌ 演示程序异常: {e}")


async def show_all_sessions(state_manager):
    """显示所有登录会话"""
    print(f"\n📋 所有登录会话")
    print("-" * 30)
    
    try:
        sessions = state_manager.list_login_sessions()
        
        if not sessions:
            print("   📭 暂无登录会话")
            return
        
        print(f"   📊 共找到 {len(sessions)} 个登录会话:")
        
        for i, session in enumerate(sessions, 1):
            status = "✅ 有效" if session['is_valid'] else "❌ 已过期"
            print(f"\n   {i}. {session['name']}")
            print(f"      会话ID: {session['session_id']}")
            print(f"      域名: {session['domain']}")
            print(f"      描述: {session['description']}")
            print(f"      创建时间: {session['created_at']}")
            print(f"      最后使用: {session['last_used']}")
            print(f"      使用次数: {session['use_count']}")
            print(f"      状态: {status}")
        
    except Exception as e:
        print(f"   ❌ 显示会话失败: {e}")


async def use_cached_login(state_executor):
    """使用缓存登录状态"""
    print(f"\n🚀 使用缓存登录状态")
    print("-" * 30)
    
    try:
        sessions = await state_executor.list_available_sessions()
        
        if not sessions:
            print("   📭 暂无可用的登录会话")
            return
        
        # 只显示有效的会话
        valid_sessions = [s for s in sessions if s['is_valid']]
        
        if not valid_sessions:
            print("   ⚠️ 没有有效的登录会话")
            return
        
        print("   📋 可用的登录会话:")
        for i, session in enumerate(valid_sessions, 1):
            print(f"      {i}. {session['name']} ({session['domain']})")
        
        choice = input(f"\n   选择要使用的会话 (1-{len(valid_sessions)}): ").strip()
        
        try:
            session_index = int(choice) - 1
            if 0 <= session_index < len(valid_sessions):
                selected_session = valid_sessions[session_index]
                session_id = selected_session['session_id']
                
                print(f"   🔄 正在恢复登录状态: {selected_session['name']}")
                
                # 询问目标URL
                target_url = input("   目标URL (可选，按回车跳过): ").strip()
                target_url = target_url if target_url else None
                
                # 执行缓存登录
                result = await state_executor.execute_with_cached_login(
                    session_id=session_id,
                    target_url=target_url,
                    headless=False
                )
                
                if result['success']:
                    print("   ✅ 缓存登录成功")
                    print(f"      会话名称: {result['session_name']}")
                    print(f"      当前URL: {result['current_url']}")
                    print(f"      执行时间: {result['execution_time']:.2f}秒")
                    
                    print(f"\n   🎉 浏览器已打开，您现在可以:")
                    print(f"      • 直接在系统中操作")
                    print(f"      • 无需重新登录")
                    print(f"      • 享受快速访问")
                    
                    input("   👀 请查看浏览器中的登录状态，按回车关闭...")
                    
                    # 关闭浏览器
                    try:
                        await result['browser'].close()
                    except Exception:
                        pass
                else:
                    print(f"   ❌ 缓存登录失败: {result['error']}")
            else:
                print("   ❌ 无效的选择")
                
        except ValueError:
            print("   ❌ 无效的输入")
        
    except Exception as e:
        print(f"   ❌ 使用缓存登录失败: {e}")


async def manual_save_login_state(state_manager):
    """手动保存登录状态"""
    print(f"\n💾 手动保存登录状态")
    print("-" * 30)
    
    print("   ℹ️ 此功能需要您先手动登录到目标网站")
    print("   然后在登录成功的页面上保存状态")
    
    proceed = input("   是否继续? (y/n): ").strip().lower()
    if proceed not in ['y', 'yes']:
        return
    
    try:
        from playwright.async_api import async_playwright
        
        # 获取保存信息
        session_name = input("   会话名称: ").strip()
        description = input("   会话描述 (可选): ").strip()
        target_url = input("   登录页面URL: ").strip()
        
        if not session_name or not target_url:
            print("   ❌ 会话名称和URL不能为空")
            return
        
        print(f"   🌐 正在打开浏览器...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            
            try:
                # 导航到登录页面
                await page.goto(target_url)
                await page.wait_for_load_state('networkidle')
                
                print(f"   📱 请在浏览器中完成登录")
                input("   登录完成后，按回车保存登录状态...")
                
                # 保存登录状态
                session_id = await state_manager.save_login_session(
                    page,
                    session_name,
                    description or f"手动保存 - {session_name}"
                )
                
                print(f"   ✅ 登录状态保存成功")
                print(f"      会话ID: {session_id}")
                print(f"      会话名称: {session_name}")
                
            finally:
                await browser.close()
        
    except Exception as e:
        print(f"   ❌ 手动保存登录状态失败: {e}")


async def delete_login_session(state_manager):
    """删除登录会话"""
    print(f"\n🗑️ 删除登录会话")
    print("-" * 30)
    
    try:
        sessions = state_manager.list_login_sessions()
        
        if not sessions:
            print("   📭 暂无登录会话")
            return
        
        print("   📋 现有登录会话:")
        for i, session in enumerate(sessions, 1):
            status = "✅ 有效" if session['is_valid'] else "❌ 已过期"
            print(f"      {i}. {session['name']} - {status}")
        
        choice = input(f"\n   选择要删除的会话 (1-{len(sessions)}): ").strip()
        
        try:
            session_index = int(choice) - 1
            if 0 <= session_index < len(sessions):
                selected_session = sessions[session_index]
                session_id = selected_session['session_id']
                session_name = selected_session['name']
                
                confirm = input(f"   确认删除会话 '{session_name}'? (y/n): ").strip().lower()
                if confirm in ['y', 'yes']:
                    success = await state_manager.delete_login_session(session_id)
                    
                    if success:
                        print(f"   ✅ 会话删除成功: {session_name}")
                    else:
                        print(f"   ❌ 会话删除失败: {session_name}")
                else:
                    print("   ⚠️ 取消删除")
            else:
                print("   ❌ 无效的选择")
                
        except ValueError:
            print("   ❌ 无效的输入")
        
    except Exception as e:
        print(f"   ❌ 删除会话失败: {e}")


async def test_login_state_validation(state_manager):
    """测试登录状态验证"""
    print(f"\n🧪 测试登录状态验证")
    print("-" * 30)
    
    try:
        sessions = state_manager.list_login_sessions()
        
        if not sessions:
            print("   📭 暂无登录会话")
            return
        
        valid_sessions = [s for s in sessions if s['is_valid']]
        
        if not valid_sessions:
            print("   ⚠️ 没有有效的登录会话")
            return
        
        print("   📋 有效的登录会话:")
        for i, session in enumerate(valid_sessions, 1):
            print(f"      {i}. {session['name']}")
        
        choice = input(f"\n   选择要验证的会话 (1-{len(valid_sessions)}): ").strip()
        
        try:
            session_index = int(choice) - 1
            if 0 <= session_index < len(valid_sessions):
                selected_session = valid_sessions[session_index]
                session_id = selected_session['session_id']
                
                print(f"   🔍 正在验证登录状态: {selected_session['name']}")
                
                # 加载会话
                session = await state_manager.load_login_session(session_id)
                
                if not session:
                    print("   ❌ 会话加载失败")
                    return
                
                from playwright.async_api import async_playwright
                
                async with async_playwright() as p:
                    browser = await p.chromium.launch(headless=False)
                    context = await browser.new_context()
                    page = await context.new_page()
                    
                    try:
                        # 恢复登录状态
                        restore_success = await state_manager.restore_login_state(page, session)
                        
                        if restore_success:
                            print("   ✅ 登录状态恢复成功")
                            
                            # 验证登录状态
                            login_valid = await state_manager.verify_login_state(page, session)
                            
                            if login_valid:
                                print("   ✅ 登录状态验证成功")
                                print(f"      当前URL: {page.url}")
                            else:
                                print("   ❌ 登录状态验证失败")
                        else:
                            print("   ❌ 登录状态恢复失败")
                        
                        input("   👀 请查看浏览器中的验证结果，按回车关闭...")
                        
                    finally:
                        await browser.close()
            else:
                print("   ❌ 无效的选择")
                
        except ValueError:
            print("   ❌ 无效的输入")
        
    except Exception as e:
        print(f"   ❌ 验证登录状态失败: {e}")


async def show_session_statistics(state_manager):
    """显示会话统计"""
    print(f"\n📊 会话统计")
    print("-" * 30)
    
    try:
        sessions = state_manager.list_login_sessions()
        
        if not sessions:
            print("   📭 暂无登录会话")
            return
        
        total_sessions = len(sessions)
        valid_sessions = len([s for s in sessions if s['is_valid']])
        expired_sessions = total_sessions - valid_sessions
        
        total_uses = sum(s['use_count'] for s in sessions)
        
        # 按域名分组
        domains = {}
        for session in sessions:
            domain = session['domain']
            if domain not in domains:
                domains[domain] = []
            domains[domain].append(session)
        
        print(f"   📈 总体统计:")
        print(f"      总会话数: {total_sessions}")
        print(f"      有效会话: {valid_sessions}")
        print(f"      过期会话: {expired_sessions}")
        print(f"      总使用次数: {total_uses}")
        
        print(f"\n   🌐 按域名分组:")
        for domain, domain_sessions in domains.items():
            valid_count = len([s for s in domain_sessions if s['is_valid']])
            print(f"      {domain}: {len(domain_sessions)} 个会话 ({valid_count} 个有效)")
        
        if sessions:
            # 最常用的会话
            most_used = max(sessions, key=lambda s: s['use_count'])
            print(f"\n   🏆 最常用会话:")
            print(f"      名称: {most_used['name']}")
            print(f"      使用次数: {most_used['use_count']}")
            
            # 最新的会话
            newest = max(sessions, key=lambda s: s['created_at'])
            print(f"\n   🆕 最新会话:")
            print(f"      名称: {newest['name']}")
            print(f"      创建时间: {newest['created_at']}")
        
    except Exception as e:
        print(f"   ❌ 显示统计失败: {e}")


async def main():
    """主函数"""
    print("🎭 登录状态缓存完整演示系统")
    print("一次登录，多次使用 - 提高自动化效率")
    print("=" * 60)
    
    # 加载环境变量
    load_env()
    
    print("✅ 环境配置加载完成")
    
    try:
        await demo_login_state_cache()
        
        print(f"\n🎯 演示完成总结:")
        print(f"   🔧 登录状态缓存系统功能完整")
        print(f"   💾 支持自动和手动状态保存")
        print(f"   🚀 支持快速状态恢复和验证")
        print(f"   📊 提供完整的会话管理功能")
        
        print(f"\n🏆 登录状态缓存系统的价值:")
        print(f"   • 大幅提高自动化效率")
        print(f"   • 减少重复登录操作")
        print(f"   • 支持长期会话管理")
        print(f"   • 简化复杂的登录流程")
        print(f"   • 实现真正的免登录操作")
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断演示")
    except Exception as e:
        print(f"\n❌ 演示程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
