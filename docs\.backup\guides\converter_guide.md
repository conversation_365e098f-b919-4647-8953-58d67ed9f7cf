# 测试用例转换工具指南

## 概述

测试用例转换工具 (`utils/converter.py`) 提供在Python测试脚本和JSON格式之间相互转换的功能。这个工具支持将Playwright测试脚本转换为结构化的JSON格式，也可以将JSON格式的测试用例转换回可执行的Python脚本。

## 功能特性

- **Python到JSON转换**：将Python测试脚本转换为结构化的JSON格式
- **JSON到Python转换**：将JSON格式的测试用例转换回可执行的Python脚本
- **支持多种操作**：包括页面导航、点击、输入、选择等常见操作
- **断言支持**：自动识别测试中的断言语句并转换为JSON格式
- **性能优化**：针对大文件处理进行了性能优化

## 安装

转换工具已包含在项目依赖中，无需额外安装。

## 使用方法

### Python到JSON转换

```python
from src.utils.converter import TestCaseConverter

# 将Python测试脚本转换为JSON
json_data = TestCaseConverter.python_to_json("path/to/test_script.py")

# 将转换结果保存到文件
TestCaseConverter.python_to_json("path/to/test_script.py", "output.json")
```

### JSON到Python转换

```python
from src.utils.converter import TestCaseConverter

# 将JSON测试用例转换为Python脚本
TestCaseConverter.json_to_python("path/to/test_case.json", "output.py")
```

## 命令行接口

转换工具也可以通过命令行使用：

```bash
# Python到JSON转换
python -m src.utils.converter to-json input.py output.json

# JSON到Python转换
python -m src.utils.converter to-py input.json output.py
```

## 示例

### Python测试脚本示例

```python
"""
登录功能测试

测试用户登录功能是否正常工作
"""

def test_login():
    # 导航到登录页面
    page.goto("https://example.com/login")
    
    # 输入用户名和密码
    page.fill("#username", "testuser")
    page.fill("#password", "testpass123")
    
    # 点击登录按钮
    page.click("button[type='submit']")
    
    # 验证登录成功
    assert "Welcome" in page.text_content("h1")
```

### 转换后的JSON格式

```json
{
  "name": "test_login",
  "description": "登录功能测试\n\n测试用户登录功能是否正常工作",
  "steps": [
    {
      "action": "goto",
      "url": "https://example.com/login",
      "description": "导航到登录页面"
    },
    {
      "action": "fill",
      "selector": "#username",
      "value": "testuser",
      "description": "输入用户名"
    },
    {
      "action": "fill",
      "selector": "#password",
      "value": "testpass123",
      "description": "输入密码"
    },
    {
      "action": "click",
      "selector": "button[type='submit']",
      "description": "点击登录按钮"
    }
  ],
  "assertions": [
    {
      "type": "text_contains",
      "selector": "h1",
      "value": "Welcome",
      "description": "验证登录成功"
    }
  ]
}
```

## 性能考虑

- 对于大型测试脚本，转换过程可能会消耗较多内存
- 建议在CI/CD流水线中使用时，为转换步骤分配足够的内存
- 性能测试结果可在`tests/performance/results`目录下查看

## 错误处理

转换工具会捕获并报告以下类型的错误：

- 文件不存在或无法访问
- JSON格式错误
- 不支持的Python语法
- 无效的测试步骤

## 扩展性

可以通过继承`TestCaseConverter`类并重写相关方法来自定义转换行为。

## 贡献指南

1. 在提交代码前运行所有测试
2. 添加新功能时请同时添加相应的测试用例
3. 更新文档以反映API变更

## 许可证

本项目采用MIT许可证。

## 更新日志

### [1.0.0] - 2025-05-30
- 初始版本发布
- 支持基本转换功能
- 添加单元测试和集成测试
- 添加性能测试和优化
