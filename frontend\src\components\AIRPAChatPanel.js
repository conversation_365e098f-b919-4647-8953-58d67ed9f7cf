import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  List,
  ListItem,
  ListItemText,
  Avatar,
  Chip,
  IconButton,
  Divider,
  CircularProgress,
  Alert,
  Tooltip,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Send as SendIcon,
  SmartToy as AIIcon,
  Person as UserIcon,
  Settings as SettingsIcon,
  Clear as ClearIcon,
  Download as DownloadIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

const AIRPAChatPanel = ({ 
  onSendMessage, 
  onExecuteWorkflow, 
  onStopExecution, 
  isExecuting = false,
  workflows = [],
  onLoadWorkflow 
}) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: '🎉 欢迎使用 AI+RPA 智能助手！\n\n我可以帮助您：\n• 🎯 智能导航和页面操作\n• 🔍 分析页面结构\n• 📝 生成和执行工作流\n• 🤖 AI协助处理命令\n\n请输入您的需求开始对话！',
      timestamp: new Date(),
      confidence: null,
      commandType: null
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [settingsAnchor, setSettingsAnchor] = useState(null);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // 调用后端API处理消息
      const response = await onSendMessage(userMessage.content);
      
      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: response.message || response.response || '处理完成',
        timestamp: new Date(),
        confidence: response.confidence,
        commandType: response.commandType,
        success: response.success,
        metadata: response.metadata
      };

      setMessages(prev => [...prev, assistantMessage]);

      // 如果是工作流执行命令，自动执行
      if (response.shouldExecute && response.workflow) {
        setTimeout(() => {
          onExecuteWorkflow(response.workflow);
        }, 1000);
      }

    } catch (error) {
      const errorMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: `❌ 处理失败: ${error.message}`,
        timestamp: new Date(),
        success: false
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理回车发送
  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  // 清空对话
  const handleClearChat = () => {
    setMessages([{
      id: 1,
      type: 'assistant',
      content: '对话已清空，请输入新的需求。',
      timestamp: new Date()
    }]);
  };

  // 导出对话记录
  const handleExportChat = () => {
    const chatData = {
      timestamp: new Date().toISOString(),
      messages: messages.map(msg => ({
        type: msg.type,
        content: msg.content,
        timestamp: msg.timestamp.toISOString(),
        confidence: msg.confidence,
        commandType: msg.commandType
      }))
    };

    const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-rpa-chat-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // 快捷命令
  const quickCommands = [
    { label: '分析页面', command: '分析当前页面' },
    { label: '打开百度', command: '打开百度' },
    { label: '搜索天气', command: '搜索今天天气' },
    { label: '系统状态', command: '系统状态' },
    { label: '帮助', command: '帮助' }
  ];

  const handleQuickCommand = (command) => {
    setInputValue(command);
    inputRef.current?.focus();
  };

  // 渲染消息
  const renderMessage = (message) => {
    const isUser = message.type === 'user';
    const isSystem = message.type === 'system';
    
    return (
      <ListItem
        key={message.id}
        sx={{
          flexDirection: 'column',
          alignItems: isUser ? 'flex-end' : 'flex-start',
          py: 1
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-start',
            gap: 1,
            maxWidth: '80%',
            flexDirection: isUser ? 'row-reverse' : 'row'
          }}
        >
          <Avatar
            sx={{
              width: 32,
              height: 32,
              bgcolor: isUser ? 'primary.main' : isSystem ? 'warning.main' : 'secondary.main'
            }}
          >
            {isUser ? <UserIcon /> : <AIIcon />}
          </Avatar>
          
          <Paper
            elevation={1}
            sx={{
              p: 2,
              bgcolor: isUser ? 'primary.light' : isSystem ? 'warning.light' : 'grey.100',
              color: isUser ? 'primary.contrastText' : 'text.primary',
              borderRadius: 2,
              maxWidth: '100%'
            }}
          >
            <Typography
              variant="body2"
              sx={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}
            >
              {message.content}
            </Typography>
            
            {/* 显示命令识别信息 */}
            {message.confidence !== null && (
              <Box sx={{ mt: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  size="small"
                  label={`置信度: ${(message.confidence * 100).toFixed(0)}%`}
                  color={message.confidence > 0.8 ? 'success' : message.confidence > 0.6 ? 'warning' : 'error'}
                />
                {message.commandType && (
                  <Chip
                    size="small"
                    label={message.commandType}
                    variant="outlined"
                  />
                )}
                {message.success !== undefined && (
                  <Chip
                    size="small"
                    label={message.success ? '成功' : '失败'}
                    color={message.success ? 'success' : 'error'}
                  />
                )}
              </Box>
            )}
            
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                mt: 1,
                opacity: 0.7,
                textAlign: isUser ? 'right' : 'left'
              }}
            >
              {message.timestamp.toLocaleTimeString()}
            </Typography>
          </Paper>
        </Box>
      </ListItem>
    );
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部工具栏 */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AIIcon color="primary" />
          AI+RPA 智能助手
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="刷新页面分析">
            <IconButton size="small" onClick={() => handleQuickCommand('分析当前页面')}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="导出对话记录">
            <IconButton size="small" onClick={handleExportChat}>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="清空对话">
            <IconButton size="small" onClick={handleClearChat}>
              <ClearIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="设置">
            <IconButton 
              size="small" 
              onClick={(e) => setSettingsAnchor(e.currentTarget)}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* 执行状态提示 */}
      {isExecuting && (
        <Alert 
          severity="info" 
          sx={{ m: 1 }}
          action={
            <Button size="small" onClick={onStopExecution} startIcon={<StopIcon />}>
              停止
            </Button>
          }
        >
          工作流正在执行中...
        </Alert>
      )}

      {/* 快捷命令 */}
      <Box sx={{ p: 1, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
          快捷命令：
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {quickCommands.map((cmd, index) => (
            <Chip
              key={index}
              label={cmd.label}
              size="small"
              onClick={() => handleQuickCommand(cmd.command)}
              sx={{ cursor: 'pointer' }}
            />
          ))}
        </Box>
      </Box>

      {/* 消息列表 */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List sx={{ p: 0 }}>
          {messages.map(renderMessage)}
          {isLoading && (
            <ListItem sx={{ justifyContent: 'center' }}>
              <CircularProgress size={24} />
              <Typography variant="body2" sx={{ ml: 1 }}>
                AI正在思考...
              </Typography>
            </ListItem>
          )}
        </List>
        <div ref={messagesEndRef} />
      </Box>

      {/* 输入区域 */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
          <TextField
            ref={inputRef}
            fullWidth
            multiline
            maxRows={4}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入您的需求或命令..."
            disabled={isLoading}
            variant="outlined"
            size="small"
          />
          <Button
            variant="contained"
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            sx={{ minWidth: 'auto', px: 2 }}
          >
            <SendIcon />
          </Button>
        </Box>
        
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          按 Enter 发送，Shift+Enter 换行
        </Typography>
      </Box>

      {/* 设置菜单 */}
      <Menu
        anchorEl={settingsAnchor}
        open={Boolean(settingsAnchor)}
        onClose={() => setSettingsAnchor(null)}
      >
        <MenuItem onClick={() => {
          setSettingsAnchor(null);
          // 打开设置对话框
        }}>
          AI模型设置
        </MenuItem>
        <MenuItem onClick={() => {
          setSettingsAnchor(null);
          // 打开浏览器设置
        }}>
          浏览器设置
        </MenuItem>
        <MenuItem onClick={() => {
          setSettingsAnchor(null);
          handleExportChat();
        }}>
          导出对话记录
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default AIRPAChatPanel;
