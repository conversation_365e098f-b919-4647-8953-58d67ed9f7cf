<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dialog Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f8f8;
            transition: all 0.3s;
        }
        button:hover {
            background-color: #e8e8e8;
        }
        #alertBtn {
            background-color: #f0ad4e;
            color: white;
            border-color: #eea236;
        }
        #confirmBtn {
            background-color: #5bc0de;
            color: white;
            border-color: #46b8da;
        }
        #promptBtn {
            background-color: #5cb85c;
            color: white;
            border-color: #4cae4c;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 50px;
            background-color: #f9f9f9;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background-color: #e9e9e9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Dialog Test Page</h1>
        
        <div class="button-group">
            <button id="alertBtn">Show Alert</button>
            <button id="confirmBtn">Show Confirm</button>
            <button id="promptBtn">Show Prompt</button>
            <button id="customDialogBtn">Show Custom Dialog</button>
        </div>
        
        <div>
            <h3>Test Results:</h3>
            <div id="result">No interactions yet.</div>
        </div>
        
        <div class="status" id="pageStatus">
            Page loaded successfully at: <span id="loadTime"></span>
        </div>
    </div>

    <script>
        // 显示页面加载时间
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            document.getElementById('loadTime').textContent = now.toLocaleTimeString();
            console.log('Page fully loaded at:', now);
            
            // 初始化按钮事件
            setupEventListeners();
        });
        
        function setupEventListeners() {
            // Alert 按钮
            document.getElementById('alertBtn').addEventListener('click', function() {
                console.log('Alert button clicked');
                alert('This is an alert message!');
                updateResult('Alert was shown and closed');
            });
            
            // Confirm 按钮
            document.getElementById('confirmBtn').addEventListener('click', function() {
                console.log('Confirm button clicked');
                const confirmed = confirm('Are you sure you want to proceed?');
                updateResult(`Confirm dialog result: ${confirmed ? 'OK' : 'Cancel'}`);
            });
            
            // Prompt 按钮
            document.getElementById('promptBtn').addEventListener('click', function() {
                console.log('Prompt button clicked');
                const userInput = prompt('Please enter your name:', 'Guest');
                if (userInput === null) {
                    updateResult('Prompt was cancelled');
                } else {
                    updateResult(`You entered: ${userInput}`);
                }
            });
            
            // 自定义对话框按钮
            document.getElementById('customDialogBtn').addEventListener('click', function() {
                console.log('Custom dialog button clicked');
                const dialog = document.createElement('div');
                dialog.style.position = 'fixed';
                dialog.style.top = '50%';
                dialog.style.left = '50%';
                dialog.style.transform = 'translate(-50%, -50%)';
                dialog.style.padding = '20px';
                dialog.style.backgroundColor = 'white';
                dialog.style.border = '1px solid #ccc';
                dialog.style.borderRadius = '5px';
                dialog.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                dialog.style.zIndex = '1000';
                
                dialog.innerHTML = `
                    <h3>Custom Dialog</h3>
                    <p>This is a custom dialog created with JavaScript.</p>
                    <div style="margin-top: 15px; text-align: right;">
                        <button id="customOkBtn" style="margin-right: 10px;">OK</button>
                        <button id="customCancelBtn">Cancel</button>
                    </div>
                `;
                
                document.body.appendChild(dialog);
                
                // 添加按钮事件
                document.getElementById('customOkBtn').addEventListener('click', function() {
                    updateResult('Custom dialog: OK clicked');
                    document.body.removeChild(dialog);
                });
                
                document.getElementById('customCancelBtn').addEventListener('click', function() {
                    updateResult('Custom dialog: Cancel clicked');
                    document.body.removeChild(dialog);
                });
            });
        }
        
        function updateResult(message) {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.innerHTML += `<div><strong>[${timestamp}]</strong> ${message}</div>`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
            console.log('Result updated:', message);
        }
    </script>
</body>
</html>
