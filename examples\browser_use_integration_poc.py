"""
browser-use集成概念验证 (PoC)

展示如何将browser-use与我们的AI智能交互系统集成
"""
import asyncio
import os
import sys
from typing import Dict, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入我们的AI智能交互模块
from src.ai_intelligent_interaction import get_interaction_manager, InteractionMode

# 注意：这里需要先安装browser-use
# pip install browser-use
# pip install "browser-use[memory]"

try:
    from browser_use import Agent
    from langchain_openai import ChatOpenAI
    BROWSER_USE_AVAILABLE = True
except ImportError:
    print("⚠️  browser-use未安装，将使用模拟实现")
    BROWSER_USE_AVAILABLE = False


class EnhancedBrowserAgent:
    """
    增强版浏览器代理
    
    集成browser-use和我们的AI智能交互系统
    """
    
    def __init__(self, llm_model: str = "gpt-4o"):
        """初始化增强版浏览器代理"""
        self.llm_model = llm_model
        self.interaction_manager = get_interaction_manager()
        
        # 初始化browser-use代理（如果可用）
        if BROWSER_USE_AVAILABLE:
            self.browser_agent = None  # 延迟初始化
        else:
            self.browser_agent = None
        
        # 执行历史
        self.execution_history = []
    
    def _init_browser_agent(self, task: str) -> Optional[Any]:
        """延迟初始化browser-use代理"""
        if not BROWSER_USE_AVAILABLE:
            return None
        
        try:
            # 检查是否有OpenAI API密钥
            if not os.getenv('OPENAI_API_KEY'):
                print("⚠️  未设置OPENAI_API_KEY环境变量")
                return None
            
            llm = ChatOpenAI(model=self.llm_model)
            agent = Agent(
                task=task,
                llm=llm,
                # 可以添加更多配置
                # use_vision=True,
                # save_conversation_path="./conversation_history.json"
            )
            return agent
        except Exception as e:
            print(f"❌ 初始化browser-use代理失败: {e}")
            return None
    
    async def execute_user_request(self, user_input: str) -> Dict[str, Any]:
        """
        执行用户请求的完整流程
        
        Args:
            user_input: 用户输入的需求描述
            
        Returns:
            执行结果
        """
        print(f"\n🎯 处理用户请求: {user_input}")
        
        try:
            # 阶段1: AI需求分析
            print("\n📋 阶段1: AI需求分析")
            session = self.interaction_manager.start_requirement_analysis_session(user_input)
            
            print(f"   解析意图: {session.user_requirement.parsed_intent}")
            print(f"   业务领域: {session.user_requirement.business_domain}")
            print(f"   置信度: {session.user_requirement.confidence:.2f}")
            
            # 阶段2: 参数收集
            print("\n📝 阶段2: 参数收集")
            if session.workflow_matches:
                best_match = session.workflow_matches[0]
                print(f"   最佳匹配工作流: {best_match.workflow_name}")
                print(f"   所需参数: {list(best_match.required_parameters.keys())}")
                
                # 模拟参数收集（实际应该通过用户交互）
                parameters = await self._collect_parameters(session)
                print(f"   收集到的参数: {list(parameters.keys())}")
            else:
                print("   ❌ 未找到匹配的工作流")
                return {"success": False, "error": "未找到匹配的工作流"}
            
            # 阶段3: 生成browser-use任务描述
            print("\n🤖 阶段3: 生成任务描述")
            task_description = self._generate_task_description(session, parameters)
            print(f"   任务描述: {task_description}")
            
            # 阶段4: 执行任务
            print("\n⚡ 阶段4: 执行任务")
            if BROWSER_USE_AVAILABLE:
                result = await self._execute_with_browser_use(task_description)
            else:
                result = await self._execute_with_simulation(task_description)
            
            # 阶段5: 结果处理
            print("\n📊 阶段5: 结果处理")
            final_result = self._process_result(result, session, parameters)
            
            # 记录执行历史
            self.execution_history.append({
                "timestamp": datetime.now().isoformat(),
                "user_input": user_input,
                "session_id": session.session_id,
                "task_description": task_description,
                "result": final_result
            })
            
            return final_result
            
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _collect_parameters(self, session) -> Dict[str, Any]:
        """收集执行所需的参数"""
        parameters = {}
        
        if not session.workflow_matches:
            return parameters
        
        best_match = session.workflow_matches[0]
        
        # 模拟参数收集（实际应该通过用户交互界面）
        for param_name, param_info in best_match.required_parameters.items():
            if param_name == "customer_name":
                parameters[param_name] = "测试客户公司"
            elif param_name == "contact_person":
                parameters[param_name] = "张三"
            elif param_name == "phone":
                parameters[param_name] = "13800138000"
            elif param_name == "email":
                parameters[param_name] = "<EMAIL>"
            elif param_name == "username":
                parameters[param_name] = "testuser"
            elif param_name == "password":
                parameters[param_name] = "testpass123"
            elif param_name == "report_type":
                parameters[param_name] = "月度财务报表"
            elif param_name == "date_range":
                parameters[param_name] = "2025年12月"
            elif param_name == "department":
                parameters[param_name] = "财务部"
            else:
                parameters[param_name] = f"默认值_{param_name}"
        
        # 更新会话参数
        self.interaction_manager.provide_user_parameters(session.session_id, parameters)
        
        return parameters
    
    def _generate_task_description(self, session, parameters: Dict[str, Any]) -> str:
        """生成browser-use可理解的任务描述"""
        user_requirement = session.user_requirement
        intent = user_requirement.parsed_intent
        
        # 基于意图和参数生成任务描述
        if intent == "create" and "customer" in user_requirement.original_text.lower():
            return f"""
            在CRM系统中创建一个新客户：
            - 客户名称: {parameters.get('customer_name', '未指定')}
            - 联系人: {parameters.get('contact_person', '未指定')}
            - 电话: {parameters.get('phone', '未指定')}
            - 邮箱: {parameters.get('email', '未指定')}
            
            请导航到CRM系统，找到创建客户的页面，填写上述信息并保存。
            """
        
        elif intent == "generate" and "报表" in user_requirement.original_text:
            return f"""
            生成财务报表：
            - 报表类型: {parameters.get('report_type', '未指定')}
            - 时间范围: {parameters.get('date_range', '未指定')}
            - 部门: {parameters.get('department', '未指定')}
            
            请导航到财务系统，找到报表生成功能，设置上述参数并生成报表。
            """
        
        elif intent == "login":
            return f"""
            登录到系统：
            - 用户名: {parameters.get('username', '未指定')}
            - 密码: {parameters.get('password', '未指定')}
            
            请导航到登录页面，输入用户名和密码，然后点击登录按钮。
            """
        
        else:
            # 通用任务描述
            return f"""
            执行用户请求: {user_requirement.original_text}
            
            参数信息:
            {chr(10).join([f'- {k}: {v}' for k, v in parameters.items()])}
            
            请根据用户需求和提供的参数，在浏览器中执行相应的操作。
            """
    
    async def _execute_with_browser_use(self, task_description: str) -> Dict[str, Any]:
        """使用browser-use执行任务"""
        try:
            # 初始化browser-use代理
            agent = self._init_browser_agent(task_description)
            if not agent:
                return {"success": False, "error": "无法初始化browser-use代理"}
            
            print("   🚀 启动browser-use代理...")
            
            # 执行任务
            result = await agent.run()
            
            return {
                "success": True,
                "method": "browser-use",
                "result": result,
                "message": "任务执行完成"
            }
            
        except Exception as e:
            print(f"   ❌ browser-use执行失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _execute_with_simulation(self, task_description: str) -> Dict[str, Any]:
        """模拟执行任务（当browser-use不可用时）"""
        print("   🎭 使用模拟执行...")
        
        # 模拟执行时间
        await asyncio.sleep(2)
        
        return {
            "success": True,
            "method": "simulation",
            "result": "模拟执行成功",
            "task_description": task_description,
            "message": "任务模拟执行完成"
        }
    
    def _process_result(self, result: Dict[str, Any], session, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理执行结果"""
        final_result = {
            "success": result.get("success", False),
            "execution_method": result.get("method", "unknown"),
            "user_requirement": session.user_requirement.original_text,
            "parsed_intent": session.user_requirement.parsed_intent,
            "business_domain": session.user_requirement.business_domain,
            "parameters_used": parameters,
            "execution_result": result.get("result"),
            "message": result.get("message", ""),
            "timestamp": datetime.now().isoformat()
        }
        
        if result.get("success"):
            print(f"   ✅ 执行成功: {result.get('message', '')}")
        else:
            print(f"   ❌ 执行失败: {result.get('error', '')}")
            final_result["error"] = result.get("error")
        
        return final_result
    
    def get_execution_history(self) -> list:
        """获取执行历史"""
        return self.execution_history


async def main():
    """主函数 - 演示browser-use集成"""
    print("🎭 browser-use集成概念验证 (PoC)")
    print("展示AI智能交互 + browser-use的完整流程")
    
    # 检查环境
    if not BROWSER_USE_AVAILABLE:
        print("\n⚠️  注意: browser-use未安装，将使用模拟实现")
        print("要安装browser-use，请运行:")
        print("pip install browser-use")
        print("pip install \"browser-use[memory]\"")
    
    if not os.getenv('OPENAI_API_KEY'):
        print("\n⚠️  注意: 未设置OPENAI_API_KEY环境变量")
        print("请设置环境变量或在.env文件中添加OPENAI_API_KEY")
    
    # 创建增强版浏览器代理
    agent = EnhancedBrowserAgent()
    
    # 测试用例
    test_cases = [
        "我需要在CRM系统中创建一个新客户",
        "帮我生成本月的财务报表", 
        "请帮我登录到系统",
        "查询最近的订单信息"
    ]
    
    print(f"\n🧪 开始测试 {len(test_cases)} 个用例:")
    
    for i, user_input in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"测试用例 {i}/{len(test_cases)}")
        print(f"{'='*60}")
        
        try:
            result = await agent.execute_user_request(user_input)
            
            print(f"\n📋 执行总结:")
            print(f"   成功: {'✅' if result['success'] else '❌'}")
            print(f"   方法: {result.get('execution_method', 'unknown')}")
            print(f"   意图: {result.get('parsed_intent', 'unknown')}")
            print(f"   领域: {result.get('business_domain', 'unknown')}")
            
            if not result['success']:
                print(f"   错误: {result.get('error', '未知错误')}")
            
        except Exception as e:
            print(f"❌ 测试用例执行失败: {e}")
    
    # 显示执行历史
    history = agent.get_execution_history()
    print(f"\n📊 执行历史总结:")
    print(f"   总执行次数: {len(history)}")
    
    successful = sum(1 for h in history if h['result']['success'])
    print(f"   成功次数: {successful}")
    print(f"   成功率: {successful/len(history)*100:.1f}%" if history else "0%")
    
    print(f"\n🎉 browser-use集成PoC演示完成!")
    print(f"\n💡 下一步:")
    print(f"   1. 安装browser-use和相关依赖")
    print(f"   2. 配置OpenAI API密钥")
    print(f"   3. 开发真实的用户交互界面")
    print(f"   4. 集成browser-tools-mcp监控功能")


if __name__ == "__main__":
    asyncio.run(main())
