"""
OpenCV验证码处理测试

测试OpenCV+AI验证码检测、图像预处理和OCR识别功能
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"✅ 加载环境变量: {key}")


def test_opencv_installation():
    """测试OpenCV安装"""
    print("🧪 测试OpenCV安装")
    print("-" * 40)
    
    try:
        import cv2
        print(f"   ✅ OpenCV版本: {cv2.__version__}")
        
        import numpy as np
        print(f"   ✅ NumPy版本: {np.__version__}")
        
        # 测试基本功能
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        print(f"   ✅ OpenCV基本功能正常")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ OpenCV未安装: {e}")
        print(f"   💡 请运行: pip install opencv-python")
        return False
    except Exception as e:
        print(f"   ❌ OpenCV测试失败: {e}")
        return False


def test_imports():
    """测试导入"""
    print("\n🧪 测试OpenCV OCR处理器导入")
    print("-" * 40)
    
    try:
        from opencv_ocr_processor import (
            OpenCVImageProcessor,
            OpenCVOCREngine,
            OpenCVOCRProvider,
            OCRResult,
            opencv_ocr_recognize,
            get_opencv_ocr_provider
        )
        print("   ✅ opencv_ocr_processor 导入成功")
        
        from ai_captcha_handler import get_ai_captcha_handler
        print("   ✅ ai_captcha_handler 导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        return False


async def test_image_processor():
    """测试图像预处理器"""
    print("\n🧪 测试OpenCV图像预处理器")
    print("-" * 40)
    
    try:
        from opencv_ocr_processor import OpenCVImageProcessor
        import cv2
        import numpy as np
        
        processor = OpenCVImageProcessor()
        print("   ✅ 图像预处理器创建成功")
        
        # 创建测试图像
        test_image = np.ones((50, 150, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "TEST123", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        
        test_path = "test_captcha.png"
        cv2.imwrite(test_path, test_image)
        print("   ✅ 测试图像创建成功")
        
        # 测试预处理
        processed_path = processor.preprocess_captcha_image(test_path)
        print(f"   ✅ 图像预处理成功: {processed_path}")
        
        # 测试文本区域提取
        regions = processor.extract_text_regions(processed_path)
        print(f"   ✅ 文本区域提取: 发现 {len(regions)} 个区域")
        
        # 清理测试文件
        try:
            Path(test_path).unlink()
            if processed_path != test_path:
                Path(processed_path).unlink()
        except Exception:
            pass
        
        return True
        
    except Exception as e:
        print(f"   ❌ 图像预处理器测试失败: {e}")
        return False


async def test_ocr_engine():
    """测试OCR引擎"""
    print("\n🧪 测试OpenCV OCR引擎")
    print("-" * 40)
    
    try:
        from opencv_ocr_processor import OpenCVOCREngine
        import cv2
        import numpy as np
        
        engine = OpenCVOCREngine()
        print(f"   ✅ OCR引擎创建成功")
        print(f"   ✅ 使用OCR引擎: {engine.ocr_engine}")
        
        # 创建更清晰的测试图像
        test_image = np.ones((80, 200, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "ABC123", (20, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 3)
        
        test_path = "test_ocr.png"
        cv2.imwrite(test_path, test_image)
        print("   ✅ 测试图像创建成功")
        
        # 测试OCR识别
        result = await engine.recognize_text(test_path)
        print(f"   ✅ OCR识别完成")
        print(f"      识别文本: '{result.text}'")
        print(f"      置信度: {result.confidence:.2f}")
        print(f"      处理时间: {result.processing_time:.2f}秒")
        
        # 清理测试文件
        try:
            Path(test_path).unlink()
            if result.preprocessed_image_path and Path(result.preprocessed_image_path).exists():
                Path(result.preprocessed_image_path).unlink()
        except Exception:
            pass
        
        return True
        
    except Exception as e:
        print(f"   ❌ OCR引擎测试失败: {e}")
        return False


async def test_ocr_provider():
    """测试OCR提供商"""
    print("\n🧪 测试OpenCV OCR提供商")
    print("-" * 40)
    
    try:
        from opencv_ocr_processor import get_opencv_ocr_provider
        import cv2
        import numpy as np
        
        provider = get_opencv_ocr_provider()
        print("   ✅ OCR提供商创建成功")
        print(f"   ✅ 提供商可用: {provider.is_available()}")
        
        if provider.is_available():
            # 创建测试图像
            test_image = np.ones((60, 180, 3), dtype=np.uint8) * 255
            cv2.putText(test_image, "XYZ789", (15, 40), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 2)
            
            # 转换为字节数据
            import cv2
            _, buffer = cv2.imencode('.png', test_image)
            image_data = buffer.tobytes()
            
            # 测试分析
            results = await provider.analyze_image(image_data)
            print(f"   ✅ 图像分析完成: {len(results)} 个结果")
            
            for i, result in enumerate(results, 1):
                print(f"      结果{i}: '{result.text}' (置信度: {result.confidence:.2f})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ OCR提供商测试失败: {e}")
        return False


async def test_captcha_integration():
    """测试验证码处理集成"""
    print("\n🧪 测试验证码处理集成")
    print("-" * 40)
    
    try:
        from ai_captcha_handler import get_ai_captcha_handler
        
        handler = get_ai_captcha_handler()
        print("   ✅ 验证码处理器创建成功")
        
        # 检查OCR提供商
        available_providers = handler.ocr_processor.ocr_analyzer.get_available_providers()
        print(f"   ✅ 可用OCR提供商: {available_providers}")
        
        if 'opencv' in available_providers:
            print("   ✅ OpenCV OCR提供商已集成")
        else:
            print("   ⚠️ OpenCV OCR提供商未集成")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 验证码处理集成测试失败: {e}")
        return False


async def test_real_captcha_with_opencv():
    """测试真实验证码处理（使用OpenCV）"""
    print("\n🧪 测试真实验证码处理（OpenCV）")
    print("-" * 40)
    
    try:
        from playwright.async_api import async_playwright
        from ai_captcha_handler import get_ai_captcha_handler
        
        print("   🌐 启动浏览器进行真实测试...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            
            try:
                # 访问登录页面
                print("   📱 访问登录页面...")
                await page.goto("https://test.yushanyun.net/ac/web/")
                await page.wait_for_load_state('networkidle')
                
                # 使用AI+OpenCV处理验证码
                print("   🤖 使用AI+OpenCV处理验证码...")
                handler = get_ai_captcha_handler()
                
                # 强制使用OpenCV OCR
                original_providers = handler.ocr_processor.ocr_analyzer.providers.copy()
                handler.ocr_processor.ocr_analyzer.providers = {
                    'opencv': handler.ocr_processor.ocr_analyzer.providers.get('opencv')
                }
                
                result = await handler.handle_captcha(page)
                
                # 恢复原始提供商
                handler.ocr_processor.ocr_analyzer.providers = original_providers
                
                print(f"   📊 OpenCV验证码处理结果:")
                print(f"      处理成功: {'✅' if result.success else '❌'}")
                print(f"      识别文本: '{result.recognized_text}'")
                print(f"      识别置信度: {result.confidence:.2f}")
                print(f"      处理时间: {result.processing_time:.2f}秒")
                
                if result.success:
                    print(f"      图片路径: {result.image_path}")
                    print(f"      输入框选择器: {result.input_selector}")
                    print(f"   🎉 OpenCV验证码处理成功！")
                else:
                    print(f"      错误信息: {result.error_message}")
                
                # 保持浏览器打开以便查看
                input("\n   👀 请查看浏览器中的结果，按回车关闭...")
                
            finally:
                await browser.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 真实验证码测试失败: {e}")
        return False


def show_opencv_usage():
    """显示OpenCV OCR使用示例"""
    print("\n💡 OpenCV验证码处理使用示例")
    print("=" * 50)
    
    print("\n1. 基础OpenCV OCR:")
    print("""
from opencv_ocr_processor import opencv_ocr_recognize

# 直接使用OpenCV OCR识别
result = await opencv_ocr_recognize("captcha.png")

print(f"识别结果: {result.text}")
print(f"置信度: {result.confidence:.2f}")
print(f"预处理图片: {result.preprocessed_image_path}")
""")
    
    print("\n2. 集成到验证码处理:")
    print("""
from ai_captcha_handler import get_ai_captcha_handler

# AI验证码处理器会自动使用OpenCV OCR
handler = get_ai_captcha_handler()
result = await handler.handle_captcha(page)

# OpenCV会自动进行图像预处理和OCR识别
if result.success:
    print(f"验证码已自动填写: {result.recognized_text}")
""")
    
    print("\n3. 自定义图像预处理:")
    print("""
from opencv_ocr_processor import OpenCVImageProcessor

processor = OpenCVImageProcessor()

# 预处理验证码图像
processed_path = processor.preprocess_captcha_image("captcha.png")

# 提取文本区域
regions = processor.extract_text_regions(processed_path)
print(f"发现 {len(regions)} 个文本区域")
""")


async def main():
    """主函数"""
    print("🎭 OpenCV+AI验证码处理系统测试")
    print("验证OpenCV图像预处理和OCR识别功能")
    print("=" * 60)
    
    # 加载环境变量
    load_env()
    
    # 检查AI配置
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        print("❌ 缺少GEMINI_API_KEY配置")
        return
    
    print("✅ AI配置检查通过")
    
    # 运行测试
    tests = [
        ("OpenCV安装", test_opencv_installation),
        ("模块导入", test_imports),
        ("图像预处理器", test_image_processor),
        ("OCR引擎", test_ocr_engine),
        ("OCR提供商", test_ocr_provider),
        ("验证码处理集成", test_captcha_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                results[test_name] = await test_func()
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 OpenCV验证码处理系统测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if results.get("OpenCV安装", False):
        print(f"\n🚀 OpenCV功能:")
        print(f"   ✅ 智能图像预处理")
        print(f"   ✅ 自适应二值化")
        print(f"   ✅ 噪点去除和增强")
        print(f"   ✅ 文本区域提取")
        print(f"   ✅ 多种OCR识别方法")
        
        if passed_tests >= total_tests * 0.8:
            # 询问是否进行真实测试
            real_test = input(f"\n🧪 是否进行真实验证码处理测试? (y/n): ").strip().lower()
            if real_test in ['y', 'yes']:
                await test_real_captcha_with_opencv()
        
        # 显示使用示例
        show_opencv_usage()
        
        print(f"\n🎯 OpenCV优势:")
        print(f"   • 无需额外OCR引擎安装")
        print(f"   • 强大的图像预处理能力")
        print(f"   • 可定制的识别流程")
        print(f"   • 与AI检测完美集成")
        
    else:
        print(f"\n❌ OpenCV未正确安装")
        print(f"💡 请运行: pip install opencv-python")


if __name__ == "__main__":
    asyncio.run(main())
