"""
AI+RPA 后端API服务

提供前端与Python AI+RPA引擎之间的通信接口
"""
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import json
import logging
import os
import sys
from pathlib import Path
from datetime import datetime
import uuid

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))

# 导入AI+RPA组件
try:
    from interactive_mode_manager import get_interactive_mode_manager
    from intelligent_command_processor import get_intelligent_command_processor
    from ai_login_workflow_generator import get_login_state_executor
    from complex_scenario_workflow import get_complex_scenario_workflow
except ImportError as e:
    print(f"警告: 无法导入AI+RPA组件: {e}")
    print("请确保src目录中的组件可用")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="AI+RPA API",
    description="AI+RPA智能自动化系统API接口",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # React开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class ChatMessage(BaseModel):
    content: str
    type: str = "user"

class ChatResponse(BaseModel):
    message: str
    success: bool
    confidence: Optional[float] = None
    commandType: Optional[str] = None
    shouldExecute: bool = False
    workflow: Optional[Dict] = None
    metadata: Optional[Dict] = None

class WorkflowData(BaseModel):
    name: str
    description: Optional[str] = ""
    domain: Optional[str] = ""
    nodes: List[Dict] = []
    edges: List[Dict] = []
    tags: List[str] = []

class WorkflowExecution(BaseModel):
    workflowId: str
    parameters: Optional[Dict] = {}

# 全局状态管理
class AppState:
    def __init__(self):
        self.interactive_manager = None
        self.command_processor = None
        self.scenario_workflow = None
        self.login_executor = None
        self.connected_clients = set()
        self.workflows = {}
        self.execution_status = {}
        
    async def initialize(self):
        """初始化AI+RPA组件"""
        try:
            self.command_processor = get_intelligent_command_processor()
            self.scenario_workflow = get_complex_scenario_workflow()
            self.login_executor = get_login_state_executor()
            logger.info("AI+RPA组件初始化成功")
        except Exception as e:
            logger.error(f"AI+RPA组件初始化失败: {e}")
    
    async def broadcast_message(self, message: Dict):
        """广播消息到所有连接的客户端"""
        if self.connected_clients:
            disconnected = set()
            for websocket in self.connected_clients:
                try:
                    await websocket.send_text(json.dumps(message))
                except:
                    disconnected.add(websocket)
            
            # 清理断开的连接
            self.connected_clients -= disconnected

# 全局应用状态
app_state = AppState()

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    await app_state.initialize()
    logger.info("AI+RPA API服务启动成功")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("AI+RPA API服务关闭")

# API路由

@app.get("/")
async def root():
    """根路径"""
    return {"message": "AI+RPA API服务运行中", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "command_processor": app_state.command_processor is not None,
            "scenario_workflow": app_state.scenario_workflow is not None,
            "login_executor": app_state.login_executor is not None
        }
    }

@app.post("/api/chat/message", response_model=ChatResponse)
async def process_chat_message(message: ChatMessage):
    """处理聊天消息"""
    try:
        if not app_state.command_processor:
            raise HTTPException(status_code=500, detail="命令处理器未初始化")
        
        # 使用智能命令处理器分析消息
        result = await app_state.command_processor.process_command(message.content)
        
        # 检查是否需要执行工作流
        should_execute = False
        workflow = None
        
        if result.success and result.command_type.value in ["navigation", "query"]:
            # 检查是否有匹配的工作流
            # 这里可以添加工作流匹配逻辑
            pass
        
        response = ChatResponse(
            message=result.response,
            success=result.success,
            confidence=result.confidence,
            commandType=result.command_type.value,
            shouldExecute=should_execute,
            workflow=workflow,
            metadata={
                "processing_method": result.processing_method,
                "parameters": result.parameters
            }
        )
        
        # 广播消息到WebSocket客户端
        await app_state.broadcast_message({
            "type": "chat_response",
            "data": response.dict()
        })
        
        return response
        
    except Exception as e:
        logger.error(f"处理聊天消息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/workflows")
async def get_workflows():
    """获取工作流列表"""
    try:
        # 从文件系统加载工作流
        workflows_dir = Path("smart_workflows")
        workflows = []
        
        if workflows_dir.exists():
            for workflow_file in workflows_dir.glob("*.json"):
                try:
                    with open(workflow_file, 'r', encoding='utf-8') as f:
                        workflow_data = json.load(f)
                    
                    workflows.append({
                        "id": workflow_file.stem,
                        "name": workflow_data.get("name", workflow_file.stem),
                        "description": workflow_data.get("description", ""),
                        "domain": workflow_data.get("domain", ""),
                        "created_at": workflow_data.get("created_at", ""),
                        "updated_at": workflow_data.get("updated_at", ""),
                        "tags": workflow_data.get("tags", []),
                        "steps": len(workflow_data.get("steps", [])),
                        "parameters": workflow_data.get("parameters", {})
                    })
                except Exception as e:
                    logger.error(f"加载工作流文件失败 {workflow_file}: {e}")
        
        return {"workflows": workflows}
        
    except Exception as e:
        logger.error(f"获取工作流列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/workflows")
async def create_workflow(workflow: WorkflowData):
    """创建新工作流"""
    try:
        workflow_id = str(uuid.uuid4())
        workflow_data = {
            "id": workflow_id,
            "name": workflow.name,
            "description": workflow.description,
            "domain": workflow.domain,
            "nodes": workflow.nodes,
            "edges": workflow.edges,
            "tags": workflow.tags,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # 保存到文件
        workflows_dir = Path("smart_workflows")
        workflows_dir.mkdir(exist_ok=True)
        
        workflow_file = workflows_dir / f"{workflow_id}.json"
        with open(workflow_file, 'w', encoding='utf-8') as f:
            json.dump(workflow_data, f, indent=2, ensure_ascii=False)
        
        # 广播更新
        await app_state.broadcast_message({
            "type": "workflow_created",
            "data": {"workflow_id": workflow_id, "workflow": workflow_data}
        })
        
        return {"success": True, "workflow_id": workflow_id, "workflow": workflow_data}
        
    except Exception as e:
        logger.error(f"创建工作流失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/api/workflows/{workflow_id}")
async def update_workflow(workflow_id: str, workflow: WorkflowData):
    """更新工作流"""
    try:
        workflows_dir = Path("smart_workflows")
        workflow_file = workflows_dir / f"{workflow_id}.json"
        
        if not workflow_file.exists():
            raise HTTPException(status_code=404, detail="工作流不存在")
        
        # 读取现有数据
        with open(workflow_file, 'r', encoding='utf-8') as f:
            existing_data = json.load(f)
        
        # 更新数据
        existing_data.update({
            "name": workflow.name,
            "description": workflow.description,
            "domain": workflow.domain,
            "nodes": workflow.nodes,
            "edges": workflow.edges,
            "tags": workflow.tags,
            "updated_at": datetime.now().isoformat()
        })
        
        # 保存更新
        with open(workflow_file, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, indent=2, ensure_ascii=False)
        
        # 广播更新
        await app_state.broadcast_message({
            "type": "workflow_updated",
            "data": {"workflow_id": workflow_id, "workflow": existing_data}
        })
        
        return {"success": True, "workflow": existing_data}
        
    except Exception as e:
        logger.error(f"更新工作流失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/workflows/{workflow_id}")
async def delete_workflow(workflow_id: str):
    """删除工作流"""
    try:
        workflows_dir = Path("smart_workflows")
        workflow_file = workflows_dir / f"{workflow_id}.json"
        
        if not workflow_file.exists():
            raise HTTPException(status_code=404, detail="工作流不存在")
        
        # 删除文件
        workflow_file.unlink()
        
        # 广播更新
        await app_state.broadcast_message({
            "type": "workflow_deleted",
            "data": {"workflow_id": workflow_id}
        })
        
        return {"success": True, "message": "工作流删除成功"}
        
    except Exception as e:
        logger.error(f"删除工作流失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/workflows/{workflow_id}/execute")
async def execute_workflow(workflow_id: str, execution: WorkflowExecution):
    """执行工作流"""
    try:
        workflows_dir = Path("smart_workflows")
        workflow_file = workflows_dir / f"{workflow_id}.json"
        
        if not workflow_file.exists():
            raise HTTPException(status_code=404, detail="工作流不存在")
        
        # 读取工作流数据
        with open(workflow_file, 'r', encoding='utf-8') as f:
            workflow_data = json.load(f)
        
        # 更新执行状态
        app_state.execution_status[workflow_id] = "running"
        
        # 广播执行开始
        await app_state.broadcast_message({
            "type": "workflow_execution_started",
            "data": {
                "workflow_id": workflow_id,
                "workflow_name": workflow_data.get("name", ""),
                "parameters": execution.parameters
            }
        })
        
        # 这里可以添加实际的工作流执行逻辑
        # 目前返回模拟结果
        
        # 模拟执行完成
        app_state.execution_status[workflow_id] = "completed"
        
        await app_state.broadcast_message({
            "type": "workflow_execution_completed",
            "data": {
                "workflow_id": workflow_id,
                "status": "completed",
                "result": "执行成功"
            }
        })
        
        return {
            "success": True,
            "message": "工作流执行成功",
            "execution_id": str(uuid.uuid4())
        }
        
    except Exception as e:
        logger.error(f"执行工作流失败: {e}")
        app_state.execution_status[workflow_id] = "error"
        
        await app_state.broadcast_message({
            "type": "workflow_execution_error",
            "data": {
                "workflow_id": workflow_id,
                "error": str(e)
            }
        })
        
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/login-sessions")
async def get_login_sessions():
    """获取登录会话列表"""
    try:
        if not app_state.login_executor:
            return {"sessions": []}
        
        sessions = await app_state.login_executor.list_available_sessions()
        return {"sessions": sessions}
        
    except Exception as e:
        logger.error(f"获取登录会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket连接
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    await websocket.accept()
    app_state.connected_clients.add(websocket)
    
    try:
        # 发送连接成功消息
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "data": {"message": "WebSocket连接成功"}
        }))
        
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理不同类型的消息
            if message.get("type") == "ping":
                await websocket.send_text(json.dumps({
                    "type": "pong",
                    "data": {"timestamp": datetime.now().isoformat()}
                }))
            
    except WebSocketDisconnect:
        app_state.connected_clients.discard(websocket)
        logger.info("WebSocket客户端断开连接")
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        app_state.connected_clients.discard(websocket)

# 静态文件服务（用于生产环境）
if os.path.exists("../frontend/build"):
    app.mount("/", StaticFiles(directory="../frontend/build", html=True), name="static")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
