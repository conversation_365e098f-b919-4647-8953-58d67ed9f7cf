# 开发者指南

## 目录

1. [项目结构](#项目结构)
2. [开发环境设置](#开发环境设置)
3. [代码规范](#代码规范)
4. [测试指南](#测试指南)
5. [提交代码](#提交代码)
6. [性能优化](#性能优化)
7. [API 参考](#api-参考)
8. [贡献指南](#贡献指南)

## 项目结构

```
playwright-automation/
├── src/                     # 源代码
│   ├── core/               # 核心功能
│   ├── utils/              # 工具函数
│   ├── workflow/           # 工作流引擎
│   └── __init__.py         
├── tests/                  # 测试代码
│   ├── unit/              # 单元测试
│   ├── integration/       # 集成测试
│   └── performance/       # 性能测试
├── docs/                   # 文档
│   ├── guides/            # 使用指南
│   └── api/               # API 文档
└── requirements.txt        # 项目依赖
```

## 开发环境设置

1. 克隆仓库
   ```bash
   git clone https://github.com/yourusername/playwright-automation.git
   cd playwright-automation
   ```

2. 创建并激活虚拟环境
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   .\venv\Scripts\activate  # Windows
   ```

3. 安装依赖
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   ```

4. 安装 Playwright 浏览器
   ```bash
   playwright install
   ```

## 代码规范

- 遵循 [PEP 8](https://www.python.org/dev/peps/pep-0008/) 代码风格
- 使用 [Google 风格](https://google.github.io/styleguide/pyguide.html) 的文档字符串
- 使用类型注解
- 行长度限制为 120 个字符
- 导入顺序：
  1. 标准库
  2. 第三方库
  3. 本地应用/库

## 测试指南

### 运行测试

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit

# 运行集成测试
pytest tests/integration

# 运行性能测试
pytest tests/performance

# 生成测试覆盖率报告
pytest --cov=src --cov-report=html
```

### 编写测试

- 单元测试应放在 `tests/unit` 目录下
- 集成测试应放在 `tests/integration` 目录下
- 性能测试应放在 `tests/performance` 目录下
- 测试文件名应以 `test_` 开头
- 测试函数/方法应以 `test_` 开头

## 提交代码

1. 创建特性分支
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. 提交更改
   ```bash
   git add .
   git commit -m "feat: add your feature"
   ```

3. 推送更改
   ```bash
   git push origin feature/your-feature-name
   ```

4. 创建合并请求
   - 访问仓库页面
   - 点击 "New Pull Request"
   - 填写描述和相关信息
   - 请求代码审查

## 性能优化

请参考[性能优化指南](./guides/performance_optimization.md)了解性能优化技术和最佳实践。

## API 参考

### 测试用例转换工具

请参考[测试用例转换工具指南](./guides/converter_guide.md)了解详细的使用说明和API参考。

## 贡献指南

1. Fork 仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开合并请求

## 许可证

本项目采用 MIT 许可证 - 详情请参阅 [LICENSE](LICENSE) 文件。
