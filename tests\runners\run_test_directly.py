"""
直接导入并运行测试模块
"""
import sys
import os
import unittest

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath('.'))

# 导入测试模块
import tests.test_operations

if __name__ == "__main__":
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromModule(tests.test_operations)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n测试结果:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败测试数: {len(result.failures)}")
    print(f"错误测试数: {len(result.errors)}")
    
    # 如果有测试失败或错误，返回非零退出码
    sys.exit(not result.wasSuccessful())
