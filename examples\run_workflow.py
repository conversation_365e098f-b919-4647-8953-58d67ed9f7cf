"""
工作流引擎使用示例
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.workflow.engine import Workflow<PERSON>arser, WorkflowExecutor
from src.operations.executor import OperationExecutor

async def main():
    """主函数"""
    print("Starting workflow engine example...")
    
    # 初始化操作执行器
    operation_executor = OperationExecutor()
    
    # 初始化工作流执行器
    workflow_executor = WorkflowExecutor(operation_executor)
    
    # 加载工作流定义
    workflow_file = os.path.join(os.path.dirname(__file__), "simple_workflow.yaml")
    print(f"Loading workflow from: {workflow_file}")
    
    try:
        # 解析工作流
        workflow_parser = WorkflowParser()
        workflow = workflow_parser.from_file(workflow_file)
        print(f"Workflow loaded: {workflow.name} (ID: {workflow.workflow_id})")
        
        # 执行工作流
        print("Starting workflow execution...")
        result = await workflow_executor.execute(workflow)
        
        # 输出执行结果
        print("\nWorkflow execution completed!")
        print(f"Status: {result.status}")
        print(f"Duration: {result.end_time - result.start_time:.2f} seconds")
        print(f"Nodes executed: {len(result.node_results)}")
        
        if result.error:
            print(f"Error: {result.error}")
        
        # 输出节点执行结果
        print("\nNode execution results:")
        for node_id, node_result in result.node_results.items():
            print(f"\nNode: {node_id} ({node_result['node_type']})")
            print(f"Status: {node_result['status']}")
            print(f"Duration: {node_result['duration']:.2f}s")
            if node_result.get('error'):
                print(f"Error: {node_result['error']}")
    
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    asyncio.run(main())
