"""
Standalone test script for TestCaseConverter that doesn't depend on test framework.
"""
import ast
import json
import os
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import the converter module directly
from utils.converter import TestCaseConverter

def run_tests():
    """Run all test cases and report results."""
    tests_passed = 0
    tests_failed = 0
    
    def test_extract_metadata():
        """Test extracting metadata from docstring"""
        nonlocal tests_passed, tests_failed
        try:
            assert TestCaseConverter._extract_metadata("""Test case""") == "Test case"
            assert TestCaseConverter._extract_metadata("""Test case\nMore details""") == "Test case"
            assert TestCaseConverter._extract_metadata("") == ""
            assert TestCaseConverter._extract_metadata(None) == ""
            print("✅ test_extract_metadata passed")
            tests_passed += 1
        except AssertionError as e:
            print(f"❌ test_extract_metadata failed: {e}")
            tests_failed += 1
    
    def test_python_to_json():
        """Test converting Python to JSON"""
        nonlocal tests_passed, tests_failed
        try:
            # Create a temporary directory
            test_dir = Path("test_temp")
            test_dir.mkdir(exist_ok=True)
            
            # Create a test Python file
            py_file = test_dir / "test_script.py"
            py_content = '"""Test case"""\n\ndef test_example():\n    pass'
            py_file.write_text(py_content)
            
            # Test without output file
            result = TestCaseConverter.python_to_json(str(py_file))
            assert result["name"] == "test_script"
            assert result["description"] == "Test case"
            assert "steps" in result
            
            # Test with output file
            json_file = test_dir / "output.json"
            result = TestCaseConverter.python_to_json(str(py_file), str(json_file))
            assert json_file.exists()
            
            # Clean up
            if test_dir.exists():
                for f in test_dir.glob("*"):
                    f.unlink()
                test_dir.rmdir()
                
            print("✅ test_python_to_json passed")
            tests_passed += 1
        except Exception as e:
            print(f"❌ test_python_to_json failed: {e}")
            tests_failed += 1
    
    # Run all tests
    print("\nRunning tests...\n" + "="*50)
    
    test_extract_metadata()
    test_python_to_json()
    
    # Print summary
    print("\n" + "="*50)
    print(f"Tests passed: {tests_passed}")
    print(f"Tests failed: {tests_failed}")
    print("="*50)
    
    return tests_failed == 0

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
