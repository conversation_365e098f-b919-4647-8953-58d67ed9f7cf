"""
全面的异常检测和告警演示程序

演示异常检测系统的完整功能，包括：
1. 多种异常类型的检测
2. 告警规则管理
3. 告警通知
4. 告警聚合与抑制
"""

import logging
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List

from src.workflow.ai.anomaly import AnomalyDetector, AnomalyType, AnomalySeverity
from src.workflow.ai.alert import AlertManager, AlertLevel, AlertStatus

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_detector_config() -> Dict:
    """获取异常检测器配置"""
    return {
        "contamination": 0.1,
        "n_estimators": 100,
        "execution_time_thresholds": {
            "low": 1.5,
            "medium": 2.0,
            "high": 3.0,
            "critical": 5.0
        },
        "resource_usage_thresholds": {
            "cpu": {
                "low": 50,
                "medium": 70,
                "high": 85,
                "critical": 95
            },
            "memory": {
                "low": 60,
                "medium": 75,
                "high": 85,
                "critical": 90
            }
        },
        "data_consistency_thresholds": {
            "mismatch_rate": {
                "low": 0.05,
                "medium": 0.1,
                "high": 0.15,
                "critical": 0.2
            },
            "validation_score": {
                "low": 0.9,
                "medium": 0.85,
                "high": 0.8,
                "critical": 0.7
            }
        },
        "performance_degradation_thresholds": {
            "response_time_ratio": {
                "low": 1.2,
                "medium": 1.5,
                "high": 2.0,
                "critical": 3.0
            },
            "throughput_drop": {
                "low": 0.9,
                "medium": 0.8,
                "high": 0.6,
                "critical": 0.4
            }
        },
        "security_risk_thresholds": {
            "suspicious_pattern_score": {
                "low": 0.3,
                "medium": 0.5,
                "high": 0.7,
                "critical": 0.85
            },
            "vulnerability_score": {
                "low": 3.0,
                "medium": 5.0,
                "high": 7.0,
                "critical": 9.0
            }
        }
    }

def get_alert_manager_config() -> Dict:
    """获取告警管理器配置"""
    return {
        "alert_rules": {
            "data_consistency": {
                "rule_id": "rule_004",
                "name": "数据一致性异常",
                "description": "检测到数据一致性问题",
                "severity_threshold": "HIGH",
                "anomaly_types": ["data_consistency"],
                "cooldown_period": 900,
                "aggregation_window": 300,
                "suppression_condition": {
                    "min_occurrences": 2,
                    "time_window": 600
                },
                "notification_channels": ["email", "slack"]
            },
            "performance_degradation": {
                "rule_id": "rule_005",
                "name": "性能退化异常",
                "description": "检测到持续的性能退化",
                "severity_threshold": "MEDIUM",
                "anomaly_types": ["performance_degradation"],
                "cooldown_period": 1800,
                "aggregation_window": 600,
                "suppression_condition": {
                    "min_duration": 900,
                    "trend_threshold": 0.8
                },
                "notification_channels": ["slack"]
            },
            "security_risk": {
                "rule_id": "rule_006",
                "name": "安全风险异常",
                "description": "检测到潜在的安全风险",
                "severity_threshold": "HIGH",
                "anomaly_types": ["security_risk"],
                "cooldown_period": 300,
                "aggregation_window": 300,
                "suppression_condition": {
                    "min_severity": "HIGH",
                    "max_alerts": 3
                },
                "notification_channels": ["email", "slack", "webhook"]
            }
        }
    }

def generate_test_data() -> List[Dict]:
    """生成测试数据"""
    test_data = []
    
    # 数据一致性异常场景
    test_data.append({
        "node_id": "node_1",
        "timestamp": datetime.now(),
        "data_format": "json",
        "format_validation_score": 0.65,
        "execution_time": 1.2,
        "resource_usage": {
            "cpu": 60,
            "memory": 70
        }
    })
    
    # 性能退化异常场景
    test_data.append({
        "node_id": "node_2",
        "timestamp": datetime.now(),
        "response_time": 4.5,
        "throughput": 0.4,
        "execution_time": 2.5,
        "resource_usage": {
            "cpu": 85,
            "memory": 90
        }
    })
    
    # 安全风险异常场景
    test_data.append({
        "node_id": "node_3",
        "timestamp": datetime.now(),
        "security_risk_score": 7.5,
        "execution_time": 1.8,
        "resource_usage": {
            "cpu": 75,
            "memory": 80
        }
    })
    
    # 多重异常场景
    test_data.append({
        "node_id": "node_4",
        "timestamp": datetime.now(),
        "response_time": 6.0,
        "throughput": 0.2,
        "security_risk_score": 9.0,
        "execution_time": 3.0,
        "resource_usage": {
            "cpu": 95,
            "memory": 95
        }
    })
    
    return test_data

def process_anomalies(detector: AnomalyDetector, alert_manager: AlertManager, data: List[Dict]):
    """处理异常和生成告警"""
    for node_data in data:
        logging.info(f"\n处理节点 {node_data['node_id']} 的数据...")
        
        # 检测各类型异常
        anomalies = []
        anomalies.extend(detector._detect_data_consistency_anomalies(node_data))
        anomalies.extend(detector._detect_performance_degradation_anomalies(node_data))
        anomalies.extend(detector._detect_security_risk_anomalies(node_data))
        
        # 处理检测到的异常
        if anomalies:
            logging.info(f"检测到 {len(anomalies)} 个异常:")
            for anomaly in anomalies:
                logging.info(f"- 类型: {anomaly.type}")
                logging.info(f"  严重度: {anomaly.severity}")
                logging.info(f"  置信度: {anomaly.confidence}")
                logging.info(f"  建议操作: {', '.join(anomaly.suggested_actions)}")
                
                # 生成告警
                alert_manager.process_anomaly(anomaly)
        else:
            logging.info("未检测到异常")

def main():
    """主函数"""
    logger.info("初始化异常检测和告警系统...")
    
    # 初始化检测器和告警管理器
    detector = AnomalyDetector(config=get_detector_config())
    alert_manager = AlertManager(config=get_alert_manager_config())
    
    logger.info("生成测试数据...")
    test_data = generate_test_data()
    
    logger.info("开始异常检测和告警处理...")
    for data in test_data:
        logger.info(f"\n处理节点 {data['node_id']} 的数据...")
        process_anomalies(detector, alert_manager, [data])
        time.sleep(1)  # 模拟数据处理间隔
    
    logger.info("\n演示完成!")

if __name__ == "__main__":
    main() 