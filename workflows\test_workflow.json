{"name": "recorded_workflow", "version": "1.0.0", "description": "Recorded workflow test case", "variables": {}, "steps": [{"id": "step_0", "type": "navigate", "description": "导航到 https://test.yushanyun.net/ac/web/", "condition": null, "retry": null, "timeout": null, "wait": null, "steps": [], "else_steps": [], "metadata": {"url": "https://test.yushanyun.net/ac/web/"}}, {"id": "step_1", "type": "navigate", "description": "导航到 https://test.yushanyun.net/ac/web/", "condition": null, "retry": null, "timeout": null, "wait": null, "steps": [], "else_steps": [], "metadata": {"url": "https://test.yushanyun.net/ac/web/"}}], "metadata": {"author": "Auto Recorder", "created_at": "2025-05-31T11:45:20.973071+00:00", "updated_at": "2025-05-31T11:45:20.973071+00:00"}, "created_at": "2025-05-31T11:45:20.973071", "updated_at": "2025-05-31T11:45:20.973071"}