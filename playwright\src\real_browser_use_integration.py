"""
真实browser-use集成实现

基于已安装的browser-use包，实现真实的AI代理执行功能
"""
import asyncio
import os
import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime

# 导入我们的AI智能交互模块
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../src')))
from ai_intelligent_interaction import get_interaction_manager, InteractionMode

# 导入browser-use
try:
    from browser_use import Agent
    from langchain_openai import ChatOpenAI
    BROWSER_USE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  browser-use导入失败: {e}")
    BROWSER_USE_AVAILABLE = False

logger = logging.getLogger(__name__)


class RealBrowserUseAgent:
    """
    真实的browser-use集成代理
    
    使用真实的browser-use库实现AI驱动的浏览器自动化
    """
    
    def __init__(self, llm_model: str = "gpt-4o"):
        """初始化真实browser-use代理"""
        self.llm_model = llm_model
        self.interaction_manager = get_interaction_manager()
        
        # 检查环境
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        if not self.openai_api_key:
            logger.warning("未设置OPENAI_API_KEY环境变量")
        
        # 执行历史
        self.execution_history = []
        
        # 配置
        self.max_steps = 10  # 最大执行步数
        self.save_conversation = True  # 保存对话历史
        
    def _check_prerequisites(self) -> bool:
        """检查执行前提条件"""
        if not BROWSER_USE_AVAILABLE:
            logger.error("browser-use未正确安装")
            return False
        
        if not self.openai_api_key:
            logger.error("未设置OPENAI_API_KEY环境变量")
            return False
        
        return True
    
    def _create_llm(self):
        """创建LLM实例"""
        try:
            return ChatOpenAI(
                model=self.llm_model,
                api_key=self.openai_api_key,
                temperature=0.1  # 降低随机性，提高一致性
            )
        except Exception as e:
            logger.error(f"创建LLM失败: {e}")
            raise
    
    def _create_agent(self, task: str) -> Optional[Agent]:
        """创建browser-use代理"""
        try:
            llm = self._create_llm()
            
            # 配置代理参数
            agent_config = {
                "task": task,
                "llm": llm,
                "max_steps": self.max_steps,
                "use_vision": True,  # 启用视觉能力
            }
            
            # 如果启用对话保存
            if self.save_conversation:
                timestamp = int(time.time())
                conversation_path = f"./conversation_history_{timestamp}.json"
                agent_config["save_conversation_path"] = conversation_path
            
            agent = Agent(**agent_config)
            logger.info(f"成功创建browser-use代理，任务: {task}")
            return agent
            
        except Exception as e:
            logger.error(f"创建browser-use代理失败: {e}")
            return None
    
    async def execute_user_request(self, user_input: str) -> Dict[str, Any]:
        """
        执行用户请求的完整流程
        
        Args:
            user_input: 用户输入的需求描述
            
        Returns:
            执行结果
        """
        logger.info(f"开始处理用户请求: {user_input}")
        
        # 检查前提条件
        if not self._check_prerequisites():
            return {
                "success": False,
                "error": "环境检查失败，请检查browser-use安装和API密钥配置",
                "user_input": user_input
            }
        
        try:
            # 阶段1: AI需求分析
            logger.info("阶段1: AI需求分析")
            session = self.interaction_manager.start_requirement_analysis_session(user_input)
            
            analysis_result = {
                "parsed_intent": session.user_requirement.parsed_intent,
                "business_domain": session.user_requirement.business_domain,
                "confidence": session.user_requirement.confidence,
                "workflow_matches": len(session.workflow_matches)
            }
            logger.info(f"需求分析结果: {analysis_result}")
            
            # 阶段2: 参数收集
            logger.info("阶段2: 参数收集")
            if session.workflow_matches:
                parameters = await self._collect_parameters(session)
                logger.info(f"收集到参数: {list(parameters.keys())}")
            else:
                logger.warning("未找到匹配的工作流")
                return {
                    "success": False,
                    "error": "未找到匹配的工作流",
                    "analysis_result": analysis_result,
                    "user_input": user_input
                }
            
            # 阶段3: 生成browser-use任务描述
            logger.info("阶段3: 生成任务描述")
            task_description = self._generate_enhanced_task_description(session, parameters)
            logger.info(f"生成的任务描述: {task_description[:200]}...")
            
            # 阶段4: 执行任务
            logger.info("阶段4: 执行browser-use任务")
            execution_result = await self._execute_with_browser_use(task_description)
            
            # 阶段5: 结果处理
            logger.info("阶段5: 结果处理")
            final_result = self._process_execution_result(
                execution_result, session, parameters, task_description
            )
            
            # 记录执行历史
            self._record_execution_history(user_input, session, task_description, final_result)
            
            return final_result
            
        except Exception as e:
            logger.error(f"执行用户请求失败: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "user_input": user_input,
                "timestamp": datetime.now().isoformat()
            }
    
    async def _collect_parameters(self, session) -> Dict[str, Any]:
        """收集执行所需的参数"""
        parameters = {}
        
        if not session.workflow_matches:
            return parameters
        
        best_match = session.workflow_matches[0]
        
        # 智能参数收集（实际应该通过用户交互界面）
        for param_name, param_info in best_match.required_parameters.items():
            if param_name == "customer_name":
                parameters[param_name] = "示例客户公司"
            elif param_name == "contact_person":
                parameters[param_name] = "张三"
            elif param_name == "phone":
                parameters[param_name] = "13800138000"
            elif param_name == "email":
                parameters[param_name] = "<EMAIL>"
            elif param_name == "username":
                parameters[param_name] = "demo_user"
            elif param_name == "password":
                parameters[param_name] = "demo_pass123"
            elif param_name == "search_term":
                parameters[param_name] = "AI自动化"
            elif param_name == "url":
                parameters[param_name] = "https://example.com"
            else:
                parameters[param_name] = f"示例_{param_name}"
        
        # 更新会话参数
        self.interaction_manager.provide_user_parameters(session.session_id, parameters)
        
        return parameters
    
    def _generate_enhanced_task_description(self, session, parameters: Dict[str, Any]) -> str:
        """生成增强的browser-use任务描述"""
        user_requirement = session.user_requirement
        intent = user_requirement.parsed_intent
        business_domain = user_requirement.business_domain or "通用"
        
        # 基础任务描述
        base_task = f"""
任务目标: {user_requirement.original_text}

业务领域: {business_domain}
操作意图: {intent}

参数信息:
{chr(10).join([f'- {k}: {v}' for k, v in parameters.items()])}

执行要求:
1. 请仔细分析页面内容，确保理解页面结构
2. 按照参数信息执行相应的操作
3. 如果遇到错误或异常，请尝试智能处理
4. 执行完成后，请确认操作结果
5. 如果需要等待页面加载，请耐心等待

"""
        
        # 根据意图添加具体指导
        if intent == "create" and "customer" in user_requirement.original_text.lower():
            base_task += """
具体操作指导:
- 寻找"创建客户"、"新建客户"或类似的按钮或链接
- 填写客户信息表单，包括客户名称、联系人、电话、邮箱等
- 确保所有必填字段都已填写
- 点击保存或提交按钮
- 确认客户创建成功
"""
        
        elif intent == "login":
            base_task += """
具体操作指导:
- 寻找登录页面或登录入口
- 输入用户名和密码
- 点击登录按钮
- 等待登录完成，确认登录成功
- 如果有验证码，请提示需要人工处理
"""
        
        elif intent == "query" or "搜索" in user_requirement.original_text:
            base_task += """
具体操作指导:
- 寻找搜索框或查询入口
- 输入搜索关键词
- 点击搜索按钮或按回车键
- 等待搜索结果加载
- 查看并分析搜索结果
"""
        
        elif intent == "generate" and "报表" in user_requirement.original_text:
            base_task += """
具体操作指导:
- 寻找报表生成功能入口
- 选择报表类型和时间范围
- 设置相关参数
- 点击生成或导出按钮
- 等待报表生成完成
"""
        
        else:
            base_task += """
具体操作指导:
- 根据用户需求和参数信息，智能分析需要执行的操作
- 按照常见的Web操作流程执行任务
- 注意页面的反馈信息，确保操作正确
"""
        
        return base_task.strip()
    
    async def _execute_with_browser_use(self, task_description: str) -> Dict[str, Any]:
        """使用browser-use执行任务"""
        try:
            # 创建代理
            agent = self._create_agent(task_description)
            if not agent:
                return {
                    "success": False,
                    "error": "无法创建browser-use代理"
                }
            
            logger.info("开始执行browser-use任务...")
            start_time = time.time()
            
            # 执行任务
            result = await agent.run()
            
            execution_time = time.time() - start_time
            logger.info(f"browser-use任务执行完成，耗时: {execution_time:.2f}秒")
            
            return {
                "success": True,
                "method": "browser-use",
                "result": result,
                "execution_time": execution_time,
                "message": "browser-use任务执行完成"
            }
            
        except Exception as e:
            logger.error(f"browser-use执行失败: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "method": "browser-use"
            }
    
    def _process_execution_result(self, execution_result: Dict[str, Any], 
                                session, parameters: Dict[str, Any], 
                                task_description: str) -> Dict[str, Any]:
        """处理执行结果"""
        final_result = {
            "success": execution_result.get("success", False),
            "execution_method": "real_browser_use",
            "user_requirement": session.user_requirement.original_text,
            "parsed_intent": session.user_requirement.parsed_intent,
            "business_domain": session.user_requirement.business_domain,
            "confidence": session.user_requirement.confidence,
            "parameters_used": parameters,
            "task_description": task_description,
            "execution_time": execution_result.get("execution_time", 0),
            "browser_use_result": execution_result.get("result"),
            "message": execution_result.get("message", ""),
            "timestamp": datetime.now().isoformat()
        }
        
        if execution_result.get("success"):
            logger.info(f"✅ 执行成功: {execution_result.get('message', '')}")
            final_result["status"] = "completed"
        else:
            logger.error(f"❌ 执行失败: {execution_result.get('error', '')}")
            final_result["error"] = execution_result.get("error")
            final_result["status"] = "failed"
        
        return final_result
    
    def _record_execution_history(self, user_input: str, session, 
                                task_description: str, final_result: Dict[str, Any]):
        """记录执行历史"""
        history_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_input": user_input,
            "session_id": session.session_id,
            "task_description": task_description,
            "result": final_result,
            "success": final_result.get("success", False),
            "execution_time": final_result.get("execution_time", 0)
        }
        
        self.execution_history.append(history_entry)
        
        # 保持历史记录数量在合理范围内
        if len(self.execution_history) > 100:
            self.execution_history = self.execution_history[-50:]
    
    def get_execution_history(self) -> list:
        """获取执行历史"""
        return self.execution_history.copy()
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        if not self.execution_history:
            return {
                "total_executions": 0,
                "success_rate": 0,
                "average_execution_time": 0,
                "most_common_intent": None
            }
        
        total = len(self.execution_history)
        successful = sum(1 for h in self.execution_history if h.get("success", False))
        
        execution_times = [h.get("execution_time", 0) for h in self.execution_history]
        avg_time = sum(execution_times) / len(execution_times) if execution_times else 0
        
        # 统计最常见的意图
        intents = [h["result"].get("parsed_intent") for h in self.execution_history 
                  if h["result"].get("parsed_intent")]
        most_common_intent = max(set(intents), key=intents.count) if intents else None
        
        return {
            "total_executions": total,
            "successful_executions": successful,
            "failed_executions": total - successful,
            "success_rate": successful / total * 100 if total > 0 else 0,
            "average_execution_time": avg_time,
            "most_common_intent": most_common_intent,
            "last_execution": self.execution_history[-1]["timestamp"] if self.execution_history else None
        }


# 全局实例
global_real_agent = RealBrowserUseAgent()


def get_real_browser_use_agent() -> RealBrowserUseAgent:
    """获取全局真实browser-use代理实例"""
    return global_real_agent
