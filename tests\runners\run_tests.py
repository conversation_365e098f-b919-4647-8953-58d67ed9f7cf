#!/usr/bin/env python3
"""
运行测试脚本

支持运行所有测试或指定测试模块/测试类/测试方法

使用示例:
    # 运行所有测试
    python run_tests.py
    
    # 运行指定模块的测试
    python run_tests.py tests.test_workflow_models
    
    # 运行指定测试类
    python run_tests.py tests.test_workflow_runner.TestWorkflowRunner
    
    # 运行指定测试方法
    python run_tests.py tests.test_workflow_runner.TestWorkflowRunner.test_workflow_runner_initialization
"""
import sys
import unittest
import pytest

def run_tests(test_path=None):
    """运行测试
    
    Args:
        test_path: 测试路径，可以是模块、类或方法
    """
    args = ["-v", "--tb=short", "--color=yes"]
    
    if test_path:
        args.append(test_path)
    
    # 添加项目根目录到 Python 路径
    import os
    import sys
    root_dir = os.path.dirname(os.path.abspath(__file__))
    if root_dir not in sys.path:
        sys.path.insert(0, root_dir)
    
    # 运行测试
    return pytest.main(args)

if __name__ == "__main__":
    test_path = sys.argv[1] if len(sys.argv) > 1 else None
    sys.exit(run_tests(test_path))
