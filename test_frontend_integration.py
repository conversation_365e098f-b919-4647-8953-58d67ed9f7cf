"""
AI+RPA 前端集成测试

测试新的Web前端与后端API的集成
"""
import asyncio
import json
import sys
import os
from pathlib import Path
import requests
import time

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def load_env():
    """加载环境变量"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value

def test_backend_api():
    """测试后端API"""
    print("🔧 测试后端API...")
    
    base_url = "http://localhost:8000"
    
    try:
        # 测试健康检查
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端API健康检查通过")
            health_data = response.json()
            print(f"   状态: {health_data.get('status')}")
            print(f"   组件: {health_data.get('components')}")
        else:
            print(f"❌ 后端API健康检查失败: {response.status_code}")
            return False
            
        # 测试聊天消息API
        chat_data = {
            "content": "测试消息",
            "type": "user"
        }
        response = requests.post(f"{base_url}/api/chat/message", json=chat_data, timeout=10)
        if response.status_code == 200:
            print("✅ 聊天消息API测试通过")
            chat_response = response.json()
            print(f"   响应: {chat_response.get('message')}")
            print(f"   成功: {chat_response.get('success')}")
        else:
            print(f"❌ 聊天消息API测试失败: {response.status_code}")
            
        # 测试工作流API
        response = requests.get(f"{base_url}/api/workflows", timeout=5)
        if response.status_code == 200:
            print("✅ 工作流列表API测试通过")
            workflows_data = response.json()
            workflows = workflows_data.get('workflows', [])
            print(f"   工作流数量: {len(workflows)}")
            for workflow in workflows[:3]:  # 显示前3个
                print(f"   - {workflow.get('name')} ({workflow.get('domain')})")
        else:
            print(f"❌ 工作流列表API测试失败: {response.status_code}")
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端API服务")
        print("   请确保后端服务正在运行 (python -m uvicorn main:app --reload)")
        return False
    except Exception as e:
        print(f"❌ 后端API测试失败: {e}")
        return False

def test_frontend_files():
    """测试前端文件"""
    print("📁 检查前端文件...")
    
    frontend_dir = Path("frontend")
    required_files = [
        "package.json",
        "src/AIRPAApp.js",
        "src/components/AIRPAChatPanel.js",
        "src/components/WorkflowListPanel.js",
        "src/index.js"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = frontend_dir / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (缺失)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺失 {len(missing_files)} 个必要文件")
        return False
    else:
        print("✅ 所有前端文件检查通过")
        return True

def test_workflow_files():
    """测试工作流文件"""
    print("📋 检查工作流文件...")
    
    workflows_dir = Path("smart_workflows")
    if not workflows_dir.exists():
        print("⚠️ smart_workflows目录不存在，将创建")
        workflows_dir.mkdir()
        return True
    
    workflow_files = list(workflows_dir.glob("*.json"))
    print(f"✅ 找到 {len(workflow_files)} 个工作流文件")
    
    for workflow_file in workflow_files[:3]:  # 检查前3个
        try:
            with open(workflow_file, 'r', encoding='utf-8') as f:
                workflow_data = json.load(f)
            
            name = workflow_data.get('name', 'Unknown')
            domain = workflow_data.get('domain', 'Unknown')
            steps = len(workflow_data.get('steps', []))
            print(f"   - {name} ({domain}) - {steps}步")
            
        except Exception as e:
            print(f"   ❌ {workflow_file.name}: {e}")
    
    return True

def test_ai_components():
    """测试AI组件"""
    print("🤖 测试AI组件...")
    
    try:
        # 测试智能命令处理器
        from intelligent_command_processor import get_intelligent_command_processor
        processor = get_intelligent_command_processor()
        print("✅ 智能命令处理器加载成功")
        
        # 测试命令处理
        result = asyncio.run(processor.process_command("测试命令"))
        print(f"   命令处理结果: {result.success}")
        print(f"   响应: {result.response}")
        
    except ImportError as e:
        print(f"⚠️ AI组件导入失败: {e}")
        print("   这是正常的，如果后端API正常工作")
        return True
    except Exception as e:
        print(f"❌ AI组件测试失败: {e}")
        return False
    
    return True

def test_integration():
    """集成测试"""
    print("🔗 执行集成测试...")
    
    # 模拟完整的用户交互流程
    base_url = "http://localhost:8000"
    
    try:
        # 1. 发送聊天消息
        print("1. 测试聊天交互...")
        chat_data = {
            "content": "打开百度",
            "type": "user"
        }
        response = requests.post(f"{base_url}/api/chat/message", json=chat_data, timeout=10)
        if response.status_code == 200:
            print("   ✅ 聊天交互成功")
        
        # 2. 获取工作流列表
        print("2. 测试工作流管理...")
        response = requests.get(f"{base_url}/api/workflows", timeout=5)
        if response.status_code == 200:
            workflows_data = response.json()
            workflows = workflows_data.get('workflows', [])
            print(f"   ✅ 获取到 {len(workflows)} 个工作流")
            
            # 3. 如果有工作流，测试执行
            if workflows:
                workflow = workflows[0]
                print(f"3. 测试工作流执行: {workflow.get('name')}")
                execution_data = {
                    "workflowId": workflow.get('id'),
                    "parameters": {}
                }
                response = requests.post(
                    f"{base_url}/api/workflows/{workflow.get('id')}/execute",
                    json=execution_data,
                    timeout=10
                )
                if response.status_code == 200:
                    print("   ✅ 工作流执行API调用成功")
                else:
                    print(f"   ⚠️ 工作流执行API返回: {response.status_code}")
        
        print("✅ 集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 AI+RPA 前端集成测试")
    print("=" * 50)
    
    # 加载环境变量
    load_env()
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("前端文件检查", test_frontend_files()))
    test_results.append(("工作流文件检查", test_workflow_files()))
    test_results.append(("AI组件测试", test_ai_components()))
    test_results.append(("后端API测试", test_backend_api()))
    test_results.append(("集成测试", test_integration()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！AI+RPA前端系统就绪")
        print("\n🚀 启动建议:")
        print("1. 运行 'python start_ai_rpa_app.py' 启动完整系统")
        print("2. 访问 http://localhost:3000 使用Web界面")
        print("3. 在AI助手中输入 '今天天气' 测试智能工作流")
    else:
        print(f"\n⚠️ {total - passed} 项测试失败，请检查相关组件")
        
        if not test_results[3][1]:  # 后端API测试失败
            print("\n💡 后端API测试失败的解决方案:")
            print("1. 确保后端服务正在运行:")
            print("   cd backend_api && python -m uvicorn main:app --reload")
            print("2. 检查端口8000是否被占用")
            print("3. 检查防火墙设置")

if __name__ == "__main__":
    main()
