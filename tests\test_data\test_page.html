<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page for Advanced Locator Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .search-container {
            margin-top: 30px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Test Page</h1>
            <nav>
                <ul style="display: flex; gap: 15px; list-style: none; padding: 0;">
                    <li><a href="#home" id="home-link">Home</a></li>
                    <li><a href="#about" id="about-link">About Us</a></li>
                    <li><a href="#contact" id="contact-link">Contact</a></li>
                </ul>
            </nav>
        </header>

        <main>
            <section class="login-form">
                <h2>Login</h2>
                <form>
                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" name="username" placeholder="Enter your username" data-testid="username-input">
                    </div>
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="password" placeholder="Enter your password">
                    </div>
                    <div class="form-group">
                        <button type="button" id="login-button" class="btn btn-primary" data-qa="login-btn">
                            Sign In
                        </button>
                        <div class="form-group">
                            <button type="button" id="test-button" title="Test Button" data-testid="test-button">
                                Test Me
                            </button>
                        </div>
                        <script>
                            // 初始化页面状态
                            window.buttonClicked = false;
                            window.inputValue = "";
                            
                            // 添加按钮点击事件监听
                            document.getElementById('test-button').addEventListener('click', function() {
                                window.buttonClicked = true;
                                console.log('Button clicked');
                            });
                            
                            // 添加输入框输入事件监听
                            document.getElementById('username').addEventListener('input', function(e) {
                                window.inputValue = e.target.value;
                                console.log('Input value changed:', window.inputValue);
                            });
                            
                            // 添加一个会失败多次然后成功的函数
                            window.simulateFailingOperation = async (failTimes = 0) => {
                                if (window.operationAttempts === undefined) {
                                    window.operationAttempts = 0;
                                }
                                
                                window.operationAttempts++;
                                if (window.operationAttempts <= failTimes) {
                                    throw new Error(`Operation failed on attempt ${window.operationAttempts}`);
                                }
                                return "success";
                            };
                        </script>
                    </div>
                </form>
            </section>

            <div class="search-container">
                <h3>Search</h3>
                <div class="form-group">
                    <label>Search our site:</label>
                    <input type="text" id="search" name="search" placeholder="Enter search terms...">
                    <button type="button" id="search-button">Search</button>
                </div>
            </div>

            <section>
                <h3>User Profile</h3>
                <div class="form-group">
                    <label for="fullname">Full Name:</label>
                    <input type="text" id="fullname" name="fullname" value="John Doe" readonly>
                </div>
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="text" id="email" name="email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <button type="button" id="save-profile" aria-label="Save profile changes">
                        Save Changes
                    </button>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2023 Test Page. All rights reserved.</p>
            <div>
                <a href="#privacy" class="footer-link">Privacy Policy</a> | 
                <a href="#terms" class="footer-link">Terms of Service</a>
            </div>
        </footer>
    </div>

    <!-- Hidden elements for testing -->
    <div id="hidden-element" style="display: none;">This is hidden</div>
    <div id="invisible-element" style="visibility: hidden;">This is invisible</div>
    <div id="transparent-element" style="opacity: 0;">This is transparent</div>
</body>
</html>
