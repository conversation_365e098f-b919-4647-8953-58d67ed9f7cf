"""
简单浏览器执行器

用于执行基本的浏览器操作
"""
import asyncio
import logging
from playwright.async_api import async_playwright

logger = logging.getLogger(__name__)

class SimpleBrowserExecutor:
    def __init__(self):
        self.browser = None
        self.page = None
        self.playwright = None
    
    async def start_browser(self):
        """启动浏览器"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=False,  # 显示浏览器窗口
                args=['--start-maximized']
            )
            context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            self.page = await context.new_page()
            logger.info("浏览器启动成功")
            return True
        except Exception as e:
            logger.error(f"浏览器启动失败: {e}")
            return False
    
    async def execute_workflow_steps(self, workflow, parameters):
        """执行工作流步骤"""
        try:
            if not self.page:
                if not await self.start_browser():
                    return "浏览器启动失败"
            
            steps = workflow.get('steps', [])
            results = []
            
            for i, step in enumerate(steps):
                action = step.get('action', '')
                target = step.get('target', '')
                value = step.get('value', '')
                description = step.get('description', '')
                
                logger.info(f"执行步骤 {i+1}: {action} - {description}")
                
                try:
                    if action == 'navigate':
                        # 导航到页面
                        await self.page.goto(target, wait_until='domcontentloaded', timeout=10000)
                        results.append(f"✅ 导航到: {target}")
                        await asyncio.sleep(1)  # 等待页面加载
                        
                    elif action == 'input':
                        # 输入文本
                        input_value = value
                        # 替换参数
                        if '{{search_query}}' in input_value:
                            input_value = parameters.get('search_query', input_value)
                        
                        # 等待元素出现
                        await self.page.wait_for_selector(target, timeout=5000)
                        await self.page.fill(target, input_value)
                        results.append(f"✅ 输入: {input_value} 到 {target}")
                        await asyncio.sleep(0.5)
                        
                    elif action == 'click':
                        # 点击元素
                        await self.page.wait_for_selector(target, timeout=5000)
                        await self.page.click(target)
                        results.append(f"✅ 点击: {target}")
                        await asyncio.sleep(2)  # 等待页面响应
                        
                    else:
                        results.append(f"⚠️ 未知操作: {action}")
                        
                except Exception as step_error:
                    error_msg = f"❌ 步骤 {i+1} 失败: {step_error}"
                    logger.error(error_msg)
                    results.append(error_msg)
                    # 继续执行下一步
            
            # 获取最终页面标题
            try:
                page_title = await self.page.title()
                results.append(f"📄 最终页面: {page_title}")
            except:
                pass
            
            return "\n".join(results)
            
        except Exception as e:
            logger.error(f"工作流执行失败: {e}")
            return f"执行失败: {e}"
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {e}")

# 全局浏览器执行器实例
browser_executor = SimpleBrowserExecutor()

async def execute_workflow_with_browser(workflow, parameters):
    """使用浏览器执行工作流"""
    try:
        result = await browser_executor.execute_workflow_steps(workflow, parameters)
        return result
    except Exception as e:
        logger.error(f"浏览器工作流执行失败: {e}")
        return f"浏览器执行失败: {e}"
