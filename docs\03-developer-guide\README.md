# 开发者指南

本指南面向希望扩展或定制系统功能的开发者。

## 系统架构

### 1. 核心组件

```
src/
├── recorder/           # 工作流录制
│   ├── __init__.py
│   ├── codegen.py     # Playwright Codegen 封装
│   ├── inspector.py   # 录制器 UI
│   └── converter.py   # 录制结果转换
├── executor/          # 工作流执行
│   ├── __init__.py
│   ├── engine.py     # 执行引擎
│   ├── context.py    # 执行上下文
│   └── steps.py      # 步骤执行器
├── monitor/          # 浏览器监控
│   ├── __init__.py
│   ├── metrics.py    # 指标收集
│   ├── analyzer.py   # 数据分析
│   └── reporter.py   # 报告生成
├── ai/               # AI 引擎
│   ├── __init__.py
│   ├── brain.py      # AI 核心
│   ├── repair.py     # 自动修复
│   └── optimize.py   # 优化建议
└── utils/            # 工具函数
    ├── __init__.py
    ├── browser.py    # 浏览器工具
    ├── logger.py     # 日志工具
    └── config.py     # 配置工具
```

### 2. 数据流

```mermaid
graph LR
    A[录制器] --> B[工作流定义]
    B --> C[执行器]
    C --> D[浏览器]
    D --> E[监控器]
    E --> F[AI 引擎]
    F --> C
```

### 3. 模块依赖

- Playwright: 浏览器自动化
- OpenAI: AI 能力支持
- FastAPI: API 服务
- SQLAlchemy: 数据存储
- Pydantic: 数据验证

## 开发环境

### 1. 环境设置

```bash
# 克隆仓库
git clone <repository-url>
cd playwright

# 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
.\.venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements-dev.txt
```

### 2. 开发工具

- VS Code + Python 插件
- PyCharm Professional
- Git + GitHub
- Docker Desktop

### 3. 代码规范

- PEP 8 编码规范
- Type Hints 类型提示
- Docstring 文档字符串
- 单元测试覆盖

## 扩展开发

### 1. 录制器扩展

#### 1.1 自定义录制器

```python
from src.recorder import CodegenWrapper

class CustomRecorder(CodegenWrapper):
    def __init__(self, output_file: str):
        super().__init__(output_file)
        
    def start_recording(self, url: str, **options):
        # 自定义录制逻辑
        pass
        
    def convert_to_workflow(self):
        # 自定义转换逻辑
        pass
```

#### 1.2 事件处理

```python
from src.recorder.events import EventHandler

class CustomEventHandler(EventHandler):
    async def handle_click(self, event):
        # 处理点击事件
        pass
        
    async def handle_input(self, event):
        # 处理输入事件
        pass
```

### 2. 执行器扩展

#### 2.1 自定义执行器

```python
from src.executor import WorkflowExecutor

class CustomExecutor(WorkflowExecutor):
    def __init__(self, workflow_file: str):
        super().__init__(workflow_file)
        
    async def execute_step(self, step: Step):
        # 自定义步骤执行逻辑
        pass
        
    async def handle_error(self, error: Exception):
        # 自定义错误处理逻辑
        pass
```

#### 2.2 步骤类型

```python
from src.executor.steps import BaseStep

class CustomStep(BaseStep):
    async def execute(self, context):
        # 自定义步骤执行
        pass
        
    async def validate(self):
        # 自定义步骤验证
        pass
```

### 3. 监控器扩展

#### 3.1 自定义监控器

```python
from src.monitor import BrowserMonitor

class CustomMonitor(BrowserMonitor):
    def __init__(self, config: dict):
        super().__init__(config)
        
    async def collect_metrics(self):
        # 自定义指标收集
        pass
        
    async def analyze_data(self, metrics: dict):
        # 自定义数据分析
        pass
```

#### 3.2 指标定义

```python
from src.monitor.metrics import BaseMetric

class CustomMetric(BaseMetric):
    async def collect(self):
        # 自定义指标收集
        pass
        
    async def analyze(self):
        # 自定义指标分析
        pass
```

### 4. AI 引擎扩展

#### 4.1 自定义 AI 模型

```python
from src.ai import BaseAI

class CustomAI(BaseAI):
    def __init__(self, config: dict):
        super().__init__(config)
        
    async def analyze(self, data: dict):
        # 自定义分析逻辑
        pass
        
    async def optimize(self, workflow: dict):
        # 自定义优化逻辑
        pass
```

#### 4.2 修复策略

```python
from src.ai.repair import BaseRepair

class CustomRepair(BaseRepair):
    async def diagnose(self, error: Exception):
        # 自定义诊断逻辑
        pass
        
    async def repair(self, diagnosis: dict):
        # 自定义修复逻辑
        pass
```

## API 参考

### 1. 录制器 API

```python
class CodegenWrapper:
    """Playwright Codegen 封装类"""
    
    def start_recording(self, url: str, **options):
        """启动录制"""
        
    def stop_recording(self):
        """停止录制"""
        
    def convert_to_workflow(self):
        """转换为工作流"""
```

### 2. 执行器 API

```python
class WorkflowExecutor:
    """工作流执行器"""
    
    async def execute(self):
        """执行工作流"""
        
    async def execute_step(self, step: Step):
        """执行单个步骤"""
        
    async def handle_error(self, error: Exception):
        """处理错误"""
```

### 3. 监控器 API

```python
class BrowserMonitor:
    """浏览器监控器"""
    
    async def start(self):
        """启动监控"""
        
    async def collect_metrics(self):
        """收集指标"""
        
    async def analyze_data(self, metrics: dict):
        """分析数据"""
```

### 4. AI API

```python
class BaseAI:
    """AI 基类"""
    
    async def analyze(self, data: dict):
        """分析数据"""
        
    async def optimize(self, workflow: dict):
        """优化工作流"""
        
    async def repair(self, error: Exception):
        """修复错误"""
```

## 测试指南

### 1. 单元测试

```python
# tests/test_recorder.py
from unittest import TestCase
from src.recorder import CodegenWrapper

class TestRecorder(TestCase):
    def setUp(self):
        self.recorder = CodegenWrapper("test.json")
        
    def test_recording(self):
        # 测试录制功能
        pass
        
    def test_conversion(self):
        # 测试转换功能
        pass
```

### 2. 集成测试

```python
# tests/integration/test_workflow.py
import pytest
from src.executor import WorkflowExecutor
from src.monitor import BrowserMonitor

@pytest.mark.integration
async def test_workflow_execution():
    # 测试完整工作流
    pass
```

### 3. 性能测试

```python
# tests/performance/test_monitor.py
import pytest
from src.monitor import BrowserMonitor

@pytest.mark.performance
async def test_monitor_performance():
    # 测试监控性能
    pass
```

## 部署指南

### 1. Docker 部署

```dockerfile
# Dockerfile
FROM python:3.8-slim

WORKDIR /app
COPY . .

RUN pip install -r requirements.txt
RUN playwright install

CMD ["python", "-m", "src.main"]
```

### 2. Kubernetes 部署

```yaml
# kubernetes/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-automation
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: workflow-automation
        image: workflow-automation:latest
```

## 贡献指南

### 1. 提交规范

- 遵循 Angular 提交规范
- 包含单元测试
- 更新文档
- 添加更新日志

### 2. 开发流程

1. Fork 项目
2. 创建特性分支
3. 提交变更
4. 发起 Pull Request

### 3. 代码审查

- 代码风格
- 测试覆盖
- 性能影响
- 安全考虑

## 下一步

- 查看[API 参考](api-reference/README.md)了解详细 API
- 查看[贡献指南](contribution/README.md)了解如何参与开发
- 查看[性能优化](../06-advanced/performance/README.md)了解性能调优 