{"name": "react-smooth", "version": "4.0.4", "description": "react animation library", "main": "lib/index", "module": "es6/index", "files": ["*.md", "es6", "lib", "umd", "src"], "keywords": ["react", "reactjs", "animation", "react-component"], "scripts": {"build": "npm run build-cjs && npm run build-es6 && rimraf umd && npm run build-umd && npm run build-min", "build-cjs": "rimraf lib && cross-env BABEL_ENV=commonjs babel ./src -d lib", "build-es6": "rimraf es6 && babel ./src -d es6", "build-umd": "cross-env NODE_ENV=development BABEL_ENV=commonjs webpack --entry ./src/index.js -o umd", "build-min": "cross-env NODE_ENV=production BABEL_ENV=commonjs webpack --entry ./src/index.js -o umd", "test": "cross-env BABEL_ENV=test jest", "demo": "webpack serve --config demo/webpack.config.js --port 4000 --host 127.0.0.1 --progress --profile --content-base demo/", "autofix": "eslint src --fix", "lint": "eslint src"}, "pre-commit": ["lint"], "repository": {"type": "git", "url": "https://github.com/recharts/react-smooth.git"}, "author": "JasonHzq", "bugs": {"url": "https://github.com/recharts/react-smooth/issues"}, "homepage": "https://github.com/recharts/react-smooth#readme", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "dependencies": {"fast-equals": "^5.0.1", "prop-types": "^15.8.1", "react-transition-group": "^4.4.5"}, "devDependencies": {"@babel/cli": "^7.23.0", "@babel/core": "^7.23.2", "@babel/eslint-parser": "^7.22.15", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/plugin-proposal-export-default-from": "^7.22.17", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-function-bind": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-runtime": "^7.23.2", "@babel/preset-env": "^7.23.2", "@babel/preset-react": "^7.22.15", "@babel/runtime": "^7.23.2", "@testing-library/dom": "^9.3.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "babel-loader": "^9.1.3", "core-js": "^3.33.0", "cross-env": "^7.0.3", "eslint": "^8.51.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.33.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "json-loader": "^0.5.7", "pre-commit": "^1.2.2", "prettier": "^2.8.8", "react": "^18.2.0", "react-dom": "^18.2.0", "webpack": "^5.89.0", "webpack-bundle-analyzer": "^4.9.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "sideEffects": false, "license": "MIT"}