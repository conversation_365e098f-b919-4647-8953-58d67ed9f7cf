"""
测试Gemini OpenAI兼容端点

测试使用自定义URL地址的Gemini API调用
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"✅ 加载环境变量: {key}")


def test_environment():
    """测试环境配置"""
    print("🔍 检查环境配置")
    print("-" * 30)
    
    # 检查必要的环境变量
    gemini_key = os.getenv('GEMINI_API_KEY')
    gemini_model = os.getenv('GEMINI_MODEL', 'gemini-pro')
    gemini_url = os.getenv('GEMINI_BASE_URL')
    
    print(f"GEMINI_API_KEY: {'✅ 已设置' if gemini_key else '❌ 未设置'}")
    print(f"GEMINI_MODEL: {gemini_model}")
    print(f"GEMINI_BASE_URL: {gemini_url or '❌ 未设置'}")
    
    if not gemini_key:
        print("❌ 缺少GEMINI_API_KEY")
        return False
    
    if not gemini_url:
        print("❌ 缺少GEMINI_BASE_URL")
        return False
    
    return True


async def test_direct_openai_compatible():
    """直接测试OpenAI兼容接口"""
    print("\n🧪 直接测试OpenAI兼容接口")
    print("-" * 40)
    
    try:
        from langchain_openai import ChatOpenAI
        
        gemini_key = os.getenv('GEMINI_API_KEY')
        gemini_model = os.getenv('GEMINI_MODEL', 'gemini-pro')
        gemini_url = os.getenv('GEMINI_BASE_URL')
        
        # 创建OpenAI兼容的客户端
        client = ChatOpenAI(
            model=gemini_model,
            api_key=gemini_key,
            base_url=gemini_url,
            temperature=0.1,
            timeout=30
        )
        
        print(f"✅ 客户端创建成功")
        print(f"   模型: {gemini_model}")
        print(f"   URL: {gemini_url}")
        
        # 测试简单调用
        print("\n🔍 测试简单调用:")
        response = await client.ainvoke("请说'Hello from Gemini via OpenAI API'")
        
        print(f"✅ 调用成功")
        print(f"📝 响应: {response.content}")
        print(f"📊 类型: {type(response)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_llm_manager_gemini():
    """测试LLM管理器中的Gemini"""
    print("\n🧪 测试LLM管理器中的Gemini")
    print("-" * 40)
    
    try:
        from ai_llm_manager import get_llm_manager, LLMProvider
        
        manager = get_llm_manager()
        available_providers = manager.get_available_providers()
        
        print(f"可用提供商: {[p.value for p in available_providers]}")
        
        if LLMProvider.GEMINI not in available_providers:
            print("❌ Gemini不在可用提供商中")
            return False
        
        print("✅ Gemini在可用提供商中")
        
        # 测试Gemini调用
        print("\n🔍 测试Gemini调用:")
        response = await manager.generate(
            "请用中文说'你好，我是通过OpenAI兼容接口调用的Gemini'",
            provider=LLMProvider.GEMINI
        )
        
        print(f"✅ 调用成功")
        print(f"📝 响应: {response.content}")
        print(f"📊 提供商: {response.provider.value}")
        print(f"📊 模型: {response.model}")
        if response.usage:
            print(f"📈 使用量: {response.usage}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_browser_use_integration():
    """测试browser-use集成"""
    print("\n🧪 测试browser-use集成")
    print("-" * 40)
    
    try:
        from real_browser_use_integration import get_real_browser_use_agent
        
        # 创建指定使用Gemini的代理
        agent = get_real_browser_use_agent(llm_provider="gemini")
        
        if not agent._check_prerequisites():
            print("❌ 前提条件检查失败")
            return False
        
        print("✅ 前提条件检查通过")
        
        # 创建LLM
        llm = agent._create_llm()
        print(f"✅ LLM创建成功: {llm.model}")
        
        # 测试需求分析
        print("\n🔍 测试需求分析:")
        test_request = "请帮我打开Google网站并搜索人工智能"
        
        session = agent.interaction_manager.start_requirement_analysis_session(test_request)
        print(f"✅ 需求分析成功")
        print(f"📝 用户需求: {test_request}")
        print(f"🧠 AI理解: {session.user_requirement.parsed_intent}")
        print(f"🏢 业务领域: {session.user_requirement.business_domain}")
        print(f"📊 置信度: {session.user_requirement.confidence:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ browser-use集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_multiple_requests():
    """测试多次请求"""
    print("\n🧪 测试多次请求")
    print("-" * 40)
    
    try:
        from ai_llm_manager import get_llm_manager, LLMProvider
        
        manager = get_llm_manager()
        
        test_prompts = [
            "请说'第一次调用'",
            "请用英文说'Second call'",
            "请用中文解释什么是AI",
            "请计算1+1等于几",
            "请说'最后一次调用'"
        ]
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"\n🔍 第{i}次调用: {prompt}")
            try:
                import time
                start_time = time.time()
                
                response = await manager.generate(prompt, provider=LLMProvider.GEMINI)
                
                end_time = time.time()
                response_time = end_time - start_time
                
                print(f"   ✅ 成功 (耗时: {response_time:.2f}秒)")
                print(f"   📝 响应: {response.content}")
                
            except Exception as e:
                print(f"   ❌ 失败: {e}")
                return False
        
        print(f"\n✅ 所有请求都成功完成")
        return True
        
    except Exception as e:
        print(f"❌ 多次请求测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🎭 Gemini OpenAI兼容端点测试")
    print("测试自定义URL地址的Gemini API调用")
    print("=" * 60)
    
    # 加载环境变量
    load_env()
    
    # 检查环境
    if not test_environment():
        print("\n❌ 环境配置不完整")
        print("💡 请确保设置了以下环境变量:")
        print("   GEMINI_API_KEY=your-api-key")
        print("   GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta/openai")
        print("   GEMINI_MODEL=gemini-2.5-flash-preview-05-20")
        return
    
    # 运行测试
    tests = [
        ("直接OpenAI兼容接口", test_direct_openai_compatible),
        ("LLM管理器Gemini", test_llm_manager_gemini),
        ("browser-use集成", test_browser_use_integration),
        ("多次请求测试", test_multiple_requests)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 Gemini OpenAI兼容端点测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！Gemini OpenAI兼容端点工作正常！")
        print(f"\n🚀 您现在可以:")
        print(f"   1. 使用Gemini执行browser-use任务")
        print(f"   2. 享受OpenAI兼容接口的稳定性")
        print(f"   3. 利用自定义URL解决网络问题")
        
        print(f"\n💡 使用示例:")
        print(f"   agent = get_real_browser_use_agent(llm_provider='gemini')")
        print(f"   result = await agent.execute_user_request('您的任务')")
        
    elif passed_tests > 0:
        print(f"\n🔄 部分测试通过")
        print(f"💡 基础功能可用，建议检查失败的测试项目")
        
    else:
        print(f"\n❌ 所有测试失败")
        print(f"💡 请检查:")
        print(f"   1. API密钥是否正确")
        print(f"   2. URL地址是否可访问")
        print(f"   3. 网络连接是否正常")
        print(f"   4. 模型名称是否正确")


if __name__ == "__main__":
    asyncio.run(main())
