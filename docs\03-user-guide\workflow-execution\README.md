# 工作流执行指南

本指南详细介绍如何执行已录制的工作流。

## 执行器概述

工作流执行器是系统的核心组件，负责：

- 解析工作流定义
- 执行自动化操作
- 处理异常情况
- 生成执行报告
- 与 AI 引擎交互
- 支持监控集成

## 执行器组件

### 1. 工作流引擎

- 工作流解析器
- 步骤执行器
- 变量管理器
- 上下文管理器
- 事件分发器

### 2. AI 引擎

- 异常分析
- 自动修复
- 优化建议
- 执行监控
- 报告生成

### 3. 浏览器控制

- 页面操作
- 元素交互
- 网络监控
- 性能分析
- 截图录制

## 使用方法

### 1. 基本执行

```bash
# 基本用法
python -m src.executor workflows/your_workflow.json

# 完整参数
python -m src.executor \
  workflows/your_workflow.json \
  --headless \
  --debug \
  --retry 3 \
  --timeout 30000 \
  --report-dir reports \
  --screenshot-dir screenshots
```

### 2. 变量配置

```bash
# 命令行变量
python -m src.executor workflows/login_workflow.json \
  --var username=test_user \
  --var password=test_pass

# 环境变量
export WF_USERNAME=test_user
export WF_PASSWORD=test_pass

# 配置文件
python -m src.executor workflows/login_workflow.json \
  --config config.json
```

配置文件示例 (`config.json`):
```json
{
  "variables": {
    "username": "test_user",
    "password": "test_pass"
  },
  "options": {
    "headless": true,
    "retry": 3,
    "timeout": 30000
  }
}
```

### 3. 执行模式

#### 3.1 标准模式

```bash
python -m src.executor workflows/workflow.json
```

#### 3.2 调试模式

```bash
python -m src.executor workflows/workflow.json --debug
```

#### 3.3 无头模式

```bash
python -m src.executor workflows/workflow.json --headless
```

#### 3.4 监控模式

```bash
python -m src.executor workflows/workflow.json --monitor
```

### 4. 高级功能

#### 4.1 重试机制

```bash
# 配置重试
python -m src.executor workflows/workflow.json \
  --retry 3 \
  --retry-delay 1000 \
  --retry-strategy exponential
```

#### 4.2 超时控制

```bash
# 设置超时
python -m src.executor workflows/workflow.json \
  --timeout 30000 \
  --step-timeout 5000
```

#### 4.3 截图设置

```bash
# 配置截图
python -m src.executor workflows/workflow.json \
  --screenshot on-failure \
  --screenshot-dir ./screenshots
```

#### 4.4 报告生成

```bash
# 生成报告
python -m src.executor workflows/workflow.json \
  --report html \
  --report-dir ./reports
```

## 最佳实践

### 1. 执行准备

- 验证工作流文件
- 准备测试数据
- 配置执行环境
- 设置错误处理

### 2. 执行过程

- 监控执行状态
- 查看实时日志
- 处理异常情况
- 收集执行数据

### 3. 执行后分析

- 检查执行报告
- 分析失败原因
- 优化执行配置
- 更新工作流定义

## 常见问题解决

### 1. 执行失败

**问题**: 工作流执行失败
**解决**:
- 检查选择器是否有效
- 验证页面加载状态
- 调整等待时间
- 查看错误日志

### 2. 性能问题

**问题**: 执行速度慢
**解决**:
- 使用无头模式
- 优化等待策略
- 减少不必要的检查
- 调整资源限制

### 3. 稳定性问题

**问题**: 执行不稳定
**解决**:
- 增加重试机制
- 添加健壮性检查
- 优化选择器策略
- 处理异常情况

## 进阶主题

### 1. 自定义执行器

可以通过扩展 `WorkflowExecutor` 类自定义执行行为：

```python
class CustomExecutor(WorkflowExecutor):
    def __init__(self, workflow_file: str):
        super().__init__(workflow_file)
        
    async def execute_step(self, step: Step):
        # 自定义步骤执行逻辑
        pass
        
    async def handle_error(self, error: Exception):
        # 自定义错误处理逻辑
        pass
```

### 2. 事件处理

可以注册自定义事件处理器：

```python
def custom_event_handler(event):
    if event.type == "step_start":
        # 处理步骤开始事件
        pass
    elif event.type == "step_end":
        # 处理步骤结束事件
        pass

executor.add_event_handler(custom_event_handler)
```

### 3. AI 配置

可以调整 AI 引擎参数：

```python
executor.configure_ai({
    "repair_enabled": true,
    "repair_timeout": 30000,
    "analysis_depth": "detailed"
})
```

## 下一步

- 查看[浏览器监控指南](../browser-monitoring/README.md)了解如何监控执行过程
- 查看[API 参考](../../04-developer-guide/api-reference/README.md)了解更多技术细节
- 查看[故障排除指南](../../06-advanced/troubleshooting/README.md)了解如何解决常见问题 