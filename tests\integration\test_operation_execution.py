"""
操作执行集成测试

该测试模块包含了对操作记录与执行功能的集成测试，
验证操作监听器、执行器以及工作流引擎的集成功能。
"""
import pytest
import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

from playwright.async_api import async_playwright, Page, Browser, BrowserContext

# 导入项目中的模块
from workflow.operations import (
    Operation,
    ClickOperation,
    FillOperation,
    NavigateOperation,
    WaitOperation,
    OperationFactory
)
from workflow.execution import OperationExecutor, ExecutionContext
from workflow.recording import OperationRecorder
from workflow.workflow_engine import WorkflowEngine

# 测试用的URL
TEST_PAGE = "file://" + str(Path(__file__).parent / "test_data" / "test_page.html")

class TestOperationExecution:
    """操作执行集成测试类"""
    
    @pytest.fixture(scope="class")
    def event_loop(self):
        """为测试类创建事件循环"""
        loop = asyncio.get_event_loop()
        yield loop
        loop.close()
    
    @pytest.fixture(scope="class")
    async def browser(self):
        """创建Playwright浏览器实例"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            try:
                yield browser
            finally:
                await browser.close()
    
    @pytest.fixture
    async def context(self, browser: Browser):
        """创建浏览器上下文"""
        context = await browser.new_context()
        try:
            yield context
        finally:
            await context.close()
    
    @pytest.fixture
    async def page(self, context: BrowserContext):
        """创建测试页面"""
        page = await context.new_page()
        await page.goto(TEST_PAGE)
        return page
    
    @pytest.fixture
    def operation_factory(self) -> OperationFactory:
        """创建操作工厂"""
        return OperationFactory()
    
    @pytest.fixture
    def recorder(self) -> OperationRecorder:
        """创建操作记录器"""
        return OperationRecorder()
    
    @pytest.fixture
    def executor(self) -> OperationExecutor:
        """创建操作执行器"""
        return OperationExecutor()
    
    @pytest.fixture
    def workflow_engine(self) -> WorkflowEngine:
        """创建工作流引擎"""
        return WorkflowEngine()
    
    async def test_operation_sequence_execution(self, page: Page, executor: OperationExecutor):
        """测试操作序列执行"""
        # 创建操作序列
        operations = [
            NavigateOperation(url=TEST_PAGE),
            WaitOperation(selector="#input1", timeout=5000),
            FillOperation(selector="#input1", text="Test Input"),
            ClickOperation(selector="#submit-btn")
        ]
        
        # 执行操作序列
        context = ExecutionContext(page=page)
        results = []
        
        for op in operations:
            result = await executor.execute(op, context)
            results.append(result)
            assert result.success, f"操作执行失败: {result.error}"
        
        # 验证操作执行结果
        assert len(results) == 4
        assert results[0].operation.type == "navigate"
        assert results[1].operation.type == "wait"
        assert results[2].operation.type == "fill"
        assert results[3].operation.type == "click"
        
        # 验证页面状态
        value = await page.input_value("#input1")
        assert value == "Test Input"
    
    async def test_operation_retry(self, page: Page, executor: OperationExecutor):
        """测试操作重试机制"""
        # 创建一个会失败的操作（元素不存在）
        op = ClickOperation(
            selector="#non-existent-element",
            retry_policy={
                "max_attempts": 3,
                "backoff_factor": 0.1
            }
        )
        
        # 执行操作并验证重试
        start_time = datetime.now()
        result = await executor.execute(op, ExecutionContext(page=page))
        end_time = datetime.now()
        
        # 验证结果
        assert not result.success
        assert result.attempts == 3
        assert (end_time - start_time).total_seconds() >= 0.3  # 验证退避时间
    
    async def test_operation_recording(self, page: Page, recorder: OperationRecorder):
        """测试操作记录功能"""
        # 开始记录
        await recorder.start()
        
        # 执行一些操作
        await page.goto(TEST_PAGE)
        await page.fill("#input1", "Recorded Input")
        await page.click("#submit-btn")
        
        # 停止记录并获取操作
        operations = await recorder.stop()
        
        # 验证记录的操作
        assert len(operations) >= 2  # 至少包含填充和点击操作
        assert any(isinstance(op, FillOperation) for op in operations)
        assert any(isinstance(op, ClickOperation) for op in operations)
    
    async def test_workflow_execution(self, page: Page, workflow_engine: WorkflowEngine):
        """测试工作流执行"""
        # 定义工作流
        workflow = {
            "name": "Test Workflow",
            "nodes": [
                {
                    "id": "start",
                    "type": "start",
                    "next": ["navigate"]
                },
                {
                    "id": "navigate",
                    "type": "navigate",
                    "url": TEST_PAGE,
                    "next": ["fill"]
                },
                {
                    "id": "fill",
                    "type": "fill",
                    "selector": "#input1",
                    "text": "Workflow Test",
                    "next": ["click"]
                },
                {
                    "id": "click",
                    "type": "click",
                    "selector": "#submit-btn",
                    "next": ["end"]
                },
                {
                    "id": "end",
                    "type": "end"
                }
            ]
        }
        
        # 执行工作流
        context = ExecutionContext(page=page)
        result = await workflow_engine.execute_workflow(workflow, context)
        
        # 验证执行结果
        assert result.success
        assert result.completed_nodes == 4  # navigate, fill, click, end
        
        # 验证页面状态
        value = await page.input_value("#input1")
        assert value == "Workflow Test"
    
    async def test_error_handling(self, page: Page, executor: OperationExecutor):
        """测试错误处理"""
        # 创建一个会失败的操作
        op = ClickOperation(
            selector="#non-existent-element",
            retry_policy={
                "max_attempts": 1
            }
        )
        
        # 执行操作
        result = await executor.execute(op, ExecutionContext(page=page))
        
        # 验证错误处理
        assert not result.success
        assert result.error is not None
        assert "Element not found" in str(result.error)

if __name__ == "__main__":
    pytest.main(["-v", "-s", __file__])
