"""
手动加载并运行测试
"""
import sys
import os
import unittest

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath('.'))

def load_tests(loader, standard_tests, pattern):
    """加载测试"""
    # 导入测试模块
    import tests.test_operations as test_module
    
    # 加载测试
    test_suite = loader.loadTestsFromModule(test_module)
    return test_suite

def run_tests():
    """运行测试"""
    # 创建测试加载器
    loader = unittest.TestLoader()
    
    # 加载测试
    test_suite = load_tests(loader, None, None)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n测试结果:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败测试数: {len(result.failures) if hasattr(result, 'failures') else 0}")
    print(f"错误测试数: {len(result.errors) if hasattr(result, 'errors') else 0}")
    
    # 返回测试结果
    return result.wasSuccessful()

if __name__ == "__main__":
    # 运行测试
    success = run_tests()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)
