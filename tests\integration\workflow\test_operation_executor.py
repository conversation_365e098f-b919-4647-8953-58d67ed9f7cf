"""
操作执行器集成测试 - 简化版
"""
import os
import sys
from pathlib import Path

import pytest
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入测试所需的模块
try:
    from src.workflow.operations.executor import OperationExecutor
    from src.workflow.operations.operations import ClickOperation
    from src.workflow.operations.base import ElementSelector, RetryStrategy
except ImportError:
    from workflow.operations.executor import OperationExecutor
    from workflow.operations.operations import ClickOperation
    from workflow.operations.base import ElementSelector, RetryStrategy

# 测试页面路径 - 使用相对路径
test_data_dir = Path(__file__).parent.parent.parent / "test_data"
TEST_HTML = test_data_dir / "simple_test_page.html"

# 确保测试页面存在
if not TEST_HTML.exists():
    raise FileNotFoundError(f"测试页面不存在: {TEST_HTML}")

# 构建文件URL - 处理Windows路径
test_url = f"file:///{TEST_HTML.absolute().as_posix().replace('//', '/').replace(':/', ':/').lstrip('/')}"
print(f"测试页面路径: {TEST_HTML}")
print(f"测试页面URL: {test_url}")

print(f"测试页面路径: {TEST_HTML}")
print(f"测试页面URL: {test_url}")

class TestOperationExecutor:
    """测试操作执行器"""
    
    @pytest.fixture(scope="class")
    def browser(self):
        """启动Playwright浏览器"""
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            yield browser
            browser.close()

    @pytest.fixture(scope="class")
    def context(self, browser):
        """创建浏览器上下文"""
        context = browser.new_context()
        yield context
        context.close()

    @pytest.fixture(scope="class")
    def page(self, context):
        """创建测试页面"""
        print("\n=== 设置测试页面 ===")
        print(f"测试页面路径: {TEST_HTML}")
        print(f"测试页面URL: {test_url}")
        
        page = context.new_page()
        
        # 设置超时
        page.set_default_timeout(10000)  # 10秒超时
        
        # 启用控制台日志
        def handle_console(msg):
            print(f"浏览器控制台: {msg.text}")
        page.on("console", handle_console)
        
        # 页面错误处理
        def handle_page_error(error):
            print(f"页面错误: {error}")
        page.on("pageerror", handle_page_error)
        
        # 导航到测试页面
        print(f"正在导航到: {test_url}")
        
        try:
            # 先访问一个空白页面
            page.goto("about:blank")
            
            # 然后导航到测试页面
            response = page.goto(test_url, wait_until="domcontentloaded")
            if not response.ok:
                print(f"页面加载失败: {response.status} {response.status_text}")
            
            # 等待页面加载完成
            print("等待页面加载完成...")
            page.wait_for_load_state("networkidle")
            
            # 等待测试按钮可见
            print("等待测试按钮...")
            page.wait_for_selector("#test-button", state="visible", timeout=5000)
            
            # 重置页面状态
            print("重置页面状态...")
            page.evaluate('''() => {
                console.log('重置页面状态...');
                window.buttonClicked = false;
                window.inputValue = "";
                return {
                    button: document.querySelector('#test-button') !== null,
                    username: document.querySelector('#username') !== null
                };
            }''')
            
            print("=== 测试页面设置完成 ===\n")
            yield page
            
        except Exception as e:
            print(f"页面设置失败: {str(e)}")
            # 获取页面信息以帮助调试
            try:
                print(f"当前URL: {page.url}")
                print(f"页面标题: {page.title()}")
                print(f"页面内容前500字符: {page.content()[:500]}")
            except Exception as e2:
                print(f"获取页面信息失败: {str(e2)}")
            raise
        finally:
            page.close()

    @pytest.fixture
    def executor(self, page):
        """创建操作执行器"""
        # 确保页面已加载
        page.goto(test_url)
        page.wait_for_load_state("networkidle")
        
        # 重置页面状态
        page.evaluate('''() => {
            window.buttonClicked = false;
            const username = document.querySelector('#username');
            if (username) username.value = '';
            window.inputValue = '';
        }''')
        
        return OperationExecutor(page)

    def test_page_content(self, page):
        """验证测试页面内容"""
        print("\n=== 开始验证测试页面内容 ===")
        
        # 获取页面标题
        title = page.title()
        print(f"页面标题: {title}")
        assert "Simple Test Page" in title, f"页面标题不匹配: {title}"
        
        # 获取页面URL
        print(f"页面URL: {page.url}")
        
        # 检查测试按钮是否存在
        button_info = page.evaluate('''() => {
            const btn = document.querySelector('#test-button');
            if (!btn) return { exists: false };
            
            return {
                exists: true,
                text: btn.textContent.trim(),
                disabled: btn.disabled,
                hidden: btn.hidden,
                style: window.getComputedStyle(btn).visibility
            };
        }''')
        
        print(f"按钮信息: {button_info}")
        assert button_info['exists'], "测试按钮不存在"
        
        # 重置按钮点击状态
        page.evaluate('''() => {
            window.buttonClicked = false;
            console.log('按钮点击状态已重置');
        }''')
        
        print("=== 测试页面验证通过 ===\n")
        assert True, "测试页面验证通过"

    def test_click_operation(self, executor, page):
        """测试点击操作"""
        print("\n=== 开始测试点击操作 ===")
        
        # 打印当前页面信息
        print(f"当前页面标题: {page.title()}")
        print(f"当前页面URL: {page.url}")
        
        # 检查页面内容
        content = page.content()
        print(f"页面内容长度: {len(content)} 字符")
        print(f"页面内容前500字符: {content[:500]}...")
        
        # 检查测试按钮是否存在
        button_exists = page.evaluate('''() => {
            const btn = document.querySelector('#test-button');
            return {
                exists: !!btn,
                outerHTML: btn ? btn.outerHTML : '按钮不存在',
                text: btn ? btn.textContent : '',
                disabled: btn ? btn.disabled : false,
                hidden: btn ? btn.hidden : false,
                style: btn ? window.getComputedStyle(btn).visibility : 'N/A',
                inViewport: btn ? {
                    top: btn.getBoundingClientRect().top,
                    left: btn.getBoundingClientRect().left,
                    visible: (
                        btn.offsetParent !== null &&
                        window.getComputedStyle(btn).visibility !== 'hidden' &&
                        window.getComputedStyle(btn).display !== 'none' &&
                        parseFloat(window.getComputedStyle(btn).opacity) > 0
                    )
                } : {}
            };
        }''')
        
        print(f"按钮检查结果: {button_exists}")
        
        if not button_exists['exists']:
            # 获取所有按钮
            all_buttons = page.evaluate('''() => {
                const buttons = Array.from(document.getElementsByTagName('button'));
                return buttons.map(btn => ({
                    id: btn.id,
                    text: btn.textContent.trim(),
                    class: btn.className,
                    disabled: btn.disabled,
                    hidden: btn.hidden,
                    style: window.getComputedStyle(btn).visibility
                }));
            }''')
            print(f"页面上的所有按钮: {all_buttons}")
            
            # 获取所有元素
            all_elements = page.evaluate('''() => {
                const elements = Array.from(document.querySelectorAll('*'));
                return elements.map(el => ({
                    tag: el.tagName,
                    id: el.id,
                    class: el.className,
                    text: el.textContent.trim()
                }));
            }''')
            print(f"页面上的所有元素 (前10个): {all_elements[:10]}")
            
            raise AssertionError("测试按钮 #test-button 不存在")
        
        # 重置按钮点击状态
        page.evaluate('''() => {
            window.buttonClicked = false;
            console.log('按钮点击状态已重置');
        }''')
        
        # 创建点击操作
        print("创建点击操作...")
        selector = ElementSelector(selector="#test-button", selector_type="css")
        operation = ClickOperation(
            id="test_click",
            name="Test Click",
            target=selector,
            parameters={},
            wait_conditions=[],
            retry_strategy=RetryStrategy(max_attempts=3, delay=0.1)
        )
        operation.timeout = 10  # 设置10秒超时
        
        # 执行点击操作
        print("执行点击操作...")
        try:
            result = executor._execute_operation(operation)
            print(f"点击操作执行结果: {result}")
            
            # 验证按钮点击事件是否触发
            is_clicked = page.evaluate('''() => {
                return window.buttonClicked === true;
            }''')
            
            print(f"按钮点击状态: {is_clicked}")
            
            assert is_clicked, "按钮点击事件未触发"
            print("=== 点击操作测试通过 ===\n")
            
        except Exception as e:
            print(f"点击操作执行失败: {str(e)}")
            
            # 获取页面截图
            try:
                screenshot = page.screenshot()
                with open("click_error.png", "wb") as f:
                    f.write(screenshot)
                print("已保存错误截图: click_error.png")
            except Exception as screenshot_error:
                print(f"保存截图失败: {str(screenshot_error)}")
                
        except Exception as e:
            print(f"测试失败: {str(e)}")
            # 获取页面HTML以帮助调试
            try:
                html = page.content()
                print(f"当前页面URL: {page.url}")
                print(f"当前页面标题: {page.title()}")
                print(f"当前页面HTML: {html[:500]}...")  # 只打印前500个字符
            except Exception as html_error:
                print(f"获取页面信息失败: {str(html_error)}")
            raise

    def test_fill_operation(self, executor, page):
        """测试填充操作"""
        # 创建填充操作
        selector = ElementSelector(selector="#username", selector_type="css")
        test_text = "test_user"
        operation = FillOperation(
            id="test_fill",
            name="Test Fill",
            target=selector,
            value=test_text,
            parameters={},
            wait_conditions=[],
            retry_strategy=RetryStrategy(max_attempts=3, delay=0.1)
        )
        operation.timeout = 5  # 设置5秒超时
        
        # 确保输入框初始值为空
        initial_value = page.evaluate('''() => {
            return document.querySelector('#username').value;
        }''')
        assert initial_value == "", "输入框初始值应为空"
        
        # 执行操作
        result = executor._execute_operation(operation)
        
        # 验证结果
        assert result is True
        
        # 验证输入框值
        input_value = page.evaluate('''() => {
            return document.querySelector('#username').value;
        }''')
        
        assert input_value == test_text

    def test_operation_retry(self, executor, page):
        """测试操作重试"""
        # 创建一个会失败2次然后成功的操作
        class FailingOperation(ClickOperation):
            def __init__(self, element: ElementSelector, fail_times: int = 2):
                super().__init__(
                    id="test_failing_click",
                    name="Test Failing Click",
                    target=element,
                    parameters={},
                    wait_conditions=[],
                    retry_strategy=RetryStrategy(max_attempts=3, delay=0.1)
                )
                self.fail_times = fail_times
                self.attempts = 0
                self.max_retries = 3
                self.retry_delay = 0.1
                self.timeout = 5  # 设置5秒超时
            
            def execute(self, *args, **kwargs):
                self.attempts += 1
                if self.attempts <= self.fail_times:
                    error = Exception(f"Operation failed on attempt {self.attempts}")
                    error.retry_count = self.attempts - 1
                    raise error
                return super().execute(*args, **kwargs)
        
        # 创建操作
        selector = ElementSelector(css="#test-button")
        operation = FailingOperation(element=selector, fail_times=2)
        
        # 执行操作
        result = executor._execute_operation(operation)
        
        # 验证结果
        assert result is True
        assert operation.attempts == 3  # 1次初始尝试 + 2次重试
        
        # 验证按钮点击事件已触发
        button_clicked = page.evaluate('''() => {
            return window.buttonClicked || false;
        }''')
        
        assert button_clicked is True

    def test_operation_timeout(self, executor, page):
        """测试操作超时"""
        # 创建一个会超时的操作
        class TimeoutOperation(WaitOperation):
            def __init__(self, timeout: float = 2.0):  # 增加超时时间到2秒
                super().__init__(
                    id="test_timeout",
                    name="Test Timeout",
                    duration=0.1,  # 设置较短的超时时间
                    parameters={},
                    wait_conditions=[],
                    retry_strategy=RetryStrategy(max_attempts=0)  # 禁用重试
                )
                self.timeout = 0.1  # 设置操作超时时间为0.1秒
                self._duration = timeout  # 实际等待时间
            
            def execute(self, *args, **kwargs):
                # 模拟长时间运行的操作
                import time
                time.sleep(self._duration)
                return super().execute(*args, **kwargs)
        
        # 创建操作
        operation = TimeoutOperation(timeout=2.0)  # 设置2秒的等待时间，超过操作超时时间0.1秒
        
        # 执行操作，预期会抛出超时异常
        with pytest.raises(OperationTimeoutError) as exc_info:
            executor._execute_operation(operation)
        
        assert "timed out" in str(exc_info.value).lower()

    def test_operation_dependencies(self, executor, page):
        """测试操作依赖关系"""
        # 创建两个操作，第二个操作依赖第一个操作
        click_selector = ElementSelector(selector="#test-button", selector_type="css")
        fill_selector = ElementSelector(selector="#username", selector_type="css")
        
        click_op = ClickOperation(
            id="test_click_dep",
            name="Test Click Dependency",
            target=click_selector,
            parameters={},
            wait_conditions=[],
            retry_strategy=RetryStrategy(max_attempts=3, delay=0.1)
        )
        click_op.timeout = 5  # 设置5秒超时
        
        fill_op = FillOperation(
            id="test_fill_dep",
            name="Test Fill Dependency",
            target=fill_selector,
            value="after_click",
            parameters={},
            wait_conditions=[],
            retry_strategy=RetryStrategy(max_attempts=3, delay=0.1)
        )
        fill_op.timeout = 5  # 设置5秒超时
        
        # 设置依赖关系
        fill_op.depends_on = [click_op]
        
        # 确保初始状态
        initial_clicked = page.evaluate('''() => {
            return window.buttonClicked || false;
        }''')
        initial_value = page.evaluate('''() => {
            return document.querySelector('#username').value;
        }''')
        
        assert initial_clicked is False, "按钮初始状态应为未点击"
        assert initial_value == "", "输入框初始值应为空"
        
        # 执行操作
        result = executor._execute_operation(fill_op)
        
        # 验证结果
        assert result is True
        
        # 验证按钮点击事件已触发
        button_clicked = page.evaluate('''() => {
            return window.buttonClicked || false;
        }''')
        
        # 验证输入框值
        input_value = page.evaluate('''() => {
            return document.querySelector('#username').value;
        }''')
        
        assert button_clicked is True
        assert input_value == "after_click"

if __name__ == "__main__":
    pytest.main(["-v", __file__])
