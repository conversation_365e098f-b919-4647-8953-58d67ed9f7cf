"""
综合AI平台测试

测试所有可用的AI平台，包括网络问题处理
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))

# 设置环境变量
def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value


async def test_all_available_llms():
    """测试所有可用的LLM"""
    print("🧪 测试所有可用的LLM平台")
    print("=" * 50)
    
    # 加载环境变量
    load_env()
    
    try:
        from ai_llm_manager import get_llm_manager, LLMProvider
        
        manager = get_llm_manager()
        available_providers = manager.get_available_providers()
        
        print(f"检测到的可用提供商: {[p.value for p in available_providers]}")
        
        if not available_providers:
            print("❌ 没有可用的LLM提供商")
            print("💡 请配置至少一个API密钥:")
            print("   - OPENAI_API_KEY")
            print("   - GEMINI_API_KEY") 
            print("   - QWEN_API_KEY")
            return False
        
        # 测试每个可用的提供商
        test_results = {}
        test_prompt = "请简单说'Hello World'"
        
        for provider in available_providers:
            print(f"\n🔍 测试 {provider.value} 提供商:")
            
            try:
                import time
                start_time = time.time()
                
                response = await manager.generate(test_prompt, provider=provider)
                
                end_time = time.time()
                response_time = end_time - start_time
                
                test_results[provider.value] = {
                    "success": True,
                    "response": response.content,
                    "model": response.model,
                    "response_time": response_time,
                    "usage": response.usage
                }
                
                print(f"   ✅ 测试成功")
                print(f"   📝 响应: {response.content}")
                print(f"   📊 模型: {response.model}")
                print(f"   ⏱️  响应时间: {response_time:.2f}秒")
                if response.usage:
                    print(f"   📈 Token使用: {response.usage}")
                
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
                test_results[provider.value] = {
                    "success": False,
                    "error": str(e)
                }
        
        # 生成对比报告
        print(f"\n📊 LLM提供商对比报告:")
        print("-" * 40)
        
        successful_providers = []
        for provider, result in test_results.items():
            if result["success"]:
                successful_providers.append(provider)
                print(f"✅ {provider}:")
                print(f"   模型: {result['model']}")
                print(f"   响应时间: {result['response_time']:.2f}秒")
                if result['usage']:
                    print(f"   Token使用: {result['usage']}")
            else:
                print(f"❌ {provider}: {result['error'][:100]}...")
        
        if successful_providers:
            print(f"\n🎉 成功的提供商: {successful_providers}")
            return True
        else:
            print(f"\n❌ 所有提供商都失败了")
            return False
        
    except Exception as e:
        print(f"❌ LLM管理器测试失败: {e}")
        return False


async def test_browser_use_integration():
    """测试browser-use集成"""
    print("\n🧪 测试browser-use集成")
    print("=" * 50)
    
    try:
        from real_browser_use_integration import get_real_browser_use_agent
        
        # 测试默认代理（自动选择最佳LLM）
        print("🔍 测试默认代理（自动选择）:")
        agent = get_real_browser_use_agent()
        
        if not agent._check_prerequisites():
            print("   ❌ 前提条件检查失败")
            return False
        
        print("   ✅ 前提条件检查通过")
        
        # 创建LLM
        try:
            llm = agent._create_llm()
            print(f"   ✅ LLM创建成功: {llm.model}")
            
            # 检查实际使用的提供商
            if hasattr(llm, '_llm_type'):
                print(f"   📊 使用的提供商: {llm._llm_type}")
            
        except Exception as e:
            print(f"   ❌ LLM创建失败: {e}")
            return False
        
        # 测试AI需求分析
        print("\n🔍 测试AI需求分析:")
        test_requests = [
            "请帮我打开百度网站",
            "请帮我搜索人工智能信息"
        ]
        
        for i, request in enumerate(test_requests, 1):
            print(f"\n   测试需求 {i}: {request}")
            try:
                session = agent.interaction_manager.start_requirement_analysis_session(request)
                print(f"   ✅ 需求分析成功")
                print(f"   📝 解析意图: {session.user_requirement.parsed_intent}")
                print(f"   🏢 业务领域: {session.user_requirement.business_domain}")
                print(f"   📊 置信度: {session.user_requirement.confidence:.2f}")
            except Exception as e:
                print(f"   ❌ 需求分析失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ browser-use集成测试失败: {e}")
        return False


async def test_fallback_mechanism():
    """测试降级机制"""
    print("\n🧪 测试降级机制")
    print("=" * 50)
    
    try:
        from ai_llm_manager import get_llm_manager
        
        manager = get_llm_manager()
        
        # 测试自动降级
        print("🔍 测试自动降级:")
        try:
            response = await manager.generate("测试自动降级功能")
            print(f"   ✅ 自动降级成功")
            print(f"   📊 使用的提供商: {response.provider.value}")
            print(f"   📝 响应: {response.content[:100]}...")
        except Exception as e:
            print(f"   ❌ 自动降级失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 降级机制测试失败: {e}")
        return False


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置")
    print("=" * 30)
    
    # 检查API密钥
    api_keys = {
        "OpenAI": os.getenv('OPENAI_API_KEY'),
        "Gemini": os.getenv('GEMINI_API_KEY'),
        "通义千问": os.getenv('QWEN_API_KEY') or os.getenv('DASHSCOPE_API_KEY')
    }
    
    configured_count = 0
    for provider, key in api_keys.items():
        if key:
            print(f"   ✅ {provider}: 已配置 (长度: {len(key)})")
            configured_count += 1
        else:
            print(f"   ❌ {provider}: 未配置")
    
    print(f"\n📊 配置状态: {configured_count}/{len(api_keys)} 个平台已配置")
    
    # 检查依赖
    print(f"\n📦 检查依赖:")
    dependencies = {
        "langchain-openai": "OpenAI支持",
        "google-generativeai": "Gemini支持", 
        "dashscope": "通义千问支持",
        "browser-use": "browser-use核心"
    }
    
    for package, description in dependencies.items():
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}: 已安装 ({description})")
        except ImportError:
            print(f"   ❌ {package}: 未安装 ({description})")
    
    return configured_count > 0


async def main():
    """主函数"""
    print("🎭 综合AI平台测试")
    print("测试所有可用的AI平台和集成功能")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境配置不完整，请配置至少一个AI平台")
        return
    
    # 运行测试
    results = {}
    
    results["LLM平台测试"] = await test_all_available_llms()
    results["browser-use集成"] = await test_browser_use_integration()
    results["降级机制"] = await test_fallback_mechanism()
    
    # 生成最终报告
    print("\n" + "=" * 60)
    print("📊 综合AI平台测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！AI+RPA系统已准备就绪！")
        print(f"\n🚀 下一步建议:")
        print(f"   1. 开始使用AI+RPA系统执行实际任务")
        print(f"   2. 根据需要选择最适合的AI平台")
        print(f"   3. 监控系统性能和使用情况")
        
        print(f"\n💡 使用示例:")
        print(f"   # 自动选择最佳AI平台")
        print(f"   agent = get_real_browser_use_agent()")
        print(f"   result = await agent.execute_user_request('您的任务')")
        
    elif passed_tests > 0:
        print(f"\n🔄 部分功能可用")
        print(f"💡 建议:")
        print(f"   1. 检查失败的测试项目")
        print(f"   2. 确保网络连接正常")
        print(f"   3. 验证API密钥有效性")
    else:
        print(f"\n❌ 所有测试失败")
        print(f"💡 请检查:")
        print(f"   1. API密钥配置")
        print(f"   2. 网络连接")
        print(f"   3. 依赖安装")


if __name__ == "__main__":
    asyncio.run(main())
