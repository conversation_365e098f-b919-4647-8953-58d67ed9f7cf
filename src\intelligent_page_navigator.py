"""
智能页面导航器

根据用户要求自动调整和打开页面
"""
import asyncio
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from ai_llm_manager import get_llm_manager, LLMProvider
from page_operation_analyzer import PageOperationData, PageLink, get_page_operation_manager

logger = logging.getLogger(__name__)


@dataclass
class NavigationRequest:
    """导航请求"""
    user_requirement: str        # 用户需求描述
    target_keywords: List[str]   # 目标关键词
    priority_categories: List[str]  # 优先功能分类
    max_results: int = 5         # 最大结果数
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "user_requirement": self.user_requirement,
            "target_keywords": self.target_keywords,
            "priority_categories": self.priority_categories,
            "max_results": self.max_results
        }


@dataclass
class NavigationResult:
    """导航结果"""
    matched_links: List[PageLink]  # 匹配的链接
    confidence_scores: List[float]  # 置信度分数
    ai_reasoning: str              # AI推理过程
    recommended_action: str        # 推荐操作
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "matched_links": [link.to_dict() for link in self.matched_links],
            "confidence_scores": self.confidence_scores,
            "ai_reasoning": self.ai_reasoning,
            "recommended_action": self.recommended_action
        }


class IntelligentPageNavigator:
    """智能页面导航器"""
    
    def __init__(self):
        self.llm_manager = get_llm_manager()
        self.operation_manager = get_page_operation_manager()
    
    async def find_target_pages(
        self, 
        operation_data: PageOperationData, 
        user_requirement: str
    ) -> NavigationResult:
        """根据用户要求查找目标页面"""
        try:
            logger.info(f"查找目标页面: {user_requirement}")
            
            # AI分析用户需求
            navigation_request = await self._analyze_user_requirement(user_requirement)
            
            # 匹配相关链接
            matched_links, confidence_scores = await self._match_relevant_links(
                operation_data.links, 
                navigation_request
            )
            
            # AI生成推理和推荐
            ai_reasoning, recommended_action = await self._generate_ai_recommendation(
                user_requirement,
                matched_links,
                operation_data
            )
            
            result = NavigationResult(
                matched_links=matched_links,
                confidence_scores=confidence_scores,
                ai_reasoning=ai_reasoning,
                recommended_action=recommended_action
            )
            
            logger.info(f"找到 {len(matched_links)} 个匹配链接")
            
            return result
            
        except Exception as e:
            logger.error(f"查找目标页面失败: {e}")
            raise
    
    async def navigate_to_target(
        self, 
        page, 
        target_link: PageLink, 
        operation_data: PageOperationData
    ) -> Dict[str, Any]:
        """导航到目标页面"""
        try:
            logger.info(f"导航到目标页面: {target_link.text} ({target_link.href})")
            
            # 检查链接类型并执行相应操作
            if target_link.element_type == "link" and target_link.href:
                # 直接导航到链接
                await page.goto(target_link.href)
                await page.wait_for_load_state('networkidle')
                
            elif target_link.element_type in ["button", "menu"]:
                # 点击按钮或菜单项
                try:
                    element = await page.wait_for_selector(target_link.selector, timeout=10000)
                    if element:
                        await element.click()
                        await page.wait_for_load_state('networkidle')
                    else:
                        # 尝试通过文本查找
                        await page.click(f'text="{target_link.text}"')
                        await page.wait_for_load_state('networkidle')
                except Exception as e:
                    logger.warning(f"点击元素失败，尝试其他方法: {e}")
                    # 尝试通过href导航
                    if target_link.href:
                        await page.goto(target_link.href)
                        await page.wait_for_load_state('networkidle')
            
            # 验证导航结果
            current_url = page.url
            current_title = await page.title()
            
            # 检查是否成功导航
            navigation_success = await self._verify_navigation_success(
                page, target_link, operation_data
            )
            
            return {
                "success": navigation_success,
                "current_url": current_url,
                "current_title": current_title,
                "target_link": target_link.to_dict(),
                "message": f"{'成功' if navigation_success else '失败'}导航到: {target_link.text}"
            }
            
        except Exception as e:
            logger.error(f"导航到目标页面失败: {e}")
            return {
                "success": False,
                "current_url": page.url,
                "current_title": await page.title(),
                "target_link": target_link.to_dict(),
                "message": f"导航失败: {e}"
            }
    
    async def auto_navigate_by_requirement(
        self, 
        page, 
        operation_data: PageOperationData, 
        user_requirement: str
    ) -> Dict[str, Any]:
        """根据用户要求自动导航"""
        try:
            logger.info(f"自动导航: {user_requirement}")
            
            # 查找目标页面
            navigation_result = await self.find_target_pages(operation_data, user_requirement)
            
            if not navigation_result.matched_links:
                return {
                    "success": False,
                    "message": "未找到匹配的页面",
                    "ai_reasoning": navigation_result.ai_reasoning,
                    "available_options": [link.text for link in operation_data.links[:10]]
                }
            
            # 选择最佳匹配
            best_link = navigation_result.matched_links[0]
            best_confidence = navigation_result.confidence_scores[0]
            
            logger.info(f"选择最佳匹配: {best_link.text} (置信度: {best_confidence:.2f})")
            
            # 执行导航
            navigation_result_data = await self.navigate_to_target(page, best_link, operation_data)
            
            # 如果导航成功，分析新页面
            new_operation_data = None
            if navigation_result_data["success"]:
                try:
                    from page_operation_analyzer import get_page_operation_analyzer
                    analyzer = get_page_operation_analyzer()
                    new_operation_data = await analyzer.analyze_page_operations(page)
                    
                    # 保存新页面操作数据
                    await self.operation_manager.save_page_operations(new_operation_data)
                    
                except Exception as e:
                    logger.warning(f"分析新页面失败: {e}")
            
            return {
                "success": navigation_result_data["success"],
                "message": navigation_result_data["message"],
                "current_url": navigation_result_data["current_url"],
                "current_title": navigation_result_data["current_title"],
                "selected_link": best_link.to_dict(),
                "confidence": best_confidence,
                "ai_reasoning": navigation_result.ai_reasoning,
                "recommended_action": navigation_result.recommended_action,
                "new_page_data": new_operation_data.to_dict() if new_operation_data else None,
                "alternative_options": [
                    {
                        "link": link.to_dict(),
                        "confidence": score
                    }
                    for link, score in zip(
                        navigation_result.matched_links[1:4], 
                        navigation_result.confidence_scores[1:4]
                    )
                ]
            }
            
        except Exception as e:
            logger.error(f"自动导航失败: {e}")
            return {
                "success": False,
                "message": f"自动导航失败: {e}",
                "current_url": page.url,
                "current_title": await page.title()
            }
    
    async def _analyze_user_requirement(self, user_requirement: str) -> NavigationRequest:
        """AI分析用户需求"""
        try:
            prompt = f"""
请分析用户的页面导航需求：

用户需求: {user_requirement}

请提供：
1. 目标关键词（用于匹配链接文本，用逗号分隔）
2. 优先功能分类（从以下选择：导航、操作、查询、设置、帮助、其他，用逗号分隔）
3. 最大结果数（1-10）

返回格式：
关键词1,关键词2,关键词3|分类1,分类2|数字

例如：
用户,管理,列表|操作,查询|5
"""
            
            response = await self.llm_manager.generate(prompt, provider=LLMProvider.GEMINI)
            
            # 解析AI响应
            parts = response.content.strip().split('|')
            
            target_keywords = []
            if len(parts) > 0:
                target_keywords = [kw.strip() for kw in parts[0].split(',') if kw.strip()]
            
            priority_categories = []
            if len(parts) > 1:
                priority_categories = [cat.strip() for cat in parts[1].split(',') if cat.strip()]
            
            max_results = 5
            if len(parts) > 2:
                try:
                    max_results = int(parts[2].strip())
                    max_results = max(1, min(10, max_results))
                except ValueError:
                    max_results = 5
            
            return NavigationRequest(
                user_requirement=user_requirement,
                target_keywords=target_keywords,
                priority_categories=priority_categories,
                max_results=max_results
            )
            
        except Exception as e:
            logger.warning(f"AI分析用户需求失败: {e}")
            # 返回默认分析结果
            keywords = re.findall(r'\w+', user_requirement.lower())
            return NavigationRequest(
                user_requirement=user_requirement,
                target_keywords=keywords[:5],
                priority_categories=["操作", "查询"],
                max_results=5
            )
    
    async def _match_relevant_links(
        self, 
        links: List[PageLink], 
        request: NavigationRequest
    ) -> Tuple[List[PageLink], List[float]]:
        """匹配相关链接"""
        try:
            scored_links = []
            
            for link in links:
                score = self._calculate_link_score(link, request)
                if score > 0:
                    scored_links.append((link, score))
            
            # 按分数排序
            scored_links.sort(key=lambda x: x[1], reverse=True)
            
            # 返回前N个结果
            top_links = scored_links[:request.max_results]
            
            matched_links = [link for link, score in top_links]
            confidence_scores = [score for link, score in top_links]
            
            return matched_links, confidence_scores
            
        except Exception as e:
            logger.error(f"匹配相关链接失败: {e}")
            return [], []
    
    def _calculate_link_score(self, link: PageLink, request: NavigationRequest) -> float:
        """计算链接匹配分数"""
        score = 0.0
        
        # 关键词匹配
        link_text_lower = link.text.lower()
        link_desc_lower = link.description.lower()
        
        for keyword in request.target_keywords:
            keyword_lower = keyword.lower()
            if keyword_lower in link_text_lower:
                score += 3.0  # 文本匹配权重高
            elif keyword_lower in link_desc_lower:
                score += 2.0  # 描述匹配权重中等
            elif keyword_lower in link.href.lower():
                score += 1.0  # URL匹配权重低
        
        # 分类匹配
        if link.category in request.priority_categories:
            score += 2.0
        
        # 优先级加权
        score += link.priority * 0.1
        
        # 元素类型加权
        if link.element_type == "link":
            score += 0.5
        elif link.element_type == "menu":
            score += 0.3
        
        return score
    
    async def _generate_ai_recommendation(
        self, 
        user_requirement: str, 
        matched_links: List[PageLink], 
        operation_data: PageOperationData
    ) -> Tuple[str, str]:
        """生成AI推荐"""
        try:
            if not matched_links:
                return "未找到匹配的链接", "请尝试其他关键词或浏览可用选项"
            
            links_info = "\n".join([
                f"- {link.text}: {link.description} (分类: {link.category}, 优先级: {link.priority})"
                for link in matched_links[:3]
            ])
            
            prompt = f"""
用户需求: {user_requirement}

当前页面: {operation_data.title}
页面摘要: {operation_data.ai_summary}

匹配的链接选项:
{links_info}

请提供：
1. 推理过程（为什么选择这些链接）
2. 推荐操作（具体建议用户如何操作）

返回格式：
推理过程|推荐操作
"""
            
            response = await self.llm_manager.generate(prompt, provider=LLMProvider.GEMINI)
            
            parts = response.content.strip().split('|')
            
            ai_reasoning = parts[0] if len(parts) > 0 else "基于关键词匹配选择了最相关的链接"
            recommended_action = parts[1] if len(parts) > 1 else f"建议点击 '{matched_links[0].text}'"
            
            return ai_reasoning, recommended_action
            
        except Exception as e:
            logger.warning(f"生成AI推荐失败: {e}")
            return "AI分析失败", "请手动选择合适的链接"
    
    async def _verify_navigation_success(
        self, 
        page, 
        target_link: PageLink, 
        original_data: PageOperationData
    ) -> bool:
        """验证导航是否成功"""
        try:
            current_url = page.url
            current_title = await page.title()
            
            # 检查URL是否改变
            if current_url != original_data.url:
                return True
            
            # 检查标题是否改变
            if current_title != original_data.title:
                return True
            
            # 检查页面内容是否改变
            try:
                # 等待可能的动态内容加载
                await page.wait_for_timeout(2000)
                
                # 检查是否有新的内容出现
                new_content = await page.evaluate("() => document.body.textContent")
                if len(new_content) > 0:
                    return True
            except Exception:
                pass
            
            return False
            
        except Exception as e:
            logger.warning(f"验证导航成功失败: {e}")
            return False


# 全局实例
global_intelligent_navigator = IntelligentPageNavigator()


def get_intelligent_page_navigator() -> IntelligentPageNavigator:
    """获取智能页面导航器实例"""
    return global_intelligent_navigator
