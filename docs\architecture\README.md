# 系统架构文档

本目录包含项目的系统架构相关文档。

## 文档列表

1. [系统架构设计](design.md)
   - 整体架构
   - 核心模块
   - 技术选型
   - 部署架构
   - 扩展性设计

## 架构概览

### 核心模块
1. 工作流引擎
   - 工作流定义
   - 状态管理
   - 执行引擎
   - 事件系统

2. 操作系统
   - 操作定义
   - 操作执行器
   - 操作工厂
   - 重试机制

3. 存储系统
   - 工作流存储
   - 快照管理
   - 状态持久化
   - 日志记录

4. AI系统
   - 模型服务
   - 特征提取
   - 决策引擎
   - 自修复系统

### 技术栈
- 后端：Python 3.12
- Web框架：FastAPI
- 数据库：PostgreSQL
- 缓存：Redis
- 消息队列：RabbitMQ
- AI框架：PyTorch
- 前端：React + TypeScript
- 自动化：Playwright

## 最新更新 (2025-05-31)

### 已完成
1. 核心架构设计
   - 模块划分
   - 接口定义
   - 数据流设计

2. 工作流引擎架构
   - 状态管理
   - 执行引擎
   - 事件系统

3. 存储系统设计
   - 数据模型
   - 持久化策略
   - 缓存机制

### 进行中
1. AI系统架构
   - 模型服务设计
   - 特征工程
   - 决策系统

2. 性能优化
   - 并发处理
   - 资源管理
   - 负载均衡

### 待开始
1. 监控系统
   - 性能监控
   - 错误追踪
   - 资源监控

## 架构决策记录 (ADR)

所有重要的架构决策都记录在ADR中，包括：
- 决策背景
- 考虑的方案
- 决策理由
- 影响分析
- 实施计划 