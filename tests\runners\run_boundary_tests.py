"""
独立运行边界条件测试
"""
import asyncio
import time
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.absolute()))

# 导入测试模块
from tests.integration.workflow.test_retry_boundary_conditions import (
    MockFailingOperation,
    test_zero_retries,
    test_negative_retries,
    test_large_number_of_retries,
    test_zero_retry_delay,
    test_large_retry_delay,
    test_custom_retry_strategy,
    test_operation_timeout_with_retry,
    test_operation_sequence_with_mixed_results,
)

# 创建测试页面
from playwright.async_api import async_playwright

class TestPage:
    """模拟页面对象"""
    def __init__(self):
        self.context = None
        self.browser = None
        self.playwright = None
        self.page = None
    
    async def setup(self):
        """设置测试环境"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=True)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
        
        # 加载一个简单的测试页面
        await self.page.set_content("""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test Page</title>
            </head>
            <body>
                <button id="test-button">Click Me</button>
                <input id="username" type="text">
            </body>
            </html>
        """)
    
    async def teardown(self):
        """清理测试环境"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

class TestExecutor:
    """模拟操作执行器"""
    def __init__(self, page):
        self.page = page
    
    async def execute_operation(self, operation, **kwargs):
        """执行操作"""
        # 设置默认值
        max_retries = getattr(operation, 'max_retries', 0)
        retry_delay = getattr(operation, 'retry_delay', 0.1)
        retry_on = getattr(operation, 'retry_on', (Exception,))
        timeout = getattr(operation, 'timeout', None)
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                # 设置超时
                if timeout:
                    result = await asyncio.wait_for(
                        operation.execute(**kwargs),
                        timeout=timeout
                    )
                else:
                    result = await operation.execute(**kwargs)
                
                # 如果执行成功，返回结果
                return result
                
            except asyncio.TimeoutError as e:
                last_exception = asyncio.TimeoutError(f"操作 {getattr(operation, 'id', '')} 超时 (超时时间: {timeout}秒)")
                print(f"操作 {getattr(operation, 'id', '')} 超时 (尝试 {attempt + 1}/{max_retries + 1})")
                
            except retry_on as e:
                last_exception = e
                print(f"操作 {getattr(operation, 'id', '')} 失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                
            # 如果不是最后一次尝试，等待重试延迟
            if attempt < max_retries:
                # 应用自定义重试策略（如果存在）
                if hasattr(operation, 'retry_strategy') and callable(operation.retry_strategy):
                    delay = operation.retry_strategy(attempt, retry_delay)
                else:
                    # 默认使用指数退避
                    delay = min(retry_delay * (2 ** attempt), 10)  # 最大延迟10秒
                
                await asyncio.sleep(delay)
        
        # 如果所有重试都失败，抛出异常
        raise Exception(f"操作 {getattr(operation, 'id', '')} 重试 {max_retries} 次后仍然失败: {last_exception}")

async def run_tests():
    """运行所有测试"""
    print("=== 开始运行边界条件测试 ===\n")
    
    # 设置测试环境
    test_page = TestPage()
    await test_page.setup()
    executor = TestExecutor(test_page.page)
    
    # 运行测试
    tests = [
        ("测试重试次数为0", test_zero_retries),
        ("测试负数的重试次数", test_negative_retries),
        ("测试大量重试次数", test_large_number_of_retries),
        ("测试重试延迟为0", test_zero_retry_delay),
        ("测试较大的重试延迟", test_large_retry_delay),
        ("测试自定义重试策略", test_custom_retry_strategy),
        ("测试操作超时与重试的结合", test_operation_timeout_with_retry),
        ("测试混合结果的操作序列", test_operation_sequence_with_mixed_results),
    ]
    
    passed = 0
    failed = 0
    
    for name, test_func in tests:
        try:
            print(f"\n=== 开始测试: {name} ===")
            await test_func(executor, test_page.page)
            print(f"✅ 测试通过: {name}")
            passed += 1
        except Exception as e:
            print(f"❌ 测试失败: {name}")
            print(f"错误信息: {str(e)}")
            import traceback
            traceback.print_exc()
            failed += 1
    
    # 清理测试环境
    await test_page.teardown()
    
    # 输出测试结果
    print("\n=== 测试完成 ===")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"🎯 成功率: {passed / (passed + failed) * 100:.1f}%")
    
    return failed == 0

if __name__ == "__main__":
    success = asyncio.run(run_tests())
    sys.exit(0 if success else 1)
