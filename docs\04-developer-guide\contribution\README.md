# 贡献指南

欢迎为项目做出贡献！本指南将帮助你了解如何参与项目开发。

## 开发流程

### 1. 准备工作

1. Fork 项目仓库
2. 克隆你的 Fork
```bash
git clone https://github.com/YOUR_USERNAME/playwright.git
cd playwright
```

3. 设置上游仓库
```bash
git remote add upstream https://github.com/ORIGINAL_OWNER/playwright.git
```

4. 创建开发环境
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
.\.venv\Scripts\activate   # Windows
pip install -r requirements-dev.txt
```

### 2. 开发流程

1. 创建特性分支
```bash
git checkout -b feature/your-feature-name
```

2. 进行开发
- 遵循代码规范
- 添加单元测试
- 更新文档
- 本地测试

3. 提交变更
```bash
git add .
git commit -m "feat: add new feature"
```

4. 同步上游更改
```bash
git fetch upstream
git rebase upstream/main
```

5. 推送变更
```bash
git push origin feature/your-feature-name
```

6. 创建 Pull Request

### 3. 代码审查

1. 等待代码审查
2. 根据反馈进行修改
3. 更新 Pull Request
4. 合并代码

## 代码规范

### 1. Python 代码规范

- 遵循 PEP 8 规范
- 使用 Type Hints
- 添加 Docstring
- 保持代码简洁

示例：
```python
from typing import Dict, List, Optional

class WorkflowExecutor:
    """工作流执行器类。
    
    负责执行工作流定义，处理执行过程中的错误，
    并生成执行报告。
    
    Attributes:
        workflow_file: 工作流文件路径
        config: 执行器配置
    """
    
    def __init__(
        self,
        workflow_file: str,
        config: Optional[Dict] = None
    ) -> None:
        self.workflow_file = workflow_file
        self.config = config or {}
        
    async def execute(self) -> Dict:
        """执行工作流。
        
        Returns:
            Dict: 执行结果
            
        Raises:
            WorkflowError: 工作流执行错误
        """
        try:
            # 执行逻辑
            pass
        except Exception as e:
            raise WorkflowError(f"执行失败: {str(e)}")
```

### 2. 提交规范

遵循 Angular 提交规范：

```
<type>(<scope>): <subject>

<body>

<footer>
```

类型：
- feat: 新功能
- fix: 修复
- docs: 文档
- style: 格式
- refactor: 重构
- test: 测试
- chore: 构建

示例：
```
feat(recorder): 添加设备模拟支持

- 添加移动设备模拟
- 支持自定义视口大小
- 添加用户代理设置

Closes #123
```

### 3. 文档规范

- 使用 Markdown 格式
- 保持结构清晰
- 提供代码示例
- 更新 API 文档

示例：
```markdown
# 设备模拟

本节介绍如何使用设备模拟功能。

## 基本用法

```python
from src.recorder import CodegenWrapper

recorder = CodegenWrapper("workflow.json")
recorder.start_recording(
    url="https://example.com",
    device="iPhone 13"
)
```

## 配置选项

| 选项 | 类型 | 说明 |
|------|------|------|
| device | str | 设备名称 |
| viewport | tuple | 视口大小 |
| user_agent | str | 用户代理 |
```

## 测试规范

### 1. 单元测试

使用 pytest 编写测试：

```python
import pytest
from src.recorder import CodegenWrapper

def test_recorder_initialization():
    """测试录制器初始化"""
    recorder = CodegenWrapper("test.json")
    assert recorder.output_file == "test.json"
    
@pytest.mark.asyncio
async def test_recording_start():
    """测试录制启动"""
    recorder = CodegenWrapper("test.json")
    await recorder.start_recording("https://example.com")
    assert recorder.is_recording
    
def test_invalid_config():
    """测试无效配置"""
    with pytest.raises(ValueError):
        CodegenWrapper(None)
```

### 2. 集成测试

```python
import pytest
from src.executor import WorkflowExecutor
from src.monitor import BrowserMonitor

@pytest.mark.integration
async def test_workflow_execution():
    """测试完整工作流执行"""
    executor = WorkflowExecutor("test_workflow.json")
    monitor = BrowserMonitor()
    
    await monitor.start()
    result = await executor.execute()
    await monitor.stop()
    
    assert result["status"] == "success"
```

### 3. 性能测试

```python
import pytest
from src.monitor import BrowserMonitor

@pytest.mark.performance
async def test_monitor_performance():
    """测试监控性能"""
    monitor = BrowserMonitor()
    
    start_time = time.time()
    await monitor.collect_metrics()
    end_time = time.time()
    
    assert end_time - start_time < 1.0
```

## 发布流程

### 1. 版本管理

使用语义化版本：

- 主版本号：不兼容的 API 修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 2. 更新日志

在 `CHANGELOG.md` 中记录变更：

```markdown
# 更新日志

## [1.1.0] - 2024-03-19

### 新增
- 设备模拟支持
- 性能监控功能
- AI 优化建议

### 修复
- 修复选择器不稳定问题
- 修复内存泄漏问题

### 变更
- 优化录制性能
- 更新依赖版本
```

### 3. 发布检查清单

1. 更新版本号
2. 更新更新日志
3. 更新文档
4. 运行测试套件
5. 构建分发包
6. 创建发布标签
7. 发布到 PyPI

## 问题报告

### 1. Bug 报告

提供完整的信息：

```markdown
## 问题描述

录制时浏览器无法启动

## 复现步骤

1. 安装最新版本
2. 运行录制命令
3. 观察错误信息

## 期望行为

浏览器正常启动并开始录制

## 实际行为

报错：无法启动浏览器

## 环境信息

- OS: Windows 11
- Python: 3.8.10
- Playwright: 1.32.0
```

### 2. 功能建议

描述新功能：

```markdown
## 功能描述

添加录制回放功能

## 使用场景

- 调试录制过程
- 验证工作流
- 培训演示

## 实现建议

1. 添加回放控制
2. 支持步骤暂停
3. 提供速度调节
```

## 社区参与

### 1. 讨论参与

- 遵守行为准则
- 保持友善态度
- 提供建设性意见
- 帮助其他成员

### 2. 文档贡献

- 修复文档错误
- 添加使用示例
- 改进文档结构
- 翻译文档

### 3. 代码贡献

- 修复已知问题
- 添加新功能
- 改进性能
- 优化代码

## 下一步

- 查看[问题列表](../../issues)寻找可以参与的任务
- 查看[项目规划](../../projects)了解开发路线
- 加入[讨论组](../../discussions)参与社区讨论 