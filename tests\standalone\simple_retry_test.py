"""
简单的重试机制测试
"""
import asyncio
import time
import uuid
from typing import Optional, Type, Tuple, List, Dict, Any, Callable

class MockFailingOperation:
    """模拟会失败的操作"""
    def __init__(self, 
                 fail_times: int = 0, 
                 success_after: Optional[int] = None, 
                 exception: Exception = Exception("Test error"),
                 operation_id: Optional[str] = None,
                 timeout: Optional[float] = None,
                 max_retries: int = 3,
                 retry_delay: float = 0.1):
        self.fail_times = fail_times
        self.success_after = success_after
        self.exception = exception
        self.attempts = 0
        self.id = operation_id or f"mock_operation_{str(uuid.uuid4())[:8]}"
        self.type = "mock"
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.retry_on = (Exception,)
        self.timeout = timeout
        self.continue_on_failure = False
    
    async def execute(self, *args, **kwargs):
        """执行操作"""
        self.attempts += 1
        print(f"执行操作 {self.id}, 尝试次数: {self.attempts}")
        
        # 模拟超时
        if self.timeout and self.attempts == 1:
            print(f"操作 {self.id} 模拟超时...")
            await asyncio.sleep(self.timeout * 1.5)
            
        # 模拟在指定次数后成功
        if self.success_after is not None and self.attempts > self.success_after:
            print(f"操作 {self.id} 第 {self.attempts} 次尝试成功")
            return "success"
            
        # 模拟失败
        if self.attempts <= self.fail_times:
            print(f"操作 {self.id} 第 {self.attempts} 次尝试失败: {self.exception}")
            raise self.exception
            
        print(f"操作 {self.id} 第 {self.attempts} 次尝试成功")
        return "success"

class OperationError(Exception):
    """操作错误"""
    pass

class OperationExecutor:
    """操作执行器"""
    def __init__(self, default_timeout: float = 30.0):
        self.default_timeout = default_timeout
        self.retry_attempts = {}
    
    async def execute_operation(self, operation, **kwargs):
        """执行操作，支持重试和超时"""
        operation_id = getattr(operation, 'id', str(id(operation)))
        self.retry_attempts[operation_id] = 0
        
        # 获取重试参数
        max_retries = getattr(operation, 'max_retries', 0)
        retry_delay = getattr(operation, 'retry_delay', 0.1)
        retry_on = getattr(operation, 'retry_on', (Exception,))
        timeout = getattr(operation, 'timeout', self.default_timeout)
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            self.retry_attempts[operation_id] = attempt
            
            try:
                # 设置超时
                if timeout:
                    result = await asyncio.wait_for(
                        operation.execute(**kwargs),
                        timeout=timeout
                    )
                else:
                    result = await operation.execute(**kwargs)
                    
                # 如果执行成功，返回结果
                return result
                
            except asyncio.TimeoutError as e:
                last_exception = asyncio.TimeoutError(f"操作 {operation_id} 超时 (超时时间: {timeout}秒)")
                print(f"操作 {operation_id} 超时 (尝试 {attempt + 1}/{max_retries + 1})")
                
            except retry_on as e:
                last_exception = e
                print(f"操作 {operation_id} 失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                
            # 如果不是最后一次尝试，等待重试延迟
            if attempt < max_retries:
                await asyncio.sleep(retry_delay)
        
        # 如果所有重试都失败，抛出异常
        raise OperationError(f"操作 {operation_id} 重试 {max_retries} 次后仍然失败: {last_exception}")

async def test_retry_mechanism():
    """测试重试机制"""
    print("\n=== 测试重试机制 ===")
    executor = OperationExecutor(default_timeout=2.0)
    
    # 创建一个会失败2次然后成功的操作
    operation = MockFailingOperation(fail_times=2, max_retries=3, retry_delay=0.1)
    
    try:
        # 执行操作
        result = await executor.execute_operation(operation)
        
        # 验证结果
        assert result == "success"
        assert operation.attempts == 3  # 初始尝试 + 2次重试
        print("✅ 测试通过：重试机制")
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        raise

async def test_zero_retries():
    """测试重试次数为0的情况"""
    print("\n=== 测试重试次数为0 ===")
    executor = OperationExecutor()
    
    # 创建一个会失败1次的操作，但重试次数为0
    operation = MockFailingOperation(fail_times=1, max_retries=0)
    
    try:
        # 执行操作，应该抛出异常
        try:
            await executor.execute_operation(operation)
            print("❌ 测试失败：预期抛出 OperationError 异常")
            return
        except OperationError as e:
            # 验证只尝试了1次
            assert operation.attempts == 1
            assert "重试 0 次后仍然失败" in str(e)
            print("✅ 测试通过：重试次数为0")
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        raise

async def test_operation_timeout():
    """测试操作超时"""
    print("\n=== 测试操作超时 ===")
    executor = OperationExecutor(default_timeout=1.0)
    
    # 创建一个会超时的操作
    operation = MockFailingOperation(timeout=0.5, max_retries=0)
    
    try:
        # 执行操作，应该抛出超时异常
        try:
            await executor.execute_operation(operation)
            print("❌ 测试失败：预期抛出 OperationError 异常")
            return
        except OperationError as e:
            # 验证错误信息
            assert "超时" in str(e)
            print("✅ 测试通过：操作超时")
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        raise

async def run_tests():
    """运行所有测试"""
    print("=== 开始运行测试 ===\n")
    
    tests = [
        test_retry_mechanism,
        test_zero_retries,
        test_operation_timeout,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        test_name = test.__name__
        try:
            print(f"\n=== 开始测试: {test_name} ===")
            await test()
            print(f"✅ 测试通过: {test_name}")
            passed += 1
        except Exception as e:
            print(f"❌ 测试失败: {test_name}")
            print(f"错误信息: {str(e)}")
            import traceback
            traceback.print_exc()
            failed += 1
    
    # 输出测试结果
    print("\n=== 测试完成 ===")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"🎯 成功率: {passed / (passed + failed) * 100:.1f}%")
    
    return failed == 0

if __name__ == "__main__":
    success = asyncio.run(run_tests())
    exit(0 if success else 1)
