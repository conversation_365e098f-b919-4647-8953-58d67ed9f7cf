"""
重试机制的边界条件测试

测试操作执行器中重试和超时功能的边界条件和异常情况。
"""
import asyncio
import time
import pytest
from pathlib import Path
from unittest.mock import patch, MagicMock, AsyncMock

from playwright.async_api import async_playwright

# 添加项目根目录到Python路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.workflow.operations.executor import OperationExecutor
from src.workflow.operations.operations import (
    ClickOperation, FillOperation, WaitOperation, ExtractOperation, NavigateOperation
)
from src.workflow.operations.base import ElementSelector
from src.workflow.operations.exceptions import OperationError, OperationTimeoutError

# 测试页面路径
TEST_HTML = Path(__file__).parent.parent.parent / "test_data" / "test_page.html"

class MockFailingOperation:
    """模拟会失败的操作"""
    def __init__(self, fail_times=0, success_after=None, exception=Exception("Test error"), 
                 id=None, timeout=None, max_retries=3, retry_delay=0.1, 
                 retry_on=None, retry_strategy=None):
        self.fail_times = fail_times
        self.success_after = success_after
        self.exception = exception
        self.attempts = 0
        self.id = id or f"mock_operation_{id(self)}"
        self.type = "mock"
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.retry_on = retry_on or (Exception,)
        self.timeout = timeout
        self.retry_strategy = retry_strategy
        self.continue_on_failure = False
        self.parameters = {}
        self.wait_conditions = []
        self.metadata = {}
    
    async def execute(self, *args, **kwargs):
        self.attempts += 1
        if self.timeout and self.attempts == 1:
            await asyncio.sleep(self.timeout * 1.5)  # 确保超时
        if self.success_after is not None and self.attempts > self.success_after:
            return "success"
        if self.attempts <= self.fail_times:
            raise self.exception
        return "success"
    
    def to_dict(self):
        """转换为字典，用于序列化"""
        return {
            "id": self.id,
            "type": self.type,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "timeout": self.timeout,
            "continue_on_failure": self.continue_on_failure
        }

@pytest.fixture
def event_loop():
    """为测试创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def browser():
    """启动Playwright浏览器"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        yield browser
        await browser.close()

@pytest.fixture
async def page(browser):
    """创建测试页面"""
    page = await browser.new_page()
    test_html = str(TEST_HTML.absolute())
    await page.goto(f"file://{test_html}")
    return page

@pytest.fixture
def executor(page):
    """创建操作执行器"""
    return OperationExecutor(page)

async def test_zero_retries(executor, page):
    """测试重试次数为0的情况"""
    # 创建一个会失败1次的操作，但重试次数为0
    operation = MockFailingOperation(fail_times=1, max_retries=0)
    
    # 执行操作，应该抛出异常
    with pytest.raises(OperationError) as exc_info:
        await executor.execute_operation(operation)
    
    # 验证只尝试了1次
    assert operation.attempts == 1
    assert "Test error" in str(exc_info.value)

async def test_negative_retries(executor, page):
    """测试负数的重试次数"""
    # 创建一个会失败1次的操作，但重试次数为-1（应该被当作0）
    operation = MockFailingOperation(fail_times=1, max_retries=-1)
    
    # 执行操作，应该抛出异常
    with pytest.raises(OperationError) as exc_info:
        await executor.execute_operation(operation)
    
    # 验证只尝试了1次
    assert operation.attempts == 1
    assert "Test error" in str(exc_info.value)

async def test_large_number_of_retries(executor, page):
    """测试大量重试次数"""
    # 创建一个会失败5次的操作，设置重试次数为10
    operation = MockFailingOperation(fail_times=5, max_retries=10)
    
    # 执行操作，应该成功
    result = await executor.execute_operation(operation)
    
    # 验证尝试了6次（初始+5次重试）
    assert operation.attempts == 6
    assert result == "success"

async def test_zero_retry_delay(executor, page):
    """测试重试延迟为0的情况"""
    # 创建一个会失败2次的操作，重试延迟为0
    operation = MockFailingOperation(fail_times=2, max_retries=3, retry_delay=0)
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行操作
    result = await executor.execute_operation(operation)
    
    # 验证执行时间应该很短（远小于重试延迟）
    execution_time = time.time() - start_time
    assert execution_time < 0.1  # 应该很快完成
    
    # 验证结果
    assert operation.attempts == 3  # 初始+2次重试
    assert result == "success"

async def test_large_retry_delay(executor, page):
    """测试较大的重试延迟"""
    # 创建一个会失败1次的操作，重试延迟为1秒
    operation = MockFailingOperation(fail_times=1, max_retries=1, retry_delay=1.0)
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行操作
    result = await executor.execute_operation(operation)
    
    # 验证执行时间应该大约为1秒（考虑误差）
    execution_time = time.time() - start_time
    assert 0.9 <= execution_time <= 1.5  # 考虑系统负载
    
    # 验证结果
    assert operation.attempts == 2  # 初始+1次重试
    assert result == "success"

async def test_custom_retry_strategy(executor, page):
    """测试自定义重试策略"""
    # 创建一个自定义重试策略（固定延迟）
    def custom_strategy(attempt, delay):
        return 0.5  # 固定0.5秒延迟
    
    # 创建一个会失败2次的操作，使用自定义重试策略
    operation = MockFailingOperation(
        fail_times=2, 
        max_retries=3, 
        retry_delay=0.1,  # 这个值会被自定义策略覆盖
        retry_strategy=custom_strategy
    )
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行操作
    result = await executor.execute_operation(operation)
    
    # 验证执行时间应该大约为1秒（2次重试 * 0.5秒）
    execution_time = time.time() - start_time
    assert 0.9 <= execution_time <= 1.5  # 考虑系统负载
    
    # 验证结果
    assert operation.attempts == 3  # 初始+2次重试
    assert result == "success"

async def test_operation_timeout_with_retry(executor, page):
    """测试操作超时与重试的结合"""
    # 创建一个会超时1次然后成功的操作
    operation = MockFailingOperation(
        success_after=1,  # 第二次成功
        timeout=0.5,      # 0.5秒超时
        max_retries=3,    # 最多重试3次
        retry_delay=0.1   # 重试延迟0.1秒
    )
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行操作
    result = await executor.execute_operation(operation)
    
    # 验证执行时间应该大约为0.5秒（超时） + 0.1秒（重试延迟）
    execution_time = time.time() - start_time
    assert 0.5 <= execution_time <= 1.0  # 考虑系统负载
    
    # 验证结果
    assert operation.attempts == 2  # 初始(超时) + 1次重试(成功)
    assert result == "success"

async def test_operation_sequence_with_mixed_results(executor, page):
    """测试混合结果的操作序列"""
    # 创建多个操作，具有不同的重试行为和结果
    operations = [
        # 操作1: 第一次失败，重试后成功
        MockFailingOperation(
            id="op1", 
            fail_times=1, 
            max_retries=2, 
            retry_delay=0.1
        ),
        # 操作2: 总是成功
        MockFailingOperation(
            id="op2", 
            fail_times=0, 
            max_retries=2, 
            retry_delay=0.1
        ),
        # 操作3: 总是失败
        MockFailingOperation(
            id="op3", 
            fail_times=10,  # 总是失败
            max_retries=1,  # 只重试1次
            retry_delay=0.1
        )
    ]
    
    # 执行操作序列
    results = []
    for op in operations:
        if op.id == "op3":
            # 操作3应该会失败
            with pytest.raises(OperationError):
                await executor.execute_operation(op)
            results.append((op.id, "failed"))
        else:
            result = await executor.execute_operation(op)
            results.append((op.id, result))
    
    # 验证结果
    assert len(results) == 3
    assert results[0] == ("op1", "success")
    assert results[1] == ("op2", "success")
    assert results[2][0] == "op3"
    assert results[2][1] == "failed"
    
    # 验证尝试次数
    assert operations[0].attempts == 2  # 初始+1次重试
    assert operations[1].attempts == 1  # 一次成功
    assert operations[2].attempts == 2  # 初始+1次重试
