"""
测试运行脚本
"""
import unittest
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

if __name__ == '__main__':
    # 加载测试
    test_suite = unittest.defaultTestLoader.discover('tests', pattern='test_operations.py')
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print(f"\n测试结果: {result}")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败测试数: {len(result.failures)}")
    print(f"错误测试数: {len(result.errors)}")
    
    # 如果有测试失败或错误，输出详细信息
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"\n{test.id()}")
            print("-" * 80)
            print(traceback)
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"\n{test.id()}")
            print("-" * 80)
            print(traceback)
    
    # 如果有测试失败或错误，返回非零退出码
    if result.failures or result.errors:
        exit(1)
