"""
简单演示测试
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def test_imports():
    """测试导入"""
    print("测试导入...")
    
    try:
        from src.workflow.variables import VariableContext, VariableResolver
        print("✅ 变量系统导入成功")
    except Exception as e:
        print(f"❌ 变量系统导入失败: {e}")
        return False
    
    try:
        from src.workflow.dsl import WorkflowDSL
        print("✅ DSL解析器导入成功")
    except Exception as e:
        print(f"❌ DSL解析器导入失败: {e}")
        return False
    
    try:
        from src.workflow.operations import OperationExecutor, ClickOperation
        print("✅ 操作系统导入成功")
    except Exception as e:
        print(f"❌ 操作系统导入失败: {e}")
        return False
    
    return True

def test_variable_system():
    """测试变量系统"""
    print("\n测试变量系统...")
    
    try:
        from src.workflow.variables import VariableContext, VariableResolver
        
        # 创建变量上下文
        context = VariableContext({"test": "value"})
        
        # 测试基本操作
        context.set("name", "测试")
        assert context.get("name") == "测试"
        assert context.has("name") == True
        
        # 测试变量解析
        resolver = VariableResolver(context)
        result = resolver.resolve("Hello {{ name }}!")
        assert result == "Hello 测试!"
        
        print("✅ 变量系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 变量系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dsl_system():
    """测试DSL系统"""
    print("\n测试DSL系统...")
    
    try:
        from src.workflow.dsl import WorkflowDSL
        
        # 创建DSL解析器
        dsl = WorkflowDSL()
        
        # 测试解析简单工作流
        workflow_data = {
            "name": "测试工作流",
            "steps": [
                {
                    "id": "step1",
                    "type": "playwright",
                    "name": "测试步骤",
                    "action": "click",
                    "selector": "#btn"
                }
            ]
        }
        
        workflow = dsl.parse_dict(workflow_data)
        assert workflow.name == "测试工作流"
        assert len(workflow.steps) == 1
        assert workflow.steps[0].type == "playwright"
        
        print("✅ DSL系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ DSL系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("Playwright 测试用例录制与回放系统 - 简单演示")
    print("=" * 50)
    
    success = True
    
    # 测试导入
    if not test_imports():
        success = False
    
    # 测试变量系统
    if not test_variable_system():
        success = False
    
    # 测试DSL系统
    if not test_dsl_system():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 所有测试通过！系统运行正常。")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
    
    return success

if __name__ == "__main__":
    main()
