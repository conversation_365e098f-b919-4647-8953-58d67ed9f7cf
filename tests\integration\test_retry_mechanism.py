"""
测试重试机制的集成测试
"""
import pytest
import asyncio
from pathlib import Path
from playwright.async_api import async_playwright
from src.workflow.operations.executor import OperationExecutor
from src.workflow.operations.operations import ClickOperation
from src.workflow.operations.base import ElementSelector

# 测试页面路径
TEST_HTML = Path(__file__).parent.parent / "test_data" / "test_page.html"

class MockFailingOperation:
    """模拟会失败的操作"""
    def __init__(self, fail_times=0):
        self.fail_times = fail_times
        self.attempts = 0
        self.id = "mock_operation"
        self.type = "mock"
    
    async def execute(self, *args, **kwargs):
        self.attempts += 1
        if self.attempts <= self.fail_times:
            raise Exception(f"Mock error on attempt {self.attempts}")
        return "success"

@pytest.fixture(scope="module")
def event_loop():
    """为测试创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="module")
async def browser():
    """启动Playwright浏览器"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        yield browser
        await browser.close()

@pytest.fixture(scope="module")
async def page(browser):
    """创建测试页面"""
    page = await browser.new_page()
    await page.goto(f"file://{TEST_HTML.absolute()}")
    yield page
    await page.close()

@pytest.fixture
def executor(page):
    """创建操作执行器"""
    return OperationExecutor(page)

@pytest.mark.asyncio
async def test_retry_mechanism(executor, page):
    """测试重试机制"""
    # 创建一个会失败2次然后成功的操作
    operation = MockFailingOperation(fail_times=2)
    
    # 设置重试配置
    operation.max_retries = 3
    operation.retry_delay = 0.1
    
    # 执行操作
    result = await executor._execute_operation(operation)
    
    # 验证结果
    assert result == "success"
    assert operation.attempts == 3  # 1次初始尝试 + 2次重试

@pytest.mark.asyncio
async def test_retry_exhausted(executor, page):
    """测试重试次数用尽的情况"""
    # 创建一个总是失败的操作
    operation = MockFailingOperation(fail_times=100)
    
    # 设置重试配置
    operation.max_retries = 2
    operation.retry_delay = 0.1
    
    # 执行操作，预期会抛出异常
    with pytest.raises(Exception) as exc_info:
        await executor._execute_operation(operation)
    
    # 验证重试次数
    assert operation.attempts == 3  # 1次初始尝试 + 2次重试
    assert "Mock error on attempt 3" in str(exc_info.value)

@pytest.mark.asyncio
async def test_click_operation_with_retry(executor, page):
    """测试带重试的点击操作"""
    # 创建一个点击操作
    selector = ElementSelector(css="#test-button")
    operation = ClickOperation(element=selector)
    
    # 设置重试配置
    operation.max_retries = 2
    operation.retry_delay = 0.1
    
    # 执行操作
    result = await executor._execute_operation(operation)
    
    # 验证结果
    assert result is True  # ClickOperation 执行成功应返回 True
    
    # 验证按钮是否被点击（可以通过添加事件监听器来验证）
    clicked = await page.evaluate('''() => {
        return window.buttonClicked || false;
    }''')
    
    assert clicked is True, "按钮点击事件未触发"
