"""
简单工作流引擎

为M5里程碑验收测试提供基础的工作流执行功能
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class WorkflowEngine:
    """简单工作流引擎"""
    
    def __init__(self, workflow_definition: Dict[str, Any]):
        """初始化工作流引擎
        
        Args:
            workflow_definition: 工作流定义字典
        """
        self.workflow_definition = workflow_definition
        self.name = workflow_definition.get("name", "未命名工作流")
        self.version = workflow_definition.get("version", "1.0.0")
        self.steps = workflow_definition.get("steps", [])
        self.variables = workflow_definition.get("variables", {})
        
        # 执行状态
        self.current_step = 0
        self.execution_id = f"exec_{int(datetime.now().timestamp())}"
        self.start_time = None
        self.end_time = None
        self.status = "pending"
        self.results = []
        
    async def execute(self) -> Dict[str, Any]:
        """执行工作流
        
        Returns:
            Dict[str, Any]: 执行结果
        """
        logger.info(f"开始执行工作流: {self.name}")
        
        self.start_time = datetime.now()
        self.status = "running"
        
        try:
            # 执行所有步骤
            for i, step in enumerate(self.steps):
                self.current_step = i
                logger.info(f"执行步骤 {i+1}/{len(self.steps)}: {step.get('description', step.get('type', '未知步骤'))}")
                
                step_result = await self._execute_step(step)
                self.results.append(step_result)
                
                if not step_result.get("success", False):
                    logger.error(f"步骤 {i+1} 执行失败: {step_result.get('error', '未知错误')}")
                    self.status = "failed"
                    break
            
            if self.status == "running":
                self.status = "completed"
                logger.info(f"工作流执行完成: {self.name}")
            
        except Exception as e:
            logger.error(f"工作流执行异常: {e}")
            self.status = "failed"
            self.results.append({
                "success": False,
                "error": str(e),
                "step_type": "exception"
            })
        
        finally:
            self.end_time = datetime.now()
        
        return self._get_execution_result()
    
    async def _execute_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个步骤
        
        Args:
            step: 步骤定义
            
        Returns:
            Dict[str, Any]: 步骤执行结果
        """
        step_type = step.get("type", "unknown")
        step_id = step.get("id", f"step_{self.current_step}")
        
        try:
            if step_type == "log":
                return await self._execute_log_step(step)
            elif step_type == "delay":
                return await self._execute_delay_step(step)
            elif step_type == "variable":
                return await self._execute_variable_step(step)
            elif step_type == "condition":
                return await self._execute_condition_step(step)
            else:
                # 默认处理：记录步骤信息
                message = step.get("metadata", {}).get("message", f"执行步骤: {step_type}")
                logger.info(message)
                
                return {
                    "success": True,
                    "step_id": step_id,
                    "step_type": step_type,
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"步骤执行异常: {e}")
            return {
                "success": False,
                "step_id": step_id,
                "step_type": step_type,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _execute_log_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行日志步骤"""
        message = step.get("metadata", {}).get("message", "日志记录")
        logger.info(f"[工作流日志] {message}")
        
        return {
            "success": True,
            "step_id": step.get("id"),
            "step_type": "log",
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _execute_delay_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行延迟步骤"""
        delay_seconds = step.get("metadata", {}).get("seconds", 1)
        
        logger.info(f"延迟 {delay_seconds} 秒")
        await asyncio.sleep(delay_seconds)
        
        return {
            "success": True,
            "step_id": step.get("id"),
            "step_type": "delay",
            "delay_seconds": delay_seconds,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _execute_variable_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行变量操作步骤"""
        metadata = step.get("metadata", {})
        var_name = metadata.get("name")
        var_value = metadata.get("value")
        
        if var_name:
            self.variables[var_name] = var_value
            logger.info(f"设置变量: {var_name} = {var_value}")
        
        return {
            "success": True,
            "step_id": step.get("id"),
            "step_type": "variable",
            "variable_name": var_name,
            "variable_value": var_value,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _execute_condition_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行条件步骤"""
        metadata = step.get("metadata", {})
        condition = metadata.get("condition", True)
        
        # 简单的条件评估
        if isinstance(condition, bool):
            result = condition
        elif isinstance(condition, str):
            # 简单的字符串条件评估
            result = condition.lower() in ["true", "yes", "1"]
        else:
            result = bool(condition)
        
        logger.info(f"条件评估: {condition} -> {result}")
        
        return {
            "success": True,
            "step_id": step.get("id"),
            "step_type": "condition",
            "condition": condition,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_execution_result(self) -> Dict[str, Any]:
        """获取执行结果"""
        execution_time = 0
        if self.start_time and self.end_time:
            execution_time = (self.end_time - self.start_time).total_seconds()
        
        success = self.status == "completed"
        
        return {
            "success": success,
            "execution_id": self.execution_id,
            "workflow_name": self.name,
            "workflow_version": self.version,
            "status": self.status,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "execution_time": execution_time,
            "total_steps": len(self.steps),
            "completed_steps": self.current_step + (1 if success else 0),
            "step_results": self.results,
            "variables": self.variables,
            "message": f"工作流执行{'成功' if success else '失败'}"
        }
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        return {
            "execution_id": self.execution_id,
            "status": self.status,
            "current_step": self.current_step,
            "total_steps": len(self.steps),
            "progress": (self.current_step / len(self.steps)) * 100 if self.steps else 0
        }


# 为了兼容性，创建一个别名
SimpleWorkflowEngine = WorkflowEngine


async def test_workflow_engine():
    """测试工作流引擎"""
    print("🧪 测试简单工作流引擎")
    
    # 创建测试工作流
    test_workflow = {
        "name": "测试工作流",
        "version": "1.0.0",
        "steps": [
            {
                "id": "step_1",
                "type": "log",
                "description": "记录开始日志",
                "metadata": {
                    "message": "工作流开始执行"
                }
            },
            {
                "id": "step_2",
                "type": "variable",
                "description": "设置变量",
                "metadata": {
                    "name": "test_var",
                    "value": "test_value"
                }
            },
            {
                "id": "step_3",
                "type": "condition",
                "description": "条件判断",
                "metadata": {
                    "condition": True
                }
            },
            {
                "id": "step_4",
                "type": "log",
                "description": "记录结束日志",
                "metadata": {
                    "message": "工作流执行完成"
                }
            }
        ],
        "variables": {
            "initial_var": "initial_value"
        }
    }
    
    # 创建并执行工作流
    engine = WorkflowEngine(test_workflow)
    result = await engine.execute()
    
    print(f"执行结果: {'成功' if result['success'] else '失败'}")
    print(f"执行时间: {result['execution_time']:.2f}秒")
    print(f"完成步骤: {result['completed_steps']}/{result['total_steps']}")
    
    return result


if __name__ == "__main__":
    asyncio.run(test_workflow_engine())
