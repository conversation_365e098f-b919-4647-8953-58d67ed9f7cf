"""
前端HTTP服务器启动器

使用Python内置HTTP服务器提供前端文件
"""
import http.server
import socketserver
import webbrowser
import threading
import time
import os
from pathlib import Path


def start_http_server(directory, port=3000):
    """启动HTTP服务器"""
    try:
        # 切换到前端目录
        os.chdir(directory)
        
        # 创建HTTP服务器
        handler = http.server.SimpleHTTPRequestHandler
        
        # 添加CORS支持
        class CORSRequestHandler(handler):
            def end_headers(self):
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                super().end_headers()
        
        with socketserver.TCPServer(("", port), CORSRequestHandler) as httpd:
            print(f"✅ 前端服务器启动成功!")
            print(f"🌐 地址: http://localhost:{port}")
            print(f"📁 目录: {directory}")
            print(f"🔗 后端API: http://localhost:8000")
            print("\n按 Ctrl+C 停止服务器")
            
            # 自动打开浏览器
            def open_browser():
                time.sleep(2)
                webbrowser.open(f'http://localhost:{port}')
            
            threading.Thread(target=open_browser, daemon=True).start()
            
            # 启动服务器
            httpd.serve_forever()
            
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ 端口 {port} 已被占用")
            print(f"💡 尝试使用其他端口...")
            start_http_server(directory, port + 1)
        else:
            print(f"❌ 启动服务器失败: {e}")
    except KeyboardInterrupt:
        print("\n👋 前端服务器已停止")


def main():
    """主函数"""
    print("🎨 AI+RPA 前端服务器启动器")
    print("=" * 40)
    
    # 检查前端目录
    frontend_simple = Path("frontend_simple")
    frontend_react = Path("frontend")
    
    if frontend_simple.exists():
        print("📁 使用简化版前端 (frontend_simple)")
        print("💡 这个版本不需要npm，直接可用")
        start_http_server(frontend_simple, 3000)
    elif frontend_react.exists():
        print("📁 使用React前端 (frontend)")
        print("⚠️ 注意: 这需要先运行 npm install")
        start_http_server(frontend_react, 3000)
    else:
        print("❌ 未找到前端目录")
        print("💡 请确保 frontend_simple 或 frontend 目录存在")


if __name__ == "__main__":
    main()
