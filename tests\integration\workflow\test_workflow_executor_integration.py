"""
工作流执行器集成测试

测试工作流执行器与Playwright的集成
"""
import asyncio
import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

import pytest
import yaml
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from workflow.dsl.parser import DSLParser
from workflow.executor import WorkflowExecutor, WorkflowContext, NodeStatus
from workflow.core.operation import OperationExecutor, OperationType, OperationStatus

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 测试数据目录
TEST_DATA_DIR = Path(__file__).parent.parent.parent / "test_data"


@pytest.fixture(scope="module")
async def browser():
    """启动浏览器实例"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        yield browser
        await browser.close()


@pytest.fixture
async def page(browser):
    """创建新页面"""
    context = await browser.new_context()
    page = await context.new_page()
    yield page
    await context.close()


@pytest.fixture
async def operation_executor(page):
    """创建操作执行器"""
    return OperationExecutor(page=page)


@pytest.fixture
async def workflow_executor(operation_executor):
    """创建工作流执行器"""
    return WorkflowExecutor(operation_executor=operation_executor)


async def load_workflow(workflow_name: str) -> Dict[str, Any]:
    """加载工作流定义"""
    workflow_path = TEST_DATA_DIR / f"{workflow_name}.yaml"
    if not workflow_path.exists():
        pytest.skip(f"Test workflow not found: {workflow_path}")
    
    with open(workflow_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


async def test_simple_navigation_workflow(workflow_executor, page):
    """测试简单的导航工作流"""
    # 加载工作流定义
    workflow_def = await load_workflow("simple_navigation")
    
    # 解析工作流
    parser = DSLParser()
    workflow = parser.parse_dict(workflow_def)
    
    # 执行工作流
    result = await workflow_executor.execute(workflow)
    
    # 验证结果
    assert result.success
    assert result.execution_time > 0
    
    # 验证页面导航
    assert "Navigated to example.com" in result.context.logs
    assert page.url == "https://example.com/"


async def test_form_submission_workflow(workflow_executor, page):
    """测试表单提交工作流"""
    # 加载工作流定义
    workflow_def = await load_workflow("form_submission")
    
    # 解析工作流
    parser = DSLParser()
    workflow = parser.parse_dict(workflow_def)
    
    # 执行工作流
    result = await workflow_executor.execute(workflow)
    
    # 验证结果
    assert result.success
    
    # 验证表单提交
    assert "Form submitted successfully" in result.context.logs
    assert "testuser" in result.context.variables.get("username", "")


async def test_conditional_workflow(workflow_executor, page):
    """测试条件分支工作流"""
    # 加载工作流定义
    workflow_def = await load_workflow("conditional_workflow")
    
    # 解析工作流
    parser = DSLParser()
    workflow = parser.parse_dict(workflow_def)
    
    # 执行工作流
    result = await workflow_executor.execute(workflow)
    
    # 验证结果
    assert result.success
    
    # 验证条件分支执行情况
    assert "Condition evaluated to true" in result.context.logs or "Condition evaluated to false" in result.context.logs


async def test_loop_workflow(workflow_executor, page):
    """测试循环工作流"""
    # 加载工作流定义
    workflow_def = await load_workflow("loop_workflow")
    
    # 解析工作流
    parser = DSLParser()
    workflow = parser.parse_dict(workflow_def)
    
    # 执行工作流
    result = await workflow_executor.execute(workflow)
    
    # 验证结果
    assert result.success
    
    # 验证循环执行次数
    loop_count = sum(1 for log in result.context.logs if "Processing item" in log)
    assert loop_count == 3  # 应该处理3个项目


async def test_error_handling_workflow(workflow_executor, page):
    """测试错误处理工作流"""
    # 加载工作流定义
    workflow_def = await load_workflow("error_handling")
    
    # 解析工作流
    parser = DSLParser()
    workflow = parser.parse_dict(workflow_def)
    
    # 执行工作流
    result = await workflow_executor.execute(workflow)
    
    # 验证错误处理
    assert not result.success
    assert "Error occurred" in result.context.logs[-1]  # 最后一条日志应该是错误信息
    assert result.context.variables.get("error_handled") is True


if __name__ == "__main__":
    # 创建测试数据目录
    os.makedirs(TEST_DATA_DIR, exist_ok=True)
    
    # 运行测试
    pytest.main(["-v", "-s", __file__])
