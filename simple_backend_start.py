"""
AI+RPA 简化启动器 - 只启动后端

由于npm问题，我们先启动后端API服务，然后手动处理前端
"""
import subprocess
import sys
import os
import time
from pathlib import Path


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"✅ 加载环境变量: {key}")


def install_python_deps():
    """安装Python依赖"""
    print("📦 安装Python依赖...")
    
    required_packages = [
        'fastapi',
        'uvicorn[standard]',
        'python-multipart',
        'websockets',
        'requests'
    ]
    
    for package in required_packages:
        try:
            print(f"安装 {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], check=True)
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"⚠️ {package} 安装失败，可能已存在")


def create_backend_api_dir():
    """创建后端API目录和文件"""
    backend_dir = Path("backend_api")
    backend_dir.mkdir(exist_ok=True)
    
    # 检查main.py是否存在
    main_file = backend_dir / "main.py"
    if not main_file.exists():
        print("❌ backend_api/main.py 不存在")
        print("💡 请确保已创建后端API文件")
        return False
    
    print("✅ 后端API文件检查通过")
    return True


def start_backend():
    """启动后端服务"""
    print("🚀 启动后端API服务...")
    
    backend_dir = Path("backend_api")
    
    try:
        # 启动FastAPI服务
        process = subprocess.Popen([
            sys.executable, '-m', 'uvicorn', 'main:app',
            '--host', '0.0.0.0',
            '--port', '8000',
            '--reload'
        ], cwd=backend_dir)
        
        print("✅ 后端服务启动成功!")
        print("🔧 后端API: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        
        return process
        
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return None


def test_backend():
    """测试后端服务"""
    print("🧪 测试后端服务...")
    
    import requests
    
    # 等待服务启动
    for i in range(10):
        try:
            response = requests.get('http://localhost:8000/health', timeout=2)
            if response.status_code == 200:
                print("✅ 后端服务测试通过")
                return True
        except:
            time.sleep(1)
    
    print("⚠️ 后端服务测试超时")
    return False


def provide_frontend_instructions():
    """提供前端安装说明"""
    print("\n" + "="*60)
    print("🎨 前端安装说明")
    print("="*60)
    
    print("\n由于npm问题，请手动安装前端:")
    
    print("\n1️⃣ 安装Node.js和npm:")
    print("   - 访问: https://nodejs.org/")
    print("   - 下载并安装LTS版本")
    print("   - 重启命令行窗口")
    
    print("\n2️⃣ 验证安装:")
    print("   node --version")
    print("   npm --version")
    
    print("\n3️⃣ 安装前端依赖:")
    print("   cd frontend")
    print("   npm install")
    
    print("\n4️⃣ 启动前端:")
    print("   npm start")
    
    print("\n5️⃣ 访问应用:")
    print("   前端: http://localhost:3000")
    print("   后端: http://localhost:8000")
    
    print("\n💡 或者使用替代方案:")
    print("   - 安装yarn: npm install -g yarn")
    print("   - 使用yarn: yarn install && yarn start")


def main():
    """主函数"""
    print("🤖 AI+RPA 简化启动器")
    print("=" * 50)
    
    # 加载环境变量
    load_env()
    
    # 安装Python依赖
    install_python_deps()
    
    # 检查后端文件
    if not create_backend_api_dir():
        return
    
    # 启动后端服务
    backend_process = start_backend()
    
    if backend_process:
        # 等待服务启动
        time.sleep(3)
        
        # 测试后端
        if test_backend():
            print("\n🎉 后端服务启动成功!")
            
            # 提供前端安装说明
            provide_frontend_instructions()
            
            print("\n" + "="*60)
            print("🚀 后端服务正在运行...")
            print("按 Ctrl+C 停止服务")
            print("="*60)
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n👋 正在停止后端服务...")
                try:
                    backend_process.terminate()
                    backend_process.wait(timeout=5)
                except:
                    try:
                        backend_process.kill()
                    except:
                        pass
                print("✅ 后端服务已停止")
        else:
            print("❌ 后端服务测试失败")
            try:
                backend_process.terminate()
            except:
                pass
    else:
        print("❌ 后端服务启动失败")


if __name__ == "__main__":
    main()
