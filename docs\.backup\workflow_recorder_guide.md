# 工作流录制器使用指南

## 概述

工作流录制器是一个强大的工具，可以将浏览器操作录制为可重复执行的工作流。它支持录制点击、输入、导航等操作，并可以添加变量、断言和注释。

## 安装依赖

```bash
pip install -e .
```

## 基本用法

### 1. 启动录制器

```bash
python -m src.recorder --output workflows/my_workflow.json --url https://example.com
```

### 2. 录制浏览器操作

在浏览器中执行操作，录制器会自动记录以下事件：
- 页面导航
- 元素点击
- 表单输入
- 表单提交

### 3. 使用命令

在录制过程中，可以在控制台输入以下命令：

#### 变量操作
- `var name=value` - 设置变量
- `var` - 显示所有变量

#### 步骤控制
- `wait selector` - 添加等待元素步骤
- `assert selector,expected_text` - 添加断言步骤
- `comment your note` - 添加注释步骤

#### 其他命令
- `help` - 显示帮助信息
- `exit` 或 `quit` - 退出录制

### 4. 完成录制

输入 `exit` 或 `quit` 退出录制，工作流将自动保存到指定文件。

## 示例

### 录制登录工作流

1. 启动录制器：
   ```bash
   python -m src.recorder --output workflows/login_workflow.json --url https://example.com/login
   ```

2. 在浏览器中执行登录操作：
   - 输入用户名
   - 输入密码
   - 点击登录按钮

3. 添加断言确保登录成功：
   ```
   assert h1,Welcome
   ```

4. 退出录制：
   ```
   exit
   ```

### 使用录制的工作流

```python
from src.workflow.runner import create_workflow_runner

# 加载工作流
workflow = create_workflow_runner("workflows/login_workflow.json")

# 执行工作流
result = workflow.run()
```

## 高级功能

### 使用变量

在录制过程中，可以使用变量来参数化工作流：

```
var username=<EMAIL>
var password=secure_password
```

### 添加等待

在需要等待元素出现时添加等待步骤：

```
wait #submit-button
```

### 添加断言

验证页面内容：

```
assert h1,Welcome to Dashboard
```

## 故障排除

1. **元素定位问题**
   - 确保使用唯一的选择器
   - 可以手动编辑生成的工作流文件调整选择器

2. **录制中断**
   - 如果录制意外中断，可以重新启动并继续录制
   - 工作流会自动保存，但建议定期备份

3. **浏览器兼容性**
   - 支持所有 Playwright 支持的浏览器
   - 默认使用 Chromium，可以通过修改代码使用其他浏览器

## 后续步骤

- 查看 `examples/` 目录中的示例
- 阅读 API 文档了解高级用法
- 参与贡献改进录制器功能
