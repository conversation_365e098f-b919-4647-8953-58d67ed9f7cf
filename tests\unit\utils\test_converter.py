"""
Tests for the TestCaseConverter class in src.utils.converter
"""
import json
import os
import pytest
from pathlib import Path
from unittest.mock import mock_open, patch

from src.utils.converter import TestCaseConverter

# Sample test data
SAMPLE_PYTHON_SCRIPT = '''
"""Sample test case"""

# Test steps
def test_example():
    # Goto example.com
    page.goto("https://example.com")
    
    # Click on a link
    page.click("text=More information...")
    
    # Fill a form
    page.fill("#username", "testuser")
    page.fill("#password", "testpass123")
    
    # Check a checkbox
    page.check("#remember-me")
    
    # Take a screenshot
    page.screenshot(path="example.png")
'''

EXPECTED_JSON = {
    "name": "test_script",
    "description": "Sample test case",
    "steps": [
        {"action": "goto", "url": "https://example.com"},
        {"action": "click", "selector": "text=More information..."},
        {"action": "fill", "selector": "#username", "value": "testuser"},
        {"action": "fill", "selector": "#password", "value": "testpass123"},
        {"action": "check", "selector": "#remember-me"},
        {"action": "screenshot", "path": "example.png"}
    ],
    "assertions": []
}

class TestTestCaseConverter:
    """Test cases for TestCaseConverter class"""
    
    def test_python_to_json_basic(self, tmp_path):
        """Test converting a Python test script to JSON format"""
        # Create a temporary Python file
        python_file = tmp_path / "test_script.py"
        python_file.write_text(SAMPLE_PYTHON_SCRIPT)
        
        # Convert to JSON
        result = TestCaseConverter.python_to_json(str(python_file))
        
        # Verify basic structure
        assert "name" in result
        assert "description" in result
        assert "steps" in result
        assert "assertions" in result
        
        # Verify steps were parsed correctly
        assert len(result["steps"]) == 6
        assert result["steps"][0]["action"] == "goto"
        assert result["steps"][1]["action"] == "click"
        
    def test_python_to_json_with_output_file(self, tmp_path):
        """Test saving the JSON output to a file"""
        python_file = tmp_path / "test_script.py"
        output_file = tmp_path / "output.json"
        python_file.write_text(SAMPLE_PYTHON_SCRIPT)
        
        # Convert and save to file
        result = TestCaseConverter.python_to_json(str(python_file), str(output_file))
        
        # Verify file was created
        assert output_file.exists()
        
        # Verify file content matches the return value
        with open(output_file, 'r', encoding='utf-8') as f:
            file_content = json.load(f)
            
        assert file_content == result
    
    def test_json_to_python_basic(self, tmp_path):
        """Test converting JSON test case to Python script"""
        # Create a temporary JSON file
        json_file = tmp_path / "test_case.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(EXPECTED_JSON, f)
        
        # Convert to Python
        output_file = tmp_path / "output.py"
        TestCaseConverter.json_to_python(str(json_file), str(output_file))
        
        # Verify file was created
        assert output_file.exists()
        
        # Verify basic content
        content = output_file.read_text()
        assert "def test_example" in content
        assert "page.goto" in content
        
    @patch('builtins.open', new_callable=mock_open, read_data=json.dumps(EXPECTED_JSON))
    def test_json_to_python_with_invalid_json(self, mock_file):
        """Test handling of invalid JSON input"""
        mock_file.return_value.read.side_effect = json.JSONDecodeError("Expecting value", "", 0)
        
        with pytest.raises(json.JSONDecodeError):
            TestCaseConverter.json_to_python("invalid.json", "output.py")
    
    def test_extract_metadata(self):
        """Test extracting metadata from docstring"""
        docstring = """
        Test case for login functionality
        
        This test verifies the login functionality
        with valid credentials.
        """
        
        description = TestCaseConverter._extract_metadata(docstring)
        assert "Test case for login functionality" in description
        assert "This test verifies" in description
    
    def test_extract_metadata_empty(self):
        """Test extracting metadata from empty docstring"""
        assert TestCaseConverter._extract_metadata("") == ""
        assert TestCaseConverter._extract_metadata(None) == ""

    def test_parse_step_goto(self):
        """Test parsing a goto step"""
        node = type('Node', (), {
            'func': type('Func', (), {'attr': 'goto'}),
            'args': [type('Arg', (), {'value': 'https://example.com'})],
            'keywords': []
        })
        
        step = TestCaseConverter._parse_step(node)
        assert step == {"action": "goto", "url": "https://example.com"}
    
    def test_parse_step_click(self):
        """Test parsing a click step"""
        node = type('Node', (), {
            'func': type('Func', (), {'attr': 'click'}),
            'args': [type('Arg', (), {'value': 'button#submit'})],
            'keywords': []
        })
        
        step = TestCaseConverter._parse_step(node)
        assert step == {"action": "click", "selector": "button#submit"}
    
    def test_parse_step_fill(self):
        """Test parsing a fill step"""
        node = type('Node', (), {
            'func': type('Func', (), {'attr': 'fill'}),
            'args': [
                type('Arg', (), {'value': 'input#username'}),
                type('Arg', (), {'value': 'testuser'})
            ],
            'keywords': []
        })
        
        step = TestCaseConverter._parse_step(node)
        assert step == {
            "action": "fill", 
            "selector": "input#username",
            "value": "testuser"
        }

    def test_parse_step_with_keywords(self):
        """Test parsing a step with keyword arguments"""
        node = type('Node', (), {
            'func': type('Func', (), {'attr': 'click'}),
            'args': [type('Arg', (), {'value': 'button#submit'})],
            'keywords': [
                type('Keyword', (), {
                    'arg': 'timeout',
                    'value': type('Value', (), {'value': 5000})
                }),
                type('Keyword', (), {
                    'arg': 'force',
                    'value': type('Value', (), {'value': True})
                })
            ]
        })
        
        step = TestCaseConverter._parse_step(node)
        assert step == {
            "action": "click",
            "selector": "button#submit",
            "timeout": 5000,
            "force": True
        }
