"""
文件操作测试
"""
import os
import sys
import json
import tempfile
import time
from pathlib import Path
from typing import Generator, Optional
import pytest
from playwright.sync_api import Page, expect, Download

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入测试辅助函数
from tests.test_utils import get_test_data_path, save_debug_info, create_temp_file, cleanup_files, verify_file_content

# 测试页面路径
TEST_PAGE = "file_test_page.html"

# 日志记录
import logging
logger = logging.getLogger(__name__)

class TestFileOperations:
    """测试文件操作"""
    
    @pytest.fixture(scope="function", autouse=True)
    def setup(self, page: Page):
        """测试前置条件"""
        test_url = get_test_data_path(TEST_PAGE)
        print(f"\n正在导航到: {test_url}")
        
        # 导航到测试页面
        page.goto(test_url)
        
        # 等待页面加载完成
        page.wait_for_load_state("networkidle")
        
        # 验证页面标题
        expect(page).to_have_title("File Test Page")
        
        yield page
        
        # 测试后清理
        print("\n测试完成，清理测试数据...")
    
    def test_file_upload(self, page: Page, tmp_path: Path):
        """测试文件上传功能"""
        logger.info("=== 开始测试文件上传 ===")
        
        try:
            # 1. 创建测试文件
            test_file = tmp_path / "test_upload.txt"
            file_content = "This is a test file for upload."
            test_file.write_text(file_content, encoding="utf-8")
            
            logger.info(f"创建测试文件: {test_file}")
            
            # 2. 选择文件上传
            with page.expect_file_chooser() as fc_info:
                page.click("#file-upload")
            file_chooser = fc_info.value
            file_chooser.set_files(str(test_file))
            
            # 3. 点击上传按钮
            upload_btn = page.locator("#upload-btn")
            upload_btn.click()
            
            # 4. 验证上传状态
            upload_status = page.locator("#upload-status")
            expect(upload_status).to_have_class("success")
            
            # 5. 验证文件列表
            file_list = page.locator("#file-list .file-item")
            expect(file_list).to_have_text(f"Uploaded: {test_file.name} (29 Bytes)")
            
            # 6. 验证控制台日志
            console_logs = page.evaluate('window.consoleMessages')
            assert any('File uploaded:' in log for log in console_logs), "未找到上传成功的日志"
            
            logger.info("文件上传测试通过")
            return True
            
        except Exception as e:
            logger.error(f"文件上传测试失败: {str(e)}")
            save_debug_info(page, "test_file_upload_failed")
            raise
    
    def test_file_download_text(self, page: Page, tmp_path: Path):
        """测试文本文件下载功能"""
        logger.info("=== 开始测试文本文件下载 ===")
        
        try:
            # 1. 设置下载路径
            download_dir = tmp_path / "downloads"
            download_dir.mkdir()
            
            # 2. 监听下载事件
            with page.expect_download() as download_info:
                page.click("#download-txt")
            
            # 3. 获取下载的文件
            download = download_info.value
            
            # 4. 保存下载的文件
            save_path = download_dir / "downloaded_file.txt"
            download.save_as(save_path)
            
            # 5. 验证文件内容
            assert save_path.exists(), "文件下载失败"
            assert save_path.read_text(encoding="utf-8") == "This is a test text file."
            
            # 6. 验证控制台日志
            console_logs = page.evaluate('window.consoleMessages')
            assert any('Downloaded: test.txt' in log for log in console_logs), "未找到下载成功的日志"
            
            logger.info(f"文件下载成功: {save_path}")
            return True
            
        except Exception as e:
            logger.error(f"文件下载测试失败: {str(e)}")
            save_debug_info(page, "test_file_download_text_failed")
            raise
    
    def test_file_download_json(self, page: Page, tmp_path: Path):
        """测试JSON文件下载功能"""
        logger.info("=== 开始测试JSON文件下载 ===")
        
        try:
            # 1. 设置下载路径
            download_dir = tmp_path / "downloads"
            download_dir.mkdir()
            
            # 2. 监听下载事件
            with page.expect_download() as download_info:
                page.click("#download-json")
            
            # 3. 获取下载的文件
            download = download_info.value
            
            # 4. 保存下载的文件
            save_path = download_dir / "data.json"
            download.save_as(save_path)
            
            # 5. 验证文件内容
            assert save_path.exists(), "文件下载失败"
            
            # 6. 验证JSON内容
            with open(save_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            expected_data = {"name": "Test", "value": 123, "items": ["a", "b", "c"]}
            assert data == expected_data, f"下载的JSON内容不匹配，期望: {expected_data}，实际: {data}"
            
            # 7. 验证控制台日志
            console_logs = page.evaluate('window.consoleMessages')
            assert any('Downloaded: data.json' in log for log in console_logs), "未找到下载成功的日志"
            
            logger.info(f"JSON文件下载成功: {save_path}")
            return True
            
        except Exception as e:
            logger.error(f"JSON文件下载测试失败: {str(e)}")
            save_debug_info(page, "test_file_download_json_failed")
            raise
    
    def test_multiple_file_upload(self, page: Page, tmp_path: Path):
        """测试多文件上传功能"""
        logger.info("=== 开始测试多文件上传 ===")
        
        try:
            # 1. 创建多个测试文件
            test_files = []
            for i in range(3):
                test_file = tmp_path / f"test_upload_{i}.txt"
                file_content = f"This is test file {i} for upload."
                test_file.write_text(file_content, encoding="utf-8")
                test_files.append(test_file)
                logger.info(f"创建测试文件: {test_file}")
            
            # 2. 选择多个文件上传
            with page.expect_file_chooser() as fc_info:
                page.click("#file-upload")
            file_chooser = fc_info.value
            file_chooser.set_files([str(f) for f in test_files])
            
            # 3. 点击上传按钮
            upload_btn = page.locator("#upload-btn")
            upload_btn.click()
            
            # 4. 验证上传状态
            upload_status = page.locator("#upload-status")
            expect(upload_status).to_have_class("success")
            
            # 5. 验证文件列表
            file_items = page.locator("#file-list .file-item")
            expect(file_items).to_have_count(3)
            
            # 6. 验证每个文件是否都显示在列表中
            for test_file in test_files:
                expected_text = f"Uploaded: {test_file.name} ({len(test_file.read_text(encoding='utf-8'))} Bytes)"
                assert any(expected_text in item.text_content() for item in file_items.all())
            
            # 7. 验证控制台日志
            console_logs = page.evaluate('window.consoleMessages')
            for test_file in test_files:
                assert any(f'File uploaded: {test_file.name}' in log for log in console_logs), f"未找到文件 {test_file.name} 上传成功的日志"
            
            logger.info("多文件上传测试通过")
            return True
            
        except Exception as e:
            logger.error(f"多文件上传测试失败: {str(e)}")
            save_debug_info(page, "test_multiple_file_upload_failed")
            raise
