"""
增强验证码处理测试

测试完整的验证码识别流程：OCR识别 → AI识别 → 用户处理
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"✅ 加载环境变量: {key}")


def test_imports():
    """测试导入"""
    print("🧪 测试增强验证码处理器导入")
    print("-" * 40)
    
    try:
        from enhanced_captcha_processor import (
            EnhancedCaptchaProcessor,
            AIVisionCaptchaRecognizer,
            CaptchaRecognitionResult,
            get_enhanced_captcha_processor,
            process_captcha_complete
        )
        print("   ✅ enhanced_captcha_processor 导入成功")
        
        from opencv_ocr_processor import OpenCVOCREngine
        print("   ✅ opencv_ocr_processor 导入成功")
        
        from ai_captcha_handler import AICaptchaDetector
        print("   ✅ ai_captcha_handler 导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        return False


async def test_ai_vision_recognizer():
    """测试AI视觉识别器"""
    print("\n🧪 测试AI视觉验证码识别器")
    print("-" * 40)
    
    try:
        from enhanced_captcha_processor import AIVisionCaptchaRecognizer
        import cv2
        import numpy as np
        
        recognizer = AIVisionCaptchaRecognizer()
        print("   ✅ AI视觉识别器创建成功")
        print(f"   ✅ 使用LLM提供商: {recognizer.llm_provider}")
        
        # 创建测试验证码图片
        test_image = np.ones((60, 150, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "TEST123", (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
        
        test_path = "test_ai_captcha.png"
        cv2.imwrite(test_path, test_image)
        print("   ✅ 测试验证码图片创建成功")
        
        # 测试AI识别
        print("   🤖 开始AI视觉识别...")
        ai_text, ai_confidence = await recognizer.recognize_captcha_with_ai(test_path)
        
        print(f"   📊 AI识别结果:")
        print(f"      识别文本: '{ai_text}'")
        print(f"      置信度: {ai_confidence:.2f}")
        
        # 清理测试文件
        try:
            Path(test_path).unlink()
        except Exception:
            pass
        
        return True
        
    except Exception as e:
        print(f"   ❌ AI视觉识别器测试失败: {e}")
        return False


async def test_enhanced_processor():
    """测试增强验证码处理器"""
    print("\n🧪 测试增强验证码处理器")
    print("-" * 40)
    
    try:
        from enhanced_captcha_processor import get_enhanced_captcha_processor
        
        processor = get_enhanced_captcha_processor()
        print("   ✅ 增强验证码处理器创建成功")
        
        # 检查组件
        print("   ✅ AI检测器已初始化")
        print("   ✅ OpenCV OCR引擎已初始化")
        print("   ✅ AI视觉识别器已初始化")
        
        # 检查配置
        print(f"   ✅ OCR最低置信度阈值: {processor.min_ocr_confidence}")
        print(f"   ✅ AI最低置信度阈值: {processor.min_ai_confidence}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 增强验证码处理器测试失败: {e}")
        return False


async def test_real_enhanced_captcha():
    """测试真实增强验证码处理"""
    print("\n🧪 测试真实增强验证码处理")
    print("-" * 40)
    
    try:
        from playwright.async_api import async_playwright
        from enhanced_captcha_processor import get_enhanced_captcha_processor
        
        print("   🌐 启动浏览器进行真实测试...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            
            try:
                # 访问登录页面
                print("   📱 访问登录页面...")
                await page.goto("https://test.yushanyun.net/ac/web/")
                await page.wait_for_load_state('networkidle')
                
                # 使用增强验证码处理器
                print("   🚀 开始增强验证码处理流程...")
                print("   💡 流程: OCR识别 → AI识别 → 用户处理")
                
                processor = get_enhanced_captcha_processor()
                
                # 询问是否进行交互式测试
                interactive_test = input("\n   是否进行交互式测试（包括用户输入）? (y/n): ").strip().lower()
                interactive = interactive_test in ['y', 'yes']
                
                result = await processor.process_captcha_complete(page, interactive=interactive)
                
                print(f"\n   📊 增强验证码处理结果:")
                print(f"      处理成功: {'✅' if result.success else '❌'}")
                print(f"      识别文本: '{result.recognized_text}'")
                print(f"      识别方法: {result.method}")
                print(f"      识别置信度: {result.confidence:.2f}")
                print(f"      处理时间: {result.processing_time:.2f}秒")
                
                if result.image_path:
                    print(f"      验证码图片: {result.image_path}")
                
                if result.error_message:
                    print(f"      错误信息: {result.error_message}")
                
                if result.success:
                    print(f"   🎉 增强验证码处理成功！")
                    print(f"   💡 使用方法: {result.method}")
                    
                    if result.method == "ocr":
                        print("      ✅ OpenCV OCR识别成功")
                    elif result.method == "ai":
                        print("      ✅ AI视觉识别成功")
                    elif result.method == "manual":
                        print("      ✅ 用户手动输入成功")
                else:
                    print(f"   ❌ 增强验证码处理失败")
                    print(f"   💡 失败原因: {result.error_message}")
                
                # 保持浏览器打开以便查看
                input("\n   👀 请查看浏览器中的结果，按回车关闭...")
                
            finally:
                await browser.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 真实增强验证码处理测试失败: {e}")
        return False


def show_enhanced_usage():
    """显示增强验证码处理使用示例"""
    print("\n💡 增强验证码处理使用示例")
    print("=" * 50)
    
    print("\n1. 完整验证码处理流程:")
    print("""
from enhanced_captcha_processor import process_captcha_complete

# 完整的验证码处理（OCR → AI → 用户处理）
result = await process_captcha_complete(page, interactive=True)

if result.success:
    print(f"验证码处理成功: {result.recognized_text}")
    print(f"使用方法: {result.method}")  # ocr, ai, manual
    print(f"置信度: {result.confidence:.2f}")
else:
    print(f"验证码处理失败: {result.error_message}")
""")
    
    print("\n2. 自定义处理流程:")
    print("""
from enhanced_captcha_processor import get_enhanced_captcha_processor

processor = get_enhanced_captcha_processor()

# 设置置信度阈值
processor.min_ocr_confidence = 0.8  # OCR最低置信度
processor.min_ai_confidence = 0.7   # AI最低置信度

# 执行处理
result = await processor.process_captcha_complete(page, interactive=False)
""")
    
    print("\n3. 集成到登录工作流:")
    print("""
# 在登录工作流中自动使用增强验证码处理
from ai_login_workflow_generator import LoginWorkflowExecutor

executor = LoginWorkflowExecutor()
result = await executor.execute_login_workflow(template, credentials)

# 验证码会自动按照 OCR → AI → 用户处理 的流程进行
""")
    
    print("\n4. 处理流程说明:")
    print("""
增强验证码处理流程：
1. 🔍 AI检测验证码元素
2. 📷 截取验证码图片
3. 🔤 OpenCV OCR识别
4. 📊 检查OCR置信度
5. 🤖 AI视觉识别（如果OCR失败）
6. 📊 检查AI置信度
7. 👤 用户手动输入（如果AI也失败）
8. ✍️ 自动填写验证码
""")


async def main():
    """主函数"""
    print("🎭 增强验证码处理系统测试")
    print("验证OCR → AI → 用户处理的完整流程")
    print("=" * 60)
    
    # 加载环境变量
    load_env()
    
    # 检查AI配置
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        print("❌ 缺少GEMINI_API_KEY配置")
        return
    
    print("✅ AI配置检查通过")
    
    # 检查OpenCV
    try:
        import cv2
        print(f"✅ OpenCV可用 (版本: {cv2.__version__})")
    except ImportError:
        print("❌ OpenCV不可用，请安装: pip install opencv-python")
        return
    
    # 运行测试
    tests = [
        ("模块导入", test_imports),
        ("AI视觉识别器", test_ai_vision_recognizer),
        ("增强验证码处理器", test_enhanced_processor)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                results[test_name] = await test_func()
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 增强验证码处理系统测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！增强验证码处理系统准备就绪！")
        print(f"\n🚀 系统功能:")
        print(f"   ✅ OpenCV OCR识别")
        print(f"   ✅ AI视觉识别")
        print(f"   ✅ 用户手动输入")
        print(f"   ✅ 智能流程切换")
        print(f"   ✅ 自动填写验证码")
        
        print(f"\n🔧 处理流程:")
        print(f"   1. 🔤 OpenCV OCR识别（快速）")
        print(f"   2. 🤖 AI视觉识别（高精度）")
        print(f"   3. 👤 用户手动输入（保底）")
        print(f"   4. ✍️ 自动填写验证码")
        
        # 询问是否进行真实测试
        real_test = input(f"\n🧪 是否进行真实验证码处理测试? (y/n): ").strip().lower()
        if real_test in ['y', 'yes']:
            await test_real_enhanced_captcha()
        
        # 显示使用示例
        show_enhanced_usage()
        
        print(f"\n🎯 增强验证码处理系统优势:")
        print(f"   • 多层次识别策略")
        print(f"   • 智能置信度判断")
        print(f"   • 用户交互保底")
        print(f"   • 完整的错误处理")
        print(f"   • 与登录工作流无缝集成")
        
    else:
        print(f"\n❌ 部分测试失败，需要检查配置")


if __name__ == "__main__":
    asyncio.run(main())
