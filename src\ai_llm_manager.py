"""
多平台AI大模型管理器

支持OpenAI、Google Gemini、阿里云通义千问等多个AI平台的统一接口
"""
import os
import logging
import asyncio
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class LLMProvider(Enum):
    """LLM提供商枚举"""
    OPENAI = "openai"
    GEMINI = "gemini"
    QWEN = "qwen"
    CLAUDE = "claude"


@dataclass
class LLMConfig:
    """LLM配置"""
    provider: LLMProvider
    model: str
    api_key: str
    base_url: Optional[str] = None
    temperature: float = 0.1
    max_tokens: Optional[int] = None
    timeout: int = 30


@dataclass
class LLMResponse:
    """LLM响应"""
    content: str
    provider: LLMProvider
    model: str
    usage: Optional[Dict[str, Any]] = None
    finish_reason: Optional[str] = None


class BaseLLMProvider(ABC):
    """LLM提供商基类"""
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.client = None
        self._initialize_client()
    
    @abstractmethod
    def _initialize_client(self):
        """初始化客户端"""
        pass
    
    @abstractmethod
    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """生成响应"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查是否可用"""
        pass


class OpenAIProvider(BaseLLMProvider):
    """OpenAI提供商"""
    
    def _initialize_client(self):
        """初始化OpenAI客户端"""
        try:
            from langchain_openai import ChatOpenAI
            self.client = ChatOpenAI(
                model=self.config.model,
                api_key=self.config.api_key,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens,
                timeout=self.config.timeout
            )
            logger.info(f"OpenAI客户端初始化成功: {self.config.model}")
        except ImportError:
            logger.error("langchain_openai未安装，请安装: pip install langchain-openai")
            self.client = None
        except Exception as e:
            logger.error(f"OpenAI客户端初始化失败: {e}")
            self.client = None
    
    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """生成响应"""
        if not self.client:
            raise Exception("OpenAI客户端未初始化")
        
        try:
            # 使用langchain的invoke方法
            response = await self.client.ainvoke(prompt)
            
            return LLMResponse(
                content=response.content,
                provider=LLMProvider.OPENAI,
                model=self.config.model,
                usage=getattr(response, 'usage_metadata', None),
                finish_reason=getattr(response, 'finish_reason', None)
            )
        except Exception as e:
            logger.error(f"OpenAI生成失败: {e}")
            raise
    
    def is_available(self) -> bool:
        """检查是否可用"""
        return self.client is not None and bool(self.config.api_key)


class GeminiProvider(BaseLLMProvider):
    """Google Gemini提供商"""

    def _initialize_client(self):
        """初始化Gemini客户端"""
        try:
            # 检查是否使用OpenAI兼容端点
            if self.config.base_url and "openai" in self.config.base_url.lower():
                # 使用OpenAI兼容的方式
                from langchain_openai import ChatOpenAI
                self.client = ChatOpenAI(
                    model=self.config.model,
                    api_key=self.config.api_key,
                    base_url=self.config.base_url,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_tokens,
                    timeout=self.config.timeout
                )
                self.use_openai_compatible = True
                logger.info(f"Gemini客户端初始化成功(OpenAI兼容): {self.config.model}, URL: {self.config.base_url}")
            else:
                # 使用原生Google API
                import google.generativeai as genai

                genai.configure(api_key=self.config.api_key)
                self.client = genai.GenerativeModel(self.config.model)
                self.use_openai_compatible = False
                logger.info(f"Gemini客户端初始化成功(原生API): {self.config.model}")

        except ImportError as e:
            logger.error(f"依赖未安装: {e}")
            logger.error("请安装: pip install google-generativeai langchain-openai")
            self.client = None
        except Exception as e:
            logger.error(f"Gemini客户端初始化失败: {e}")
            self.client = None
    
    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """生成响应"""
        if not self.client:
            raise Exception("Gemini客户端未初始化")

        try:
            if self.use_openai_compatible:
                # 使用OpenAI兼容接口
                response = await self.client.ainvoke(prompt)

                return LLMResponse(
                    content=response.content,
                    provider=LLMProvider.GEMINI,
                    model=self.config.model,
                    usage=getattr(response, 'usage_metadata', None),
                    finish_reason=getattr(response, 'finish_reason', None)
                )
            else:
                # 使用原生Google API
                generation_config = {
                    "temperature": self.config.temperature,
                    "max_output_tokens": self.config.max_tokens,
                }

                # 生成响应
                response = await asyncio.to_thread(
                    self.client.generate_content,
                    prompt,
                    generation_config=generation_config
                )

                return LLMResponse(
                    content=response.text,
                    provider=LLMProvider.GEMINI,
                    model=self.config.model,
                    usage={
                        "prompt_tokens": response.usage_metadata.prompt_token_count if hasattr(response, 'usage_metadata') else None,
                        "completion_tokens": response.usage_metadata.candidates_token_count if hasattr(response, 'usage_metadata') else None,
                    },
                    finish_reason=response.candidates[0].finish_reason.name if response.candidates else None
                )
        except Exception as e:
            logger.error(f"Gemini生成失败: {e}")
            raise
    
    def is_available(self) -> bool:
        """检查是否可用"""
        return self.client is not None and bool(self.config.api_key)


class QwenProvider(BaseLLMProvider):
    """阿里云通义千问提供商"""
    
    def _initialize_client(self):
        """初始化通义千问客户端"""
        try:
            import dashscope
            
            dashscope.api_key = self.config.api_key
            self.client = dashscope
            logger.info(f"通义千问客户端初始化成功: {self.config.model}")
        except ImportError:
            logger.error("dashscope未安装，请安装: pip install dashscope")
            self.client = None
        except Exception as e:
            logger.error(f"通义千问客户端初始化失败: {e}")
            self.client = None
    
    async def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """生成响应"""
        if not self.client:
            raise Exception("通义千问客户端未初始化")
        
        try:
            from dashscope import Generation
            
            # 生成响应
            response = await asyncio.to_thread(
                Generation.call,
                model=self.config.model,
                prompt=prompt,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens,
                top_p=0.8
            )
            
            if response.status_code == 200:
                output = response.output
                return LLMResponse(
                    content=output.text,
                    provider=LLMProvider.QWEN,
                    model=self.config.model,
                    usage={
                        "prompt_tokens": response.usage.input_tokens if hasattr(response, 'usage') else None,
                        "completion_tokens": response.usage.output_tokens if hasattr(response, 'usage') else None,
                    },
                    finish_reason=output.finish_reason if hasattr(output, 'finish_reason') else None
                )
            else:
                raise Exception(f"通义千问API调用失败: {response.message}")
                
        except Exception as e:
            logger.error(f"通义千问生成失败: {e}")
            raise
    
    def is_available(self) -> bool:
        """检查是否可用"""
        return self.client is not None and bool(self.config.api_key)


class LLMManager:
    """LLM管理器"""
    
    def __init__(self):
        """初始化LLM管理器"""
        self.providers: Dict[LLMProvider, BaseLLMProvider] = {}
        self.default_provider: Optional[LLMProvider] = None
        self.fallback_order: List[LLMProvider] = []
        
        # 自动初始化可用的提供商
        self._auto_initialize_providers()
    
    def _auto_initialize_providers(self):
        """自动初始化可用的提供商"""
        # OpenAI配置
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_key:
            try:
                config = LLMConfig(
                    provider=LLMProvider.OPENAI,
                    model=os.getenv('OPENAI_MODEL', 'gpt-4o'),
                    api_key=openai_key,
                    temperature=float(os.getenv('OPENAI_TEMPERATURE', '0.1')),
                    max_tokens=int(os.getenv('OPENAI_MAX_TOKENS', '2000')) if os.getenv('OPENAI_MAX_TOKENS') else None
                )
                self.add_provider(config)
                logger.info("OpenAI提供商自动配置成功")
            except Exception as e:
                logger.warning(f"OpenAI提供商自动配置失败: {e}")
        
        # Gemini配置
        gemini_key = os.getenv('GEMINI_API_KEY')
        if gemini_key:
            try:
                config = LLMConfig(
                    provider=LLMProvider.GEMINI,
                    model=os.getenv('GEMINI_MODEL', 'gemini-pro'),
                    api_key=gemini_key,
                    base_url=os.getenv('GEMINI_BASE_URL'),  # 支持自定义API地址
                    temperature=float(os.getenv('GEMINI_TEMPERATURE', '0.1')),
                    max_tokens=int(os.getenv('GEMINI_MAX_TOKENS', '2000')) if os.getenv('GEMINI_MAX_TOKENS') else None
                )
                self.add_provider(config)
                logger.info(f"Gemini提供商自动配置成功 (URL: {config.base_url or '默认'})")
            except Exception as e:
                logger.warning(f"Gemini提供商自动配置失败: {e}")
        
        # 通义千问配置
        qwen_key = os.getenv('QWEN_API_KEY') or os.getenv('DASHSCOPE_API_KEY')
        if qwen_key:
            try:
                config = LLMConfig(
                    provider=LLMProvider.QWEN,
                    model=os.getenv('QWEN_MODEL', 'qwen-turbo'),
                    api_key=qwen_key,
                    temperature=float(os.getenv('QWEN_TEMPERATURE', '0.1')),
                    max_tokens=int(os.getenv('QWEN_MAX_TOKENS', '2000')) if os.getenv('QWEN_MAX_TOKENS') else None
                )
                self.add_provider(config)
                logger.info("通义千问提供商自动配置成功")
            except Exception as e:
                logger.warning(f"通义千问提供商自动配置失败: {e}")
        
        # 设置默认提供商和降级顺序
        self._set_default_and_fallback()
    
    def _set_default_and_fallback(self):
        """设置默认提供商和降级顺序"""
        available_providers = [p for p in self.providers.keys() if self.providers[p].is_available()]
        
        if available_providers:
            # 优先级: OpenAI > Gemini > Qwen
            priority_order = [LLMProvider.OPENAI, LLMProvider.GEMINI, LLMProvider.QWEN]
            
            for provider in priority_order:
                if provider in available_providers:
                    self.default_provider = provider
                    break
            
            # 设置降级顺序
            self.fallback_order = [p for p in priority_order if p in available_providers]
            
            logger.info(f"默认提供商: {self.default_provider}")
            logger.info(f"降级顺序: {self.fallback_order}")
        else:
            logger.warning("没有可用的LLM提供商")
    
    def add_provider(self, config: LLMConfig):
        """添加LLM提供商"""
        try:
            if config.provider == LLMProvider.OPENAI:
                provider = OpenAIProvider(config)
            elif config.provider == LLMProvider.GEMINI:
                provider = GeminiProvider(config)
            elif config.provider == LLMProvider.QWEN:
                provider = QwenProvider(config)
            else:
                raise ValueError(f"不支持的提供商: {config.provider}")
            
            self.providers[config.provider] = provider
            logger.info(f"添加LLM提供商: {config.provider.value}")
            
        except Exception as e:
            logger.error(f"添加LLM提供商失败: {e}")
    
    def get_available_providers(self) -> List[LLMProvider]:
        """获取可用的提供商列表"""
        return [p for p in self.providers.keys() if self.providers[p].is_available()]
    
    async def generate(self, prompt: str, provider: Optional[LLMProvider] = None, **kwargs) -> LLMResponse:
        """生成响应"""
        # 确定使用的提供商
        target_provider = provider or self.default_provider
        
        if not target_provider:
            raise Exception("没有可用的LLM提供商")
        
        # 尝试使用指定提供商
        if target_provider in self.providers and self.providers[target_provider].is_available():
            try:
                return await self.providers[target_provider].generate(prompt, **kwargs)
            except Exception as e:
                logger.warning(f"{target_provider.value}生成失败: {e}")
                
                # 如果指定了提供商但失败了，尝试降级
                if provider is None:  # 只有在使用默认提供商时才降级
                    return await self._generate_with_fallback(prompt, target_provider, **kwargs)
                else:
                    raise
        else:
            raise Exception(f"提供商 {target_provider.value} 不可用")
    
    async def _generate_with_fallback(self, prompt: str, failed_provider: LLMProvider, **kwargs) -> LLMResponse:
        """使用降级策略生成响应"""
        for provider in self.fallback_order:
            if provider != failed_provider and provider in self.providers:
                try:
                    logger.info(f"尝试降级到提供商: {provider.value}")
                    return await self.providers[provider].generate(prompt, **kwargs)
                except Exception as e:
                    logger.warning(f"{provider.value}降级失败: {e}")
                    continue
        
        raise Exception("所有LLM提供商都不可用")


# 全局实例
global_llm_manager = LLMManager()


def get_llm_manager() -> LLMManager:
    """获取全局LLM管理器实例"""
    return global_llm_manager
