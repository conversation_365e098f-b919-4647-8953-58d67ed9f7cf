# 快速开始

**预计时间**: 5分钟 | **难度**: 初级

## 🎯 目标

通过本指南，您将：
- 安装和配置AI+RPA系统
- 运行第一个AI自动化任务
- 了解基本使用方法

## 📋 前置条件

### 系统要求
- **操作系统**: Windows 11, macOS, 或 Linux
- **Python**: 3.8 或更高版本
- **内存**: 至少 4GB RAM
- **网络**: 稳定的互联网连接

### 必需账户
- **OpenAI账户**: 用于AI功能 ([注册链接](https://platform.openai.com/))
- **API密钥**: 从OpenAI控制台获取

## 🚀 安装步骤

### 步骤1: 下载项目
```bash
# 克隆项目
git clone <repository-url>
cd playwright

# 或下载ZIP文件并解压
```

### 步骤2: 创建虚拟环境
```bash
# Windows
python -m venv .venv
.\.venv\Scripts\activate

# macOS/Linux
python3 -m venv .venv
source .venv/bin/activate
```

### 步骤3: 安装依赖
```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装AI功能依赖
pip install browser-use

# 安装浏览器
playwright install
```

### 步骤4: 配置API密钥
```bash
# 方法1: 环境变量
export OPENAI_API_KEY="your-api-key-here"

# 方法2: .env文件
echo "OPENAI_API_KEY=your-api-key-here" > .env
```

## 🎭 第一次运行

### 运行演示程序
```bash
# 运行AI集成演示
python examples/ai_integration_demo.py

# 运行真实browser-use演示
python examples/real_browser_use_demo.py
```

### 预期输出
```
🚀 真实browser-use集成演示
============================================================
🔍 环境检查:
   ✅ OpenAI API密钥: 已配置
   ✅ browser-use: 已安装
🧪 开始测试 3 个用例:
...
📈 测试统计
总测试数: 3
成功数: 3
成功率: 100.0%
```

## 🎯 第一个AI任务

### 简单网页操作
```python
from src.real_browser_use_integration import get_real_browser_use_agent

# 创建AI代理
agent = get_real_browser_use_agent()

# 执行AI任务
result = await agent.execute_user_request("请帮我打开example.com并获取页面标题")

print(f"任务结果: {result['success']}")
```

### 智能表单填写
```python
# 执行表单填写任务
result = await agent.execute_user_request(
    "在联系表单中填写姓名'张三'，邮箱'<EMAIL>'"
)
```

## 🔧 基本配置

### 配置文件示例
创建 `config.yaml`:
```yaml
# AI配置
ai:
  model: "gpt-4o"
  temperature: 0.1
  max_tokens: 2000

# 浏览器配置
browser:
  headless: false
  timeout: 30000
  
# 监控配置
monitoring:
  enabled: true
  interval: 500
```

### 环境变量
```bash
# 必需配置
OPENAI_API_KEY=your-openai-key

# 可选配置
LOG_LEVEL=INFO
BROWSER_HEADLESS=false
MONITORING_ENABLED=true
```

## 🎨 Web界面 (即将推出)

### 启动Web界面
```bash
# 启动Web服务器
python -m src.web.app

# 访问界面
# http://localhost:8080
```

### 主要功能
- 🎯 自然语言任务输入
- 📊 实时执行监控
- 📈 执行历史和统计
- ⚙️ 系统配置管理

## 🆘 故障排除

### 常见问题

**Q: 提示"browser-use未安装"**
```bash
pip install browser-use --upgrade
```

**Q: OpenAI API错误**
```bash
# 检查API密钥
echo $OPENAI_API_KEY

# 测试API连接
python -c "import openai; print('API连接正常')"
```

**Q: Playwright浏览器问题**
```bash
# 重新安装浏览器
playwright install --force
```

**Q: 权限错误**
```bash
# Windows: 以管理员身份运行
# Linux/Mac: 检查文件权限
chmod +x scripts/*.sh
```

### 获取帮助
- 📖 查看[完整用户指南](./README.md)
- 🐛 提交[Issue报告](https://github.com/your-repo/issues)
- 💬 加入[社区讨论](https://github.com/your-repo/discussions)

## 🎉 下一步

恭喜！您已经成功运行了AI+RPA系统。接下来可以：

1. **学习更多功能**: 查看[用户指南](./README.md)
2. **自定义工作流**: 学习[工作流编写](./workflows.md)
3. **集成到项目**: 查看[集成指南](../development/README.md)
4. **参与开发**: 查看[开发指南](../development/README.md)

---

> 🚀 **提示**: 如果遇到问题，请先查看故障排除部分，或查看完整的用户指南。
