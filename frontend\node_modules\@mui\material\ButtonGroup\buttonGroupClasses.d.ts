export interface ButtonGroupClasses {
    /** Styles applied to the root element. */
    root: string;
    /** Styles applied to the root element if `variant="contained"`. */
    contained: string;
    /** Styles applied to the root element if `variant="outlined"`. */
    outlined: string;
    /** Styles applied to the root element if `variant="text"`. */
    text: string;
    /** Styles applied to the root element if `disableElevation={true}`. */
    disableElevation: string;
    /** State class applied to the child elements if `disabled={true}`. */
    disabled: string;
    /** Styles applied to the first button in the button group. */
    firstButton: string;
    /** Styles applied to the root element if `fullWidth={true}`. */
    fullWidth: string;
    /** Styles applied to the root element if `orientation="vertical"`. */
    vertical: string;
    /** Styles applied to the children. */
    grouped: string;
    /** Styles applied to the children if `orientation="horizontal"`. */
    groupedHorizontal: string;
    /** Styles applied to the children if `orientation="vertical"`. */
    groupedVertical: string;
    /** Styles applied to the children if `variant="text"`. */
    groupedText: string;
    /** Styles applied to the children if `variant="text"` and `orientation="horizontal"`. */
    groupedTextHorizontal: string;
    /** Styles applied to the children if `variant="text"` and `orientation="vertical"`. */
    groupedTextVertical: string;
    /** Styles applied to the children if `variant="text"` and `color="primary"`. */
    groupedTextPrimary: string;
    /** Styles applied to the children if `variant="text"` and `color="secondary"`. */
    groupedTextSecondary: string;
    /** Styles applied to the children if `variant="outlined"`. */
    groupedOutlined: string;
    /** Styles applied to the children if `variant="outlined"` and `orientation="horizontal"`. */
    groupedOutlinedHorizontal: string;
    /** Styles applied to the children if `variant="outlined"` and `orientation="vertical"`. */
    groupedOutlinedVertical: string;
    /** Styles applied to the children if `variant="outlined"` and `color="primary"`. */
    groupedOutlinedPrimary: string;
    /** Styles applied to the children if `variant="outlined"` and `color="secondary"`. */
    groupedOutlinedSecondary: string;
    /** Styles applied to the children if `variant="contained"`. */
    groupedContained: string;
    /** Styles applied to the children if `variant="contained"` and `orientation="horizontal"`. */
    groupedContainedHorizontal: string;
    /** Styles applied to the children if `variant="contained"` and `orientation="vertical"`. */
    groupedContainedVertical: string;
    /** Styles applied to the children if `variant="contained"` and `color="primary"`. */
    groupedContainedPrimary: string;
    /** Styles applied to the children if `variant="contained"` and `color="secondary"`. */
    groupedContainedSecondary: string;
    /** Styles applied to the last button in the button group. */
    lastButton: string;
    /** Styles applied to buttons in the middle of the button group. */
    middleButton: string;
}
export type ButtonGroupClassKey = keyof ButtonGroupClasses;
export declare function getButtonGroupUtilityClass(slot: string): string;
declare const buttonGroupClasses: ButtonGroupClasses;
export default buttonGroupClasses;
