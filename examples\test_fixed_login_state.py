"""
修复后的登录状态缓存测试

测试修复后的登录状态保存和恢复功能
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"✅ 加载环境变量: {key}")


async def test_manual_login_state_save():
    """测试手动登录状态保存"""
    print("🧪 测试手动登录状态保存")
    print("-" * 40)
    
    try:
        from login_state_manager import get_login_state_manager
        from playwright.async_api import async_playwright
        
        state_manager = get_login_state_manager()
        
        # 获取保存信息
        session_name = input("   会话名称: ").strip()
        if not session_name:
            session_name = "测试会话"
        
        description = input("   会话描述 (可选): ").strip()
        if not description:
            description = f"手动测试保存 - {session_name}"
        
        target_url = input("   登录页面URL (默认测试环境): ").strip()
        if not target_url:
            target_url = "https://test.yushanyun.net/ac/web/"
        
        print(f"   🌐 正在打开浏览器...")
        print(f"   📱 目标URL: {target_url}")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                # 导航到登录页面
                print(f"   📱 正在访问登录页面...")
                await page.goto(target_url)
                await page.wait_for_load_state('networkidle')
                
                print(f"   🔐 请在浏览器中完成登录")
                print(f"   💡 登录成功后，请确保页面已跳转到登录后的页面")
                input("   ✅ 登录完成后，按回车保存登录状态...")
                
                # 检查页面状态
                try:
                    current_url = page.url
                    print(f"   📍 当前页面URL: {current_url}")
                    
                    # 检查是否还在登录页面
                    if "login" in current_url.lower():
                        print("   ⚠️ 警告: 当前仍在登录页面，请确认登录是否成功")
                        proceed = input("   是否继续保存状态? (y/n): ").strip().lower()
                        if proceed not in ['y', 'yes']:
                            print("   ❌ 取消保存")
                            return False
                    
                except Exception as e:
                    print(f"   ❌ 无法获取页面状态: {e}")
                    return False
                
                # 保存登录状态
                print(f"   💾 正在保存登录状态...")
                session_id = await state_manager.save_login_session(
                    page,
                    session_name,
                    description
                )
                
                print(f"   ✅ 登录状态保存成功")
                print(f"      会话ID: {session_id}")
                print(f"      会话名称: {session_name}")
                print(f"      当前URL: {current_url}")
                
                return session_id
                
            except Exception as e:
                print(f"   ❌ 保存过程中出错: {e}")
                return False
                
            finally:
                # 询问是否关闭浏览器
                close_browser = input("   是否关闭浏览器? (y/n): ").strip().lower()
                if close_browser in ['y', 'yes']:
                    await browser.close()
                else:
                    print("   💡 浏览器保持打开，您可以继续操作")
                    input("   按回车关闭浏览器...")
                    await browser.close()
        
    except Exception as e:
        print(f"   ❌ 手动登录状态保存失败: {e}")
        return False


async def test_cached_login_restore(session_id: str = None):
    """测试缓存登录状态恢复"""
    print("\n🧪 测试缓存登录状态恢复")
    print("-" * 40)
    
    try:
        from ai_login_workflow_generator import get_login_state_executor
        
        executor = get_login_state_executor()
        
        # 如果没有提供session_id，让用户选择
        if not session_id:
            sessions = await executor.list_available_sessions()
            
            if not sessions:
                print("   📭 暂无可用的登录会话")
                return False
            
            valid_sessions = [s for s in sessions if s['is_valid']]
            
            if not valid_sessions:
                print("   ⚠️ 没有有效的登录会话")
                return False
            
            print("   📋 可用的登录会话:")
            for i, session in enumerate(valid_sessions, 1):
                print(f"      {i}. {session['name']} ({session['domain']})")
                print(f"         创建时间: {session['created_at']}")
                print(f"         使用次数: {session['use_count']}")
            
            choice = input(f"\n   选择要使用的会话 (1-{len(valid_sessions)}): ").strip()
            
            try:
                session_index = int(choice) - 1
                if 0 <= session_index < len(valid_sessions):
                    session_id = valid_sessions[session_index]['session_id']
                    selected_session = valid_sessions[session_index]
                    print(f"   ✅ 选择会话: {selected_session['name']}")
                else:
                    print("   ❌ 无效的选择")
                    return False
            except ValueError:
                print("   ❌ 无效的输入")
                return False
        
        # 询问目标URL
        target_url = input("   目标URL (可选，按回车跳过): ").strip()
        target_url = target_url if target_url else None
        
        print(f"   🔄 正在恢复登录状态...")
        
        # 执行缓存登录
        result = await executor.execute_with_cached_login(
            session_id=session_id,
            target_url=target_url,
            headless=False
        )
        
        if result['success']:
            print("   ✅ 缓存登录恢复成功")
            print(f"      会话名称: {result['session_name']}")
            print(f"      当前URL: {result['current_url']}")
            print(f"      执行时间: {result['execution_time']:.2f}秒")
            
            print(f"\n   🎉 浏览器已打开，登录状态已恢复！")
            print(f"   💡 您现在可以:")
            print(f"      • 直接在系统中操作")
            print(f"      • 无需重新登录")
            print(f"      • 享受快速访问")
            
            # 保持浏览器打开
            input("   👀 请查看浏览器中的登录状态，按回车关闭...")
            
            # 关闭浏览器
            try:
                await result['browser'].close()
            except Exception:
                pass
            
            return True
        else:
            print(f"   ❌ 缓存登录恢复失败: {result['error']}")
            return False
        
    except Exception as e:
        print(f"   ❌ 缓存登录恢复测试失败: {e}")
        return False


async def test_complete_workflow():
    """测试完整的工作流程"""
    print("\n🧪 测试完整的登录状态缓存工作流程")
    print("-" * 50)
    
    print("   📋 完整流程:")
    print("      1. 手动登录并保存状态")
    print("      2. 使用缓存状态快速恢复")
    print("      3. 验证登录状态有效性")
    
    # 步骤1: 手动登录并保存状态
    print(f"\n   🔐 步骤1: 手动登录并保存状态")
    session_id = await test_manual_login_state_save()
    
    if not session_id:
        print("   ❌ 步骤1失败，无法继续")
        return False
    
    print(f"   ✅ 步骤1完成，会话ID: {session_id}")
    
    # 等待一下
    print(f"\n   ⏳ 等待3秒后进行步骤2...")
    await asyncio.sleep(3)
    
    # 步骤2: 使用缓存状态快速恢复
    print(f"\n   🚀 步骤2: 使用缓存状态快速恢复")
    restore_success = await test_cached_login_restore(session_id)
    
    if restore_success:
        print(f"   ✅ 步骤2完成，缓存登录成功")
        print(f"\n   🎉 完整工作流程测试成功！")
        print(f"   💡 登录状态缓存机制工作正常")
        return True
    else:
        print(f"   ❌ 步骤2失败，缓存登录失败")
        return False


async def main():
    """主函数"""
    print("🎭 修复后的登录状态缓存测试")
    print("验证登录状态保存和恢复的修复效果")
    print("=" * 60)
    
    # 加载环境变量
    load_env()
    
    print("✅ 环境配置加载完成")
    
    try:
        print(f"\n🎯 选择测试类型:")
        print(f"   1. 手动登录状态保存测试")
        print(f"   2. 缓存登录状态恢复测试")
        print(f"   3. 完整工作流程测试")
        print(f"   4. 退出")
        
        choice = input(f"\n请选择 (1-4): ").strip()
        
        if choice == "1":
            await test_manual_login_state_save()
        elif choice == "2":
            await test_cached_login_restore()
        elif choice == "3":
            await test_complete_workflow()
        elif choice == "4":
            print("👋 退出测试")
            return
        else:
            print("❌ 无效的选择")
            return
        
        print(f"\n🎯 测试完成总结:")
        print(f"   🔧 登录状态保存机制已修复")
        print(f"   💾 支持手动和自动状态保存")
        print(f"   🚀 支持快速状态恢复")
        print(f"   ✅ 页面关闭问题已解决")
        
        print(f"\n🏆 修复效果:")
        print(f"   • 增强了页面状态检查")
        print(f"   • 改进了错误处理机制")
        print(f"   • 优化了状态捕获流程")
        print(f"   • 确保在正确时机保存状态")
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
