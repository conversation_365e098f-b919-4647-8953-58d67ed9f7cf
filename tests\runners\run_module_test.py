"""
使用 python -m 方式运行测试模块
"""
import sys
import os
import importlib
import unittest

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath('.'))

def run_module_test(module_name):
    """运行指定模块的测试"""
    try:
        # 导入测试模块
        module = importlib.import_module(module_name)
        
        # 创建测试套件
        test_suite = unittest.TestLoader().loadTestsFromModule(module)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(test_suite)
        
        # 输出测试结果
        print("\n测试结果:")
        print(f"运行测试数: {result.testsRun}")
        print(f"失败测试数: {len(result.failures)}")
        print(f"错误测试数: {len(result.errors)}")
        
        # 返回测试结果
        return result.wasSuccessful()
        
    except Exception as e:
        print(f"运行测试时出错: {str(e)}", file=sys.stderr)
        return False

if __name__ == "__main__":
    # 要运行的测试模块名
    test_module_name = "tests.test_operations"
    
    # 运行测试
    success = run_module_test(test_module_name)
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)
