"""
AI+RPA 交互模式启动器

快速启动图形化交互模式
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))


def load_env():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value


def check_requirements():
    """检查运行要求"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本需要3.8或更高")
        return False
    
    # 检查必要的包
    required_packages = ['tkinter', 'asyncio', 'playwright']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'asyncio':
                import asyncio
            elif package == 'playwright':
                import playwright
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要的包: {', '.join(missing_packages)}")
        return False
    
    # 检查AI配置
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        print("❌ 缺少GEMINI_API_KEY配置")
        print("💡 请在.env文件中配置您的Gemini API密钥")
        return False
    
    print("✅ 运行环境检查通过")
    return True


async def select_login_session():
    """选择登录会话"""
    try:
        from ai_login_workflow_generator import get_login_state_executor
        
        login_executor = get_login_state_executor()
        sessions = await login_executor.list_available_sessions()
        valid_sessions = [s for s in sessions if s["is_valid"]]
        
        if not valid_sessions:
            print("❌ 没有可用的登录会话")
            print("💡 请先运行登录测试创建登录会话:")
            print("   python examples/test_login_state_cache.py")
            return None
        
        print(f"\n📋 可用的登录会话:")
        for i, session in enumerate(valid_sessions, 1):
            print(f"   {i}. {session['name']}")
            print(f"      域名: {session['domain']}")
            print(f"      创建时间: {session['created_at']}")
            print(f"      使用次数: {session['use_count']}")
        
        while True:
            choice = input(f"\n请选择登录会话 (1-{len(valid_sessions)}, 或按回车使用第一个): ").strip()
            
            if not choice:
                return valid_sessions[0]["session_id"]
            
            try:
                session_index = int(choice) - 1
                if 0 <= session_index < len(valid_sessions):
                    return valid_sessions[session_index]["session_id"]
                else:
                    print("❌ 无效的选择，请重新输入")
            except ValueError:
                print("❌ 请输入数字")
        
    except Exception as e:
        print(f"❌ 获取登录会话失败: {e}")
        return None


async def start_interactive_mode():
    """启动交互模式"""
    try:
        print("🎭 AI+RPA 交互模式启动器")
        print("=" * 50)
        
        # 加载环境变量
        load_env()
        
        # 检查运行要求
        if not check_requirements():
            return
        
        # 选择登录会话
        login_session_id = await select_login_session()
        if not login_session_id:
            return
        
        # 获取场景名称
        scenario_name = input("\n场景名称 (默认: AI+RPA交互操作): ").strip()
        if not scenario_name:
            scenario_name = "AI+RPA交互操作"
        
        print(f"\n🚀 启动交互模式...")
        print(f"   场景名称: {scenario_name}")
        print(f"   登录会话: {login_session_id[:8]}...")
        print(f"\n💡 即将打开图形化聊天窗口，您可以:")
        print(f"   • 用自然语言描述需求进行操作")
        print(f"   • 使用命令进行页面分析和导航")
        print(f"   • 点击快捷按钮进行常用操作")
        print(f"   • 查看完整的操作记录")
        
        input("\n按回车键启动交互模式...")
        
        # 启动交互模式
        from interactive_mode_manager import get_interactive_mode_manager
        
        manager = get_interactive_mode_manager()
        await manager.start_interactive_mode(
            scenario_name=scenario_name,
            login_session_id=login_session_id
        )
        
    except KeyboardInterrupt:
        print("\n👋 用户取消启动")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print(f"💡 请检查配置和依赖是否正确安装")


def show_welcome():
    """显示欢迎信息"""
    print("""
🤖 AI+RPA 智能助手交互模式

功能特点:
• 🖥️ 图形化聊天界面，告别命令行
• 🗣️ 自然语言交互，智能理解需求
• 🤖 AI协助处理，提高命令识别准确率
• 📝 完整操作记录，支持流程复用
• ⚡ 实时交互反馈，提高操作效率

使用方式:
• 直接输入需求: "我要查看用户管理"
• 使用命令: "分析页面"、"系统状态"
• 点击快捷按钮进行常用操作

支持的操作:
• 智能页面导航和分析
• 操作记录和历史查看
• 系统状态查询和管理
• AI协助和使用指南

开始您的智能操作之旅吧！🚀
""")


async def main():
    """主函数"""
    show_welcome()
    await start_interactive_mode()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        input("按回车键退出...")
