# 工作流 DSL 设计文档

## 1. 概述

本文档定义了工作流引擎的领域特定语言(DSL)设计。DSL 用于以声明式的方式定义工作流，支持变量、条件分支、循环等高级特性。

## 2. 基本结构

```yaml
name: 工作流名称
version: 1.0.0
description: 工作流描述

# 环境变量
variables:
  base_url: https://example.com
  username: testuser
  password: testpass

# 步骤定义
steps:
  - id: step1
    type: navigate
    url: "{{base_url}}/login"
    description: 导航到登录页面
    
  - id: step2
    type: fill
    target:
      selector: "input#username"
      type: css
    value: "{{username}}"
    description: 输入用户名
    
  - id: step3
    type: fill
    target:
      selector: "input#password"
      type: css
    value: "{{password}}"
    description: 输入密码
    
  - id: step4
    type: click
    target:
      selector: "button#login"
      type: css
    description: 点击登录按钮
    
  - id: step5
    type: wait
    condition:
      type: selector_visible
      selector: "div.dashboard"
      timeout: 10000
    description: 等待仪表盘加载完成
```

## 3. 步骤类型

### 3.1 导航 (navigate)
导航到指定 URL。

```yaml
- id: navigate_to_home
  type: navigate
  url: "https://example.com"
  description: 导航到首页
```

### 3.2 点击 (click)
点击页面元素。

```yaml
- id: click_login
  type: click
  target:
    selector: "button#login"
    type: css
  description: 点击登录按钮
  wait:
    timeout: 5000
    condition:
      type: element_visible
      selector: "button#login"
```

### 3.3 输入 (fill)
在输入框中输入文本。

```yaml
- id: fill_username
  type: fill
  target:
    selector: "input#username"
    type: css
  value: "testuser"
  description: 输入用户名
```

### 3.4 等待 (wait)
等待特定条件满足。

```yaml
- id: wait_for_element
  type: wait
  condition:
    type: element_visible
    selector: "div.loading"
    timeout: 10000
  description: 等待加载完成
```

### 3.5 条件分支 (condition)
根据条件执行不同的步骤。

```yaml
- id: check_login
  type: condition
  condition: "{{is_logged_in}}"
  description: 检查是否已登录
  steps:
    - type: navigate
      url: "{{base_url}}/dashboard"
  else_steps:
    - type: navigate
      url: "{{base_url}}/login"
```

### 3.6 循环 (loop)
循环执行一组步骤。

```yaml
- id: process_items
  type: loop
  items: "{{items}}"
  item: "item"
  description: 处理项目列表
  steps:
    - type: click
      target:
        selector: ".item-{{item.id}}"
        type: css
    - type: wait
      condition:
        type: element_visible
        selector: ".item-detail"
```

## 4. 变量系统

### 4.1 变量定义

```yaml
variables:
  base_url: https://example.com
  credentials:
    username: testuser
    password: testpass
```

### 4.2 变量引用

```yaml
- type: fill
  target:
    selector: "input#username"
    type: css
  value: "{{credentials.username}}"
```

## 5. 错误处理

```yaml
- id: safe_operation
  type: try_catch
  description: 安全执行可能失败的操作
  try:
    - type: click
      target:
        selector: "button.submit"
        type: css
  catch:
    - type: log
      message: "提交按钮点击失败"
      level: warning
  finally:
    - type: log
      message: "操作执行完成"
```

## 6. 示例工作流

```yaml
name: 用户登录工作流
version: 1.0.0
description: 自动化用户登录流程

variables:
  base_url: https://example.com
  username: testuser
  password: testpass

steps:
  - id: navigate_to_login
    type: navigate
    url: "{{base_url}}/login"
    description: 导航到登录页面
    
  - id: fill_username
    type: fill
    target:
      selector: "input#username"
      type: css
    value: "{{username}}"
    description: 输入用户名
    
  - id: fill_password
    type: fill
    target:
      selector: "input#password"
      type: css
    value: "{{password}}"
    description: 输入密码
    
  - id: click_login
    type: click
    target:
      selector: "button#login"
      type: css
    description: 点击登录按钮
    
  - id: verify_login
    type: wait
    condition:
      type: url_contains
      value: "/dashboard"
      timeout: 5000
    description: 验证登录成功
```

## 7. 下一步计划

1. 实现 DSL 解析器
2. 实现工作流执行引擎
3. 添加变量作用域管理
4. 实现条件分支和循环控制流
5. 添加错误处理和重试机制
