# 环境配置指南

**版本**: v1.0 | **更新**: 2025-12-19

## 🎯 概述

本指南帮助您配置AI+RPA系统所需的完整环境，包括所有外部服务的API密钥和依赖项。

## 📋 环境要求检查清单

### ✅ 基础环境
- [ ] Python 3.8+ 已安装
- [ ] 虚拟环境已创建并激活
- [ ] 基础依赖已安装 (`pip install -r requirements.txt`)
- [ ] Playwright浏览器已安装 (`playwright install`)

### ✅ Node.js环境 (用于browser-tools-mcp)
- [ ] Node.js 16+ 已安装
- [ ] npm 已安装
- [ ] npx 命令可用

### ✅ API服务配置
- [ ] OpenAI API密钥已配置
- [ ] OCR服务已配置 (Google Vision/Azure CV/Tesseract)

## 🚀 详细配置步骤

### 1. 基础环境配置

#### 检查Python环境
```bash
# 检查Python版本
python --version

# 检查虚拟环境
python -c "import sys; print('虚拟环境:' if 'venv' in sys.prefix else '系统环境:', sys.prefix)"
```

#### 安装基础依赖
```bash
# 激活虚拟环境
.\.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt
pip install browser-use

# 安装浏览器
playwright install
```

### 2. Node.js环境配置

#### 安装Node.js
1. 访问 [Node.js官网](https://nodejs.org/) 下载并安装
2. 验证安装：
```bash
node --version
npm --version
npx --version
```

#### 安装browser-tools-mcp (可选)
```bash
# 全局安装
npm install -g @agentdeskai/browser-tools-mcp
npm install -g @agentdeskai/browser-tools-server

# 验证安装
npx @agentdeskai/browser-tools-server --version
```

### 3. OpenAI API配置

#### 获取API密钥
1. 访问 [OpenAI平台](https://platform.openai.com/)
2. 注册/登录账户
3. 创建API密钥

#### 配置API密钥
```bash
# 方法1: 环境变量 (推荐)
# Windows PowerShell
$env:OPENAI_API_KEY="your-api-key-here"

# Windows CMD
set OPENAI_API_KEY=your-api-key-here

# Linux/Mac
export OPENAI_API_KEY="your-api-key-here"

# 方法2: .env文件
echo "OPENAI_API_KEY=your-api-key-here" > .env
```

#### 验证API密钥
```bash
python -c "import os; print('API密钥已配置' if os.getenv('OPENAI_API_KEY') else '❌ API密钥未配置')"
```

### 4. OCR服务配置

#### 选项1: Google Vision API
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建项目并启用Vision API
3. 创建服务账户并下载JSON密钥文件
4. 配置环境变量：
```bash
# 设置认证文件路径
set GOOGLE_APPLICATION_CREDENTIALS=path\to\your\credentials.json
```

5. 安装依赖：
```bash
pip install google-cloud-vision
```

#### 选项2: Azure Computer Vision
1. 访问 [Azure门户](https://portal.azure.com/)
2. 创建Computer Vision资源
3. 获取密钥和端点
4. 配置环境变量：
```bash
set AZURE_COMPUTER_VISION_KEY=your-azure-key
set AZURE_COMPUTER_VISION_ENDPOINT=your-azure-endpoint
```

5. 安装依赖：
```bash
pip install azure-cognitiveservices-vision-computervision
```

#### 选项3: Tesseract OCR (本地)
1. 下载并安装 [Tesseract](https://github.com/tesseract-ocr/tesseract)
2. 安装Python包：
```bash
pip install pytesseract
```

3. 配置Tesseract路径 (如果需要)：
```python
import pytesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
```

## 🧪 环境验证

### 运行完整验收测试
```bash
# 激活虚拟环境
.\.venv\Scripts\activate

# 运行M5里程碑验收测试
python examples/m5_milestone_acceptance_test.py
```

### 预期结果
配置完成后，验收测试应该显示：
```
📈 验收统计:
   总测试数: 5
   通过数: 5
   失败数: 0
   通过率: 100.0%

🎯 M5里程碑验收结论:
   🎉 所有验收测试通过，M5里程碑验收成功！
```

## 🔧 故障排除

### 常见问题

#### Q: OpenAI API调用失败
**症状**: `AuthenticationError` 或 `RateLimitError`
**解决方案**:
1. 检查API密钥是否正确
2. 确认账户有足够余额
3. 检查API使用限制

#### Q: Node.js命令找不到
**症状**: `'node' is not recognized as an internal or external command`
**解决方案**:
1. 重新安装Node.js
2. 检查PATH环境变量
3. 重启命令行窗口

#### Q: browser-tools-mcp安装失败
**症状**: npm安装错误
**解决方案**:
1. 更新npm: `npm install -g npm@latest`
2. 清理缓存: `npm cache clean --force`
3. 使用管理员权限运行

#### Q: OCR服务不可用
**症状**: 所有OCR提供商都显示不可用
**解决方案**:
1. 至少配置一个OCR服务
2. 检查API密钥和认证文件
3. 验证网络连接

#### Q: Playwright浏览器启动失败
**症状**: 浏览器无法启动
**解决方案**:
```bash
# 重新安装浏览器
playwright install --force

# 安装系统依赖 (Linux)
playwright install-deps
```

### 环境检查脚本
创建 `check_environment.py` 文件：
```python
import os
import sys
import subprocess
import shutil

def check_environment():
    print("🔍 环境检查")
    print("=" * 40)
    
    # 检查Python
    print(f"Python版本: {sys.version.split()[0]}")
    
    # 检查虚拟环境
    venv_active = 'venv' in sys.prefix or '.venv' in sys.prefix
    print(f"虚拟环境: {'✅ 已激活' if venv_active else '❌ 未激活'}")
    
    # 检查Node.js
    node_available = shutil.which('node') is not None
    print(f"Node.js: {'✅ 可用' if node_available else '❌ 不可用'}")
    
    # 检查API密钥
    openai_key = os.getenv('OPENAI_API_KEY')
    print(f"OpenAI API: {'✅ 已配置' if openai_key else '❌ 未配置'}")
    
    # 检查OCR服务
    google_creds = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
    azure_key = os.getenv('AZURE_COMPUTER_VISION_KEY')
    tesseract_available = shutil.which('tesseract') is not None
    
    ocr_services = []
    if google_creds: ocr_services.append("Google Vision")
    if azure_key: ocr_services.append("Azure CV")
    if tesseract_available: ocr_services.append("Tesseract")
    
    print(f"OCR服务: {', '.join(ocr_services) if ocr_services else '❌ 未配置'}")
    
    print("\n📋 建议:")
    if not venv_active:
        print("  - 激活虚拟环境")
    if not node_available:
        print("  - 安装Node.js")
    if not openai_key:
        print("  - 配置OpenAI API密钥")
    if not ocr_services:
        print("  - 配置至少一个OCR服务")

if __name__ == "__main__":
    check_environment()
```

运行检查：
```bash
python check_environment.py
```

## 📚 相关文档

- [快速开始指南](./quickstart.md)
- [API文档](../../03-developer-guide/api/README.md)
- [部署指南](../../04-deployment/README.md)
- [故障排除](../troubleshooting.md)

## 🎯 下一步

环境配置完成后：
1. 运行验收测试确认所有功能正常
2. 查看[用户指南](../README.md)了解系统使用方法
3. 开始使用AI+RPA系统自动化您的工作流程

---

> 💡 **提示**: 如果遇到配置问题，请参考故障排除部分或查看详细的错误日志。
