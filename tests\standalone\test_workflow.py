"""
Simple test script to verify workflow engine functionality
"""
import asyncio
import logging
import sys
import os
import pytest
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = str(Path(__file__).parent.parent.parent)
sys.path.insert(0, project_root)

# Import workflow engine
try:
    from src.workflow.engine.workflow import Workflow, Node, Edge, NodeType
    from src.workflow.engine.executor import WorkflowExecutor
except ImportError as e:
    print(f"Error importing workflow engine: {e}")
    print("Please make sure you're running from the project root directory")
    sys.exit(1)

# Create a simple operation executor
class OperationExecutor:
    def __init__(self):
        self.operations = {}
        
    def register_operation(self, name, operation=None):
        if operation is None:
            def decorator(op):
                self.operations[name] = op
                return op
            return decorator
        self.operations[name] = operation
        
    async def execute_operation(self, operation_id, params=None):
        if operation_id not in self.operations:
            raise ValueError(f"Operation {operation_id} not found")
            
        operation = self.operations[operation_id]
        params = params or {}
        if callable(operation):
            return await operation(**params)
        elif hasattr(operation, 'execute') and callable(operation.execute):
            return await operation.execute(params)
        else:
            raise ValueError(f"Invalid operation: {operation_id}")

# Create a simple workflow
async def test_simple_workflow():
    print("\n" + "="*50)
    print("Testing simple workflow...")
    print("="*50 + "\n")
    
    # Create workflow
    workflow = Workflow(
        workflow_id="test_workflow",
        name="Test Workflow",
        description="A simple test workflow"
    )
    
    # Create nodes
    start_node = Node(
        id="start",
        type=NodeType.START,
        name="Start"
    )
    
    task_node = Node(
        id="task1",
        type=NodeType.TASK,
        name="Test Task",
        metadata={
            "operation_id": "log_message",
            "params": {
                "message": "Hello from workflow task!"
            }
        }
    )
    
    end_node = Node(
        id="end",
        type=NodeType.END,
        name="End"
    )
    
    # Add nodes to workflow
    workflow.add_node(start_node)
    workflow.add_node(task_node)
    workflow.add_node(end_node)
    
    # Add edges
    workflow.add_edge(Edge(source_id="start", target_id="task1"))
    workflow.add_edge(Edge(source_id="task1", target_id="end"))
    
    # Create operation executor
    operation_executor = OperationExecutor()
    
    # Register a simple log operation
    @operation_executor.register_operation("log_message")
    async def log_message_operation(message: str, **kwargs):
        """Simple log operation"""
        logger.info(f"[Operation] {message}")
        print(f"[Operation] {message}")
        return {"status": "success", "message": message}
    
    # Create workflow executor
    workflow_executor = WorkflowExecutor(operation_executor)
    
    # Validate workflow
    print("\nValidating workflow...")
    if not workflow.validate():
        print("❌ Workflow validation failed!")
        return
    print("✅ Workflow validation passed")
    
    # Execute workflow
    print("\nExecuting workflow...")
    result = await workflow_executor.execute(workflow)
    
    # Print results
    print("\nWorkflow execution completed!")
    print(f"Status: {result.status}")
    print(f"Duration: {result.end_time - result.start_time:.2f} seconds")
    
    # Print node results
    print("\nNode execution results:")
    for node_id, node_result in result.node_results.items():
        print(f"\nNode: {node_id} ({node_result.get('node_type', 'N/A')})")
        print(f"Status: {node_result.get('status', 'N/A')}")
        print(f"Duration: {node_result.get('duration', 0):.2f}s")
        if 'error' in node_result:
            print(f"Error: {node_result['error']}")

if __name__ == "__main__":
    asyncio.run(test_simple_workflow())
