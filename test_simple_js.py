"""
简单的JavaScript执行测试
"""
import os
import sys
import json
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入增强版工作流播放器
from enhanced_workflow_player import EnhancedWorkflowPlayer
from src.workflow.variables import VariableContext

def create_simple_js_workflow():
    """创建一个简单的JavaScript执行测试工作流"""
    return {
        "name": "简单JavaScript执行测试",
        "description": "测试JavaScript执行功能",
        "version": "1.0",
        "steps": [
            # 1. 导航到测试页面
            {
                "type": "navigate",
                "name": "打开测试页面",
                "url": "https://example.com"
            },
            
            # 2. 执行JavaScript获取页面信息
            {
                "type": "execute_script",
                "name": "获取页面标题",
                "script": "return document.title;",
                "script_type": "javascript",
                "output_variable": "page_title"
            },
            
            # 3. 记录获取的信息
            {
                "type": "log",
                "name": "记录页面标题",
                "message": "页面标题: {{ page_title }}",
                "level": "info"
            }
        ]
    }

def main():
    """主函数"""
    try:
        # 创建必要的目录
        os.makedirs('test_results', exist_ok=True)
        
        # 创建测试工作流
        workflow = create_simple_js_workflow()
        
        print("\n" + "=" * 50)
        print("开始执行简单JavaScript测试")
        print("=" * 50 + "\n")
        
        # 初始化变量上下文
        variable_context = VariableContext(initial_variables={
            'test_var': '测试变量'
        })
        
        # 创建增强版工作流播放器
        player = EnhancedWorkflowPlayer(variable_context=variable_context)
        
        # 执行工作流
        print(f"开始执行工作流: {workflow.get('name')}")
        result = player.play(workflow=workflow)
        
        # 输出执行结果
        print(f"\n工作流执行完成，状态: {result.status}")
        print(f"执行时间: {result.duration:.2f} 秒\n")
        
        # 输出最终变量
        print("最终变量状态:")
        for var_name, var_value in result.variables.items():
            if not var_name.startswith('_') and var_name not in ['workflow', 'execution']:
                print(f"  {var_name} = {var_value}")
        
        # 输出步骤执行结果
        print("\n步骤执行结果:")
        for i, step_result in enumerate(result.step_results):
            status_emoji = "✅" if step_result.status == "passed" else "❌" if step_result.status == "failed" else "⏩"
            print(f"  {i+1}. {status_emoji} [{step_result.step_type}] {step_result.step_name}: {step_result.status} ({step_result.duration:.2f}秒)")
            if step_result.error:
                print(f"     错误: {step_result.error}")
        
        # 保存测试结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f'test_results/simple_js_test_{timestamp}.txt'
        
        with open(result_file, 'w', encoding='utf-8') as f:
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"工作流名称: {workflow.get('name')}\n")
            f.write(f"执行状态: {result.status}\n")
            f.write(f"执行时间: {result.duration:.2f} 秒\n\n")
            
            f.write("变量状态:\n")
            for var_name, var_value in result.variables.items():
                if not var_name.startswith('_') and var_name not in ['workflow', 'execution']:
                    f.write(f"  {var_name} = {var_value}\n")
            
            f.write("\n步骤执行结果:\n")
            for i, step_result in enumerate(result.step_results):
                status_emoji = "✅" if step_result.status == "passed" else "❌" if step_result.status == "failed" else "⏩"
                f.write(f"  {i+1}. {status_emoji} [{step_result.step_type}] {step_result.step_name}: {step_result.status} ({step_result.duration:.2f}秒)\n")
                if step_result.error:
                    f.write(f"     错误: {step_result.error}\n")
            
            f.write("\n功能验证结果:\n")
            
            # 验证JavaScript执行
            if 'page_title' in result.variables and result.variables['page_title']:
                f.write(f"✅ JavaScript执行功能正常: 页面标题={result.variables['page_title']}\n")
            else:
                f.write("❌ JavaScript执行功能异常\n")
        
        print(f"\n测试结果已保存到: {result_file}")
        print("\n" + "=" * 50)
        print("测试完成")
        print("=" * 50 + "\n")
        
        return 0
    
    except Exception as e:
        print(f"\n工作流执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 保存错误信息到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        error_file = f'test_results/simple_js_error_{timestamp}.txt'
        
        with open(error_file, 'w', encoding='utf-8') as f:
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"错误信息: {str(e)}\n\n")
            f.write("详细堆栈:\n")
            traceback.print_exc(file=f)
        
        print(f"\n错误信息已保存到: {error_file}")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
