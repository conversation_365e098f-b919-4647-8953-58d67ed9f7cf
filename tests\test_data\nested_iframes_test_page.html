<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nested Iframes Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .frame-container {
            position: relative;
            width: 100%;
            height: 500px;
            border: 2px solid #4CAF50;
            margin: 20px 0;
            padding: 10px;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: 1px solid #ccc;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            padding: 8px 16px;
            margin-right: 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f8f8;
            border-left: 4px solid #4CAF50;
        }
    </style>
</head>
<body>
    <h1>Nested Iframes Test Page</h1>
    
    <div class="container">
        <h2>Outer Frame</h2>
        <p>This page contains an iframe with another iframe inside it.</p>
        
        <div class="frame-container">
            <iframe id="outer-iframe" src="outer_frame.html"></iframe>
        </div>
        
        <div class="controls">
            <button id="check-status">Check Status</button>
        </div>
        
        <div id="status" class="status">Ready</div>
    </div>

    <script>
        document.getElementById('check-status').addEventListener('click', function() {
            const statusDiv = document.getElementById('status');
            try {
                const outerFrame = document.getElementById('outer-iframe');
                const outerDoc = outerFrame.contentDocument || outerFrame.contentWindow.document;
                const innerFrame = outerDoc.getElementById('inner-iframe');
                const innerDoc = innerFrame.contentDocument || innerFrame.contentWindow.document;
                
                const searchValue = innerDoc.getElementById('search').value;
                const searchButton = innerDoc.getElementById('search-button');
                
                statusDiv.innerHTML = `
                    <strong>Status:</strong> Frames loaded successfully<br>
                    <strong>Search value:</strong> ${searchValue || '(empty)'}
                `;
                
                console.log('Nested iframes status checked');
            } catch (e) {
                statusDiv.innerHTML = `<strong>Error:</strong> ${e.message}`;
                console.error('Error checking iframes:', e);
            }
        });
    </script>
</body>
</html>
