# AI+RPA 智能工作流自动化系统

基于 Playwright + AI 的智能工作流自动化系统，结合 browser-use 实现智能监控和自动修正。

## 🎯 项目特点

- **智能录制**: 基于 Playwright Codegen 实现工作流录制，自动生成标准化 JSON 工作流定义
- **AI 监控**: 集成 browser-use 实现实时监控，自动检测和修正异常
- **流程重组**: 智能拆解录制记录，通过 AI 生成新的工作流组合
- **自动执行**: 结合 browser-use 实现工作流的智能化自动执行

## 📚 文档目录

```
docs/
├── 01-project-overview/           # 项目概述
│   ├── introduction.md           # 项目介绍
│   ├── features.md              # 功能特性
│   └── architecture.md          # 系统架构
│
├── 02-getting-started/           # 快速入门
│   ├── installation.md          # 安装指南
│   ├── configuration.md         # 配置说明
│   └── quickstart.md           # 快速开始
│
├── 03-user-guide/               # 用户指南
│   ├── workflow-recording/      # 工作流录制
│   ├── workflow-execution/      # 工作流执行
│   └── browser-monitoring/      # 浏览器监控
│
├── 04-developer-guide/          # 开发指南
│   ├── codebase-structure/      # 代码结构
│   ├── api-reference/          # API 文档
│   └── contribution/           # 贡献指南
│
├── 05-integration/              # 集成指南
│   ├── browser-use/            # browser-use 集成
│   └── playwright/             # Playwright 集成
│
└── 06-advanced/                 # 高级主题
    ├── ai-features/            # AI 功能
    ├── custom-workflows/       # 自定义工作流
    └── troubleshooting/        # 故障排除
```

## 🚀 快速开始

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **配置环境**
```bash
# 设置 OpenAI API 密钥
export OPENAI_API_KEY=your_key_here
```

3. **运行录制器**
```bash
python -m src.recorder -u https://your-target-url.com --viewport 1280,720
```

## 📖 核心文档

- [项目概述](docs/01-project-overview/introduction.md)
- [快速入门](docs/02-getting-started/quickstart.md)
- [工作流录制指南](docs/03-user-guide/workflow-recording/README.md)
- [开发者指南](docs/04-developer-guide/README.md)
- [browser-use 集成](docs/05-integration/browser-use/README.md)

## 🤝 贡献

欢迎贡献代码和文档！请查看我们的[贡献指南](docs/04-developer-guide/contribution/README.md)。

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。
