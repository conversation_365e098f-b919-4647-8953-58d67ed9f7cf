# AI模型开发与训练指南

**最后更新**：2025-05-30

## 目录

1. [概述](#概述)
2. [环境准备](#环境准备)
3. [数据准备](#数据准备)
4. [模型训练](#模型训练)
5. [模型评估](#模型评估)
6. [模型部署](#模型部署)
7. [模型监控与维护](#模型监控与维护)
8. [最佳实践](#最佳实践)
9. [故障排除](#故障排除)
10. [参考资源](#参考资源)

## 概述

本文档提供了在Playwright自动化框架中开发和训练AI模型的完整指南。我们的AI模型主要用于智能元素定位、测试用例生成和自愈测试等场景。

### 主要功能

- 智能元素定位
- 测试用例生成
- 自愈测试
- 异常检测
- 测试优化建议

## 环境准备

### 1. 硬件要求

| 组件 | 最低配置 | 推荐配置 |
|------|---------|---------|
| GPU  | NVIDIA GTX 1060 6GB | NVIDIA RTX 3080 10GB+ |
| 内存 | 16GB    | 32GB+   |
| 存储 | 100GB   | 1TB SSD |

### 2. 软件依赖

```bash
# 创建并激活conda环境
conda create -n playwright-ai python=3.9
conda activate playwright-ai

# 安装PyTorch (根据CUDA版本选择)
conda install pytorch torchvision torchaudio cudatoolkit=11.3 -c pytorch

# 安装其他依赖
pip install -r requirements-ai.txt
```

### 3. 项目结构

```
ai/
├── data/                  # 训练数据
├── models/                # 模型定义
├── notebooks/             # Jupyter notebooks
├── scripts/               # 训练和评估脚本
├── tests/                 # 单元测试
├── utils/                 # 工具函数
├── config.py              # 配置文件
└── requirements-ai.txt    # AI相关依赖
```

## 数据准备

### 1. 数据收集

```python
from playwright.sync_api import sync_playwright
import json

def collect_training_data():
    data = []
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        
        # 导航到目标页面
        page.goto("https://example.com")
        
        # 收集页面元素信息
        elements = page.query_selector_all("*")
        for element in elements:
            try:
                # 获取元素属性
                tag = await element.evaluate('el => el.tagName')
                text = await element.evaluate('el => el.innerText')
                attrs = await element.evaluate('el => el.attributes')
                
                # 保存数据
                data.append({
                    'tag': tag,
                    'text': text,
                    'attributes': attrs,
                    'screenshot': await element.screenshot()
                })
            except:
                continue
        
        browser.close()
    
    # 保存数据
    with open('data/training_data.json', 'w') as f:
        json.dump(data, f)
```

### 2. 数据预处理

```python
import pandas as pd
from sklearn.model_selection import train_test_split

def preprocess_data(data_path):
    # 加载数据
    df = pd.read_json(data_path)
    
    # 特征工程
    df['has_text'] = df['text'].apply(lambda x: 1 if x and len(x.strip()) > 0 else 0)
    df['class_attr'] = df['attributes'].apply(lambda x: x.get('class', ''))
    
    # 分割数据集
    train_df, test_df = train_test_split(df, test_size=0.2, random_state=42)
    
    return train_df, test_df
```

## 模型训练

### 1. 模型定义

```python
import torch
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer

class ElementClassifier(nn.Module):
    def __init__(self, model_name='bert-base-uncased', num_classes=10):
        super(ElementClassifier, self).__init__()
        self.bert = AutoModel.from_pretrained(model_name)
        self.dropout = nn.Dropout(0.1)
        self.classifier = nn.Linear(self.bert.config.hidden_size, num_classes)
    
    def forward(self, input_ids, attention_mask):
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = outputs.last_hidden_state[:, 0, :]
        pooled_output = self.dropout(pooled_output)
        return self.classifier(pooled_output)
```

### 2. 训练循环

```python
from torch.utils.data import Dataset, DataLoader
from transformers import AdamW, get_linear_schedule_with_warmup

def train_model(model, train_loader, val_loader, num_epochs=5):
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    optimizer = AdamW(model.parameters(), lr=2e-5)
    scheduler = get_linear_schedule_with_warmup(
        optimizer, 
        num_warmup_steps=0,
        num_training_steps=len(train_loader) * num_epochs
    )
    
    criterion = nn.CrossEntropyLoss()
    
    for epoch in range(num_epochs):
        model.train()
        total_loss = 0
        
        for batch in train_loader:
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            optimizer.zero_grad()
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            loss = criterion(outputs, labels)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            scheduler.step()
            
            total_loss += loss.item()
        
        avg_train_loss = total_loss / len(train_loader)
        val_metrics = evaluate_model(model, val_loader)
        
        print(f"Epoch {epoch+1}/{num_epochs}")
        print(f"Train Loss: {avg_train_loss:.4f}")
        print(f"Val Accuracy: {val_metrics['accuracy']:.4f}")
        print("-" * 50)
    
    return model
```

## 模型评估

### 1. 评估指标

```python
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

def compute_metrics(preds, labels):
    precision, recall, f1, _ = precision_recall_fscore_support(
        labels, preds, average='weighted')
    acc = accuracy_score(labels, preds)
    return {
        'accuracy': acc,
        'f1': f1,
        'precision': precision,
        'recall': recall
    }

def evaluate_model(model, dataloader):
    model.eval()
    device = next(model.parameters()).device
    
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch in dataloader:
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            preds = torch.argmax(outputs, dim=1)
            
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    return compute_metrics(all_preds, all_labels)
```

## 模型部署

### 1. 导出模型

```python
def export_model(model, output_dir):
    # 保存模型权重
    torch.save(model.state_dict(), f"{output_dir}/model_weights.pth")
    
    # 保存模型架构
    model_config = {
        'model_name': 'bert-base-uncased',
        'num_classes': model.classifier.out_features
    }
    
    import json
    with open(f"{output_dir}/config.json", 'w') as f:
        json.dump(model_config, f)
    
    # 保存tokenizer
    tokenizer = AutoTokenizer.from_pretrained('bert-base-uncased')
    tokenizer.save_pretrained(output_dir)
```

### 2. 创建推理服务

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import torch
from transformers import AutoTokenizer

app = FastAPI()

class PredictionRequest(BaseModel):
    text: str

class ElementClassifier:
    def __init__(self, model_path):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = self._load_model(model_path)
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    def _load_model(self, model_path):
        with open(f"{model_path}/config.json") as f:
            config = json.load(f)
        
        model = ElementClassifier(
            model_name=config['model_name'],
            num_classes=config['num_classes']
        )
        model.load_state_dict(torch.load(f"{model_path}/model_weights.pth", map_location=self.device))
        model.eval()
        return model
    
    def predict(self, text):
        inputs = self.tokenizer(
            text,
            padding='max_length',
            truncation=True,
            max_length=128,
            return_tensors='pt'
        )
        
        with torch.no_grad():
            outputs = self.model(
                input_ids=inputs['input_ids'].to(self.device),
                attention_mask=inputs['attention_mask'].to(self.device)
            )
            probs = torch.softmax(outputs, dim=1)
            pred = torch.argmax(probs, dim=1)
            
        return {
            'prediction': int(pred.item()),
            'confidence': float(torch.max(probs).item())
        }

# 加载模型
model = ElementClassifier('models/element_classifier')

@app.post("/predict")
async def predict(request: PredictionRequest):
    try:
        result = model.predict(request.text)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

## 模型监控与维护

### 1. 监控指标

- 预测延迟
- 内存使用
- GPU利用率
- 请求量
- 错误率

### 2. 数据漂移检测

```python
import numpy as np
from scipy import stats

def detect_data_drift(reference_data, current_data, feature_columns, threshold=0.05):
    drift_detected = {}
    
    for col in feature_columns:
        # 计算KS检验
        ks_stat, p_value = stats.ks_2samp(
            reference_data[col].dropna(),
            current_data[col].dropna()
        )
        
        # 如果p值小于阈值，认为存在数据漂移
        drift_detected[col] = {
            'ks_statistic': ks_stat,
            'p_value': p_value,
            'drift_detected': p_value < threshold
        }
    
    return drift_detected
```

## 最佳实践

### 1. 数据收集

- 收集多样化的数据
- 定期更新训练数据
- 平衡类别分布
- 数据增强

### 2. 模型训练

- 使用预训练模型
- 学习率调度
- 早停机制
- 模型检查点

### 3. 部署

- A/B测试
- 金丝雀发布
- 流量控制
- 回滚策略

## 故障排除

### 1. 常见问题

**问题**: 内存不足

**解决方案**:
- 减小批次大小
- 使用梯度累积
- 使用混合精度训练

**问题**: 训练不稳定

**解决方案**:
- 调整学习率
- 使用学习率预热
- 添加梯度裁剪

## 参考资源

- [Hugging Face Transformers](https://huggingface.co/transformers/)
- [PyTorch 文档](https://pytorch.org/docs/stable/index.html)
- [Scikit-learn 文档](https://scikit-learn.org/stable/documentation.html)
- [FastAPI 文档](https://fastapi.tiangolo.com/)
